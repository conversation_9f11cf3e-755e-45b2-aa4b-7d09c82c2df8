{% extends "base.html" %}

{% block content %}
<div id="main-content" class="page-transition">
<!-- 现代化轮播图 -->
<div id="heroCarousel" class="carousel slide hero-carousel mb-5" data-bs-ride="carousel" data-bs-interval="5000">
    <div class="carousel-indicators">
        <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"
                aria-current="true" aria-label="第1张幻灯片"></button>
        <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"
                aria-label="第2张幻灯片"></button>
        <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"
                aria-label="第3张幻灯片"></button>
    </div>
    <div class="carousel-inner">
        <div class="carousel-item active">
            <div class="carousel-slide-1 text-white carousel-content">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-6 col-md-7">
                            <h1 class="carousel-title">欢迎来到二手交易平台</h1>
                            <p class="carousel-subtitle">安全、便捷的二手商品交易体验，让闲置物品重新焕发价值</p>
                            <div class="d-flex flex-wrap gap-3">
                                <a href="{{ url_for('main.search') }}" class="carousel-btn">
                                    <i class="fas fa-search me-2"></i>开始购物
                                </a>
                                {% if not current_user.is_authenticated %}
                                <a href="{{ url_for('auth.register') }}" class="carousel-btn" style="background: rgba(255,255,255,0.1);">
                                    <i class="fas fa-user-plus me-2"></i>立即注册
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-5 text-center d-none d-md-block">
                            <i class="fas fa-store carousel-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="carousel-item">
            <div class="carousel-slide-2 text-white carousel-content">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-6 col-md-7">
                            <h1 class="carousel-title">发布您的商品</h1>
                            <p class="carousel-subtitle">轻松发布，快速出售，让您的闲置物品找到新主人</p>
                            <div class="d-flex flex-wrap gap-3">
                                {% if current_user.is_authenticated %}
                                <a href="{{ url_for('products.publish') }}" class="carousel-btn">
                                    <i class="fas fa-plus-circle me-2"></i>立即发布
                                </a>
                                <a href="{{ url_for('products.my_products') }}" class="carousel-btn" style="background: rgba(255,255,255,0.1);">
                                    <i class="fas fa-box me-2"></i>我的商品
                                </a>
                                {% else %}
                                <a href="{{ url_for('auth.register') }}" class="carousel-btn">
                                    <i class="fas fa-user-plus me-2"></i>注册发布
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-5 text-center d-none d-md-block">
                            <i class="fas fa-plus-circle carousel-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="carousel-item">
            <div class="carousel-slide-3 text-white carousel-content">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-6 col-md-7">
                            <h1 class="carousel-title">安全交易保障</h1>
                            <p class="carousel-subtitle">平台担保，交易无忧，为您提供安全可靠的交易环境</p>
                            <div class="d-flex flex-wrap gap-3">
                                <a href="{{ url_for('main.help') }}" class="carousel-btn">
                                    <i class="fas fa-info-circle me-2"></i>了解更多
                                </a>
                                <a href="{{ url_for('main.contact') }}" class="carousel-btn" style="background: rgba(255,255,255,0.1);">
                                    <i class="fas fa-envelope me-2"></i>联系我们
                                </a>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-5 text-center d-none d-md-block">
                            <i class="fas fa-shield-alt carousel-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev" aria-label="上一张">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
    </button>
    <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next" aria-label="下一张">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
    </button>
</div>

<!-- 商品分类 -->
{% if categories %}
<section class="mb-5" aria-labelledby="categories-heading">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 id="categories-heading" class="mb-0">
            <i class="fas fa-th-large text-primary me-2"></i>商品分类
        </h2>
        <a href="{{ url_for('main.search') }}" class="btn btn-outline-primary btn-sm">
            <i class="fas fa-eye me-1"></i>查看全部
        </a>
    </div>
    <div class="row g-4">
        {% for category in categories %}
        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-3">
            <a href="{{ url_for('main.search', category=category.id) }}" class="text-decoration-none">
                <div class="card h-100 category-card card-animate" style="transition-delay: {{ loop.index0 * 0.1 }}s;">
                    <div class="card-body">
                        {% if category.icon %}
                        <i class="{{ category.icon }} fa-3x mb-3" aria-hidden="true"></i>
                        {% else %}
                        <i class="fas fa-tag fa-3x mb-3" aria-hidden="true"></i>
                        {% endif %}
                        <h5 class="card-title">{{ category.name }}</h5>
                        <p class="card-text text-muted">{{ category.description or '查看该分类下的商品' }}</p>
                        <div class="mt-auto">
                            <small class="text-primary">
                                <i class="fas fa-arrow-right me-1"></i>浏览商品
                            </small>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}

<!-- 推荐商品 -->
{% if featured_products %}
<section class="mb-5" aria-labelledby="featured-heading">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 id="featured-heading" class="mb-0">
            <i class="fas fa-fire text-danger me-2"></i>热门商品
        </h2>
        <a href="{{ url_for('main.search', sort='popular') }}" class="btn btn-outline-danger btn-sm">
            <i class="fas fa-chart-line me-1"></i>查看更多
        </a>
    </div>
    <div class="row g-4">
        {% for product in featured_products %}
        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-4">
            <article class="card h-100 product-card card-animate" style="transition-delay: {{ loop.index0 * 0.1 }}s;">
                <div class="position-relative">
                    <a href="{{ url_for('products.detail', id=product.id) }}" aria-label="查看商品详情">
                        <img src="{{ product.get_main_image_url() }}"
                             class="card-img-top" alt="{{ product.title }}" loading="lazy">
                    </a>
                    <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                        <i class="fas fa-fire me-1"></i>热门
                    </span>
                </div>
                <div class="card-body d-flex flex-column">
                    <h6 class="card-title">
                        <a href="{{ url_for('products.detail', id=product.id) }}" class="text-decoration-none">
                            {{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}
                        </a>
                    </h6>
                    <p class="card-text text-muted small flex-grow-1">
                        {{ product.description[:80] }}{% if product.description|length > 80 %}...{% endif %}
                    </p>

                    <!-- 商品信息 -->
                    <div class="product-info-list-grid mb-2">
                        <div class="info-item-grid">
                            <span>{{ product.category.name }}</span>
                        </div>
                        <div class="info-item-grid">
                            <span>{{ product.condition }}</span>
                        </div>
                    </div>

                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="price">¥{{ "%.2f"|format(product.price) }}</span>
                            <div class="d-flex gap-2 text-muted small">
                                <span><i class="fas fa-eye"></i> {{ product.view_count }}</span>
                                <span><i class="fas fa-heart"></i> {{ product.favorite_count }}</span>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>{{ product.seller.nickname or product.seller.username }}
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ product.location or '未知' }}
                            </small>
                        </div>
                    </div>
                </div>
            </article>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}

<!-- 最新商品 -->
<section class="mb-5" aria-labelledby="latest-heading">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 id="latest-heading" class="mb-0">
            <i class="fas fa-clock text-success me-2"></i>最新商品
        </h2>
        <a href="{{ url_for('main.search', sort='newest') }}" class="btn btn-outline-success btn-sm">
            <i class="fas fa-plus me-1"></i>查看更多
        </a>
    </div>

    {% if products.items %}
    <div class="row g-4">
        {% for product in products.items %}
        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-4">
            <article class="card h-100 product-card card-animate" style="transition-delay: {{ loop.index0 * 0.1 }}s;">
                <div class="position-relative">
                    <a href="{{ url_for('products.detail', id=product.id) }}" aria-label="查看商品详情">
                        <img src="{{ product.get_main_image_url() }}"
                             class="card-img-top" alt="{{ product.title }}" loading="lazy">
                    </a>
                    <span class="badge bg-success position-absolute top-0 end-0 m-2">
                        <i class="fas fa-plus me-1"></i>新品
                    </span>
                </div>
                <div class="card-body d-flex flex-column">
                    <h6 class="card-title">
                        <a href="{{ url_for('products.detail', id=product.id) }}" class="text-decoration-none">
                            {{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}
                        </a>
                    </h6>
                    <p class="card-text text-muted small flex-grow-1">
                        {{ product.description[:80] }}{% if product.description|length > 80 %}...{% endif %}
                    </p>

                    <!-- 商品信息列表 -->
                    <div class="product-info-list-grid mb-2">
                        <div class="info-item-grid">
                            <small>分类:</small>
                            <span>{{ product.category.name }}</span>
                        </div>
                        <div class="info-item-grid">
                            <small>成色:</small>
                            <span>{{ product.condition }}</span>
                        </div>
                        <div class="info-item-grid">
                            <small>卖家:</small>
                            <span>{{ product.seller.nickname or product.seller.username }}</span>
                        </div>
                        <div class="info-item-grid">
                            <small>地址:</small>
                            <span>{{ product.location or '未知' }}</span>
                        </div>
                        <div class="info-item-grid">
                            <small>浏览:</small>
                            <span>{{ product.view_count }}</span>
                        </div>
                        <div class="info-item-grid">
                            <small>收藏:</small>
                            <span>{{ product.favorite_count }}</span>
                        </div>
                    </div>

                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="price">¥{{ "%.2f"|format(product.price) }}</span>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <time datetime="{{ product.created_at.isoformat() }}">
                                    {{ product.created_at.strftime('%m-%d %H:%M') }}
                                </time>
                            </small>
                        </div>
                    </div>
                </div>
            </article>
        </div>
        {% endfor %}
    </div>

    <!-- 现代化分页 -->
    {% if products.pages > 1 %}
    <nav aria-label="商品分页" class="mt-5">
        <ul class="pagination justify-content-center">
            {% if products.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.index', page=products.prev_num) }}"
                   aria-label="上一页">
                    <i class="fas fa-chevron-left me-1"></i>上一页
                </a>
            </li>
            {% endif %}

            {% for page_num in products.iter_pages() %}
                {% if page_num %}
                    {% if page_num != products.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('main.index', page=page_num) }}"
                           aria-label="第{{ page_num }}页">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active" aria-current="page">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if products.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.index', page=products.next_num) }}"
                   aria-label="下一页">
                    下一页<i class="fas fa-chevron-right ms-1"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    {% else %}
    <!-- 空状态 -->
    <div class="empty-state text-center py-5">
        <div class="mb-4">
            <i class="fas fa-box-open fa-5x text-muted mb-3" aria-hidden="true"></i>
            <h4 class="text-muted">暂无商品</h4>
            <p class="text-muted">快来发布第一个商品，开启您的二手交易之旅吧！</p>
        </div>
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('products.publish') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus-circle me-2"></i>发布商品
            </a>
            <a href="{{ url_for('main.search') }}" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-search me-2"></i>浏览商品
            </a>
            {% else %}
            <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-user-plus me-2"></i>注册发布
            </a>
            <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-sign-in-alt me-2"></i>立即登录
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</section>
</div>

<!-- 页面底部统计信息 -->
<section class="bg-light py-4 mt-5" aria-labelledby="stats-heading">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-item">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h5 class="mb-1">活跃用户</h5>
                    <p class="text-muted mb-0">{{ "{:,}".format(stats.active_users) }}+</p>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-item">
                    <i class="fas fa-box fa-2x text-success mb-2"></i>
                    <h5 class="mb-1">在售商品</h5>
                    <p class="text-muted mb-0">{{ "{:,}".format(stats.active_products) }}+</p>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-item">
                    <i class="fas fa-handshake fa-2x text-warning mb-2"></i>
                    <h5 class="mb-1">成功交易</h5>
                    <p class="text-muted mb-0">{{ "{:,}".format(stats.completed_orders) }}+</p>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-item">
                    <i class="fas fa-star fa-2x text-danger mb-2"></i>
                    <h5 class="mb-1">用户评价</h5>
                    <p class="text-muted mb-0">{{ "{:,}".format(stats.total_reviews) }}+</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
