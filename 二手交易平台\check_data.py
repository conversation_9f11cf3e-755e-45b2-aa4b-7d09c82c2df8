#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库数据完整性
"""

import sqlite3
import os

def check_database_data():
    """检查数据库数据"""
    db_path = 'app/app.db'
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 数据库数据检查 ===\n")
        
        # 检查分类数据
        cursor.execute("SELECT COUNT(*) FROM categories")
        category_count = cursor.fetchone()[0]
        print(f"📁 分类数量: {category_count}")
        
        if category_count > 0:
            cursor.execute("SELECT id, name, description, icon, color FROM categories ORDER BY sort_order")
            categories = cursor.fetchall()
            print("分类列表:")
            for cat in categories:
                print(f"  - ID:{cat[0]} 名称:{cat[1]} 图标:{cat[3]} 颜色:{cat[4]}")
        
        print()
        
        # 检查商品数据
        cursor.execute("SELECT COUNT(*) FROM products")
        product_count = cursor.fetchone()[0]
        print(f"🛍️ 商品数量: {product_count}")
        
        if product_count > 0:
            cursor.execute("""
                SELECT p.id, p.title, p.price, p.status, c.name as category_name, p.created_at 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                ORDER BY p.created_at DESC LIMIT 10
            """)
            products = cursor.fetchall()
            print("最新商品列表:")
            for prod in products:
                print(f"  - ID:{prod[0]} 标题:{prod[1]} 价格:¥{prod[2]} 状态:{prod[3]} 分类:{prod[4]} 时间:{prod[5]}")
        
        print()
        
        # 检查用户数据
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"👤 用户数量: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT id, username, email, nickname, role FROM users")
            users = cursor.fetchall()
            print("用户列表:")
            for user in users:
                print(f"  - ID:{user[0]} 用户名:{user[1]} 邮箱:{user[2]} 昵称:{user[3]} 角色:{user[4]}")
        
        print()
        
        # 检查商品图片数据
        cursor.execute("SELECT COUNT(*) FROM product_images")
        image_count = cursor.fetchone()[0]
        print(f"🖼️ 商品图片数量: {image_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

def init_sample_data():
    """初始化示例数据"""
    print("\n=== 初始化示例数据 ===")
    
    db_path = 'app/app.db'
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已有分类数据
        cursor.execute("SELECT COUNT(*) FROM categories")
        if cursor.fetchone()[0] == 0:
            print("📁 添加分类数据...")
            categories_data = [
                ('电子产品', '手机、电脑、数码相机等电子设备', 'fas fa-laptop', '#3498db', True, 1),
                ('服装鞋帽', '各类服装、鞋子、帽子等', 'fas fa-tshirt', '#e74c3c', True, 2),
                ('图书文具', '教材、小说、文具用品等', 'fas fa-book', '#f39c12', True, 3),
                ('家居用品', '家具、装饰品、生活用品等', 'fas fa-home', '#2ecc71', True, 4),
                ('运动户外', '运动器材、户外用品等', 'fas fa-running', '#9b59b6', True, 5),
                ('美妆护肤', '化妆品、护肤品等', 'fas fa-heart', '#e91e63', True, 6),
                ('母婴用品', '婴儿用品、玩具等', 'fas fa-baby', '#ff9800', True, 7),
                ('其他', '其他类别商品', 'fas fa-ellipsis-h', '#95a5a6', True, 8),
            ]
            
            for category_data in categories_data:
                cursor.execute('''
                INSERT INTO categories (name, description, icon, color, is_active, sort_order, created_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
                ''', category_data)
            
            print("✅ 分类数据添加完成")
        
        # 检查是否已有用户数据
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        if cursor.fetchone()[0] == 0:
            print("👤 添加管理员用户...")
            from werkzeug.security import generate_password_hash
            
            admin_data = (
                'admin', '<EMAIL>', generate_password_hash('admin123'), 
                '管理员', None, None, '系统管理员', '系统', True, 'admin'
            )
            
            cursor.execute('''
            INSERT INTO users (username, email, password_hash, nickname, phone, avatar, bio, location, is_active, role, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
            ''', admin_data)
            
            print("✅ 管理员用户添加完成")
        
        conn.commit()
        conn.close()
        print("✅ 示例数据初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化数据时出错: {e}")

if __name__ == '__main__':
    check_database_data()
    
    # 询问是否初始化示例数据
    response = input("\n是否需要初始化示例数据? (y/n): ")
    if response.lower() == 'y':
        init_sample_data()
        print("\n重新检查数据:")
        check_database_data()
