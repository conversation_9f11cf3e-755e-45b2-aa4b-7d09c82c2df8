package com.second.hand.ui.common

/**
 * 通用加载状态定义
 * 用于统一管理UI的加载状态
 */
sealed class LoadingState<out T> {
    object Loading : LoadingState<Nothing>()
    data class Success<T>(val data: T) : LoadingState<T>()
    data class Error(val message: String) : LoadingState<Nothing>()
    object Idle : LoadingState<Nothing>()
}

/**
 * UI状态扩展函数
 */
inline fun <T> LoadingState<T>.onLoading(action: () -> Unit): LoadingState<T> {
    if (this is LoadingState.Loading) action()
    return this
}

inline fun <T> LoadingState<T>.onSuccess(action: (T) -> Unit): LoadingState<T> {
    if (this is LoadingState.Success) action(data)
    return this
}

inline fun <T> LoadingState<T>.onError(action: (String) -> Unit): LoadingState<T> {
    if (this is LoadingState.Error) action(message)
    return this
}
