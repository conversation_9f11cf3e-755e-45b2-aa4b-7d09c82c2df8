{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-heart me-2 text-danger"></i>我的收藏</h2>
        <div class="text-muted">
            共 {{ products.total }} 件商品
        </div>
    </div>

    {% if products.items %}
    <div class="row">
        {% for product in products.items %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card h-100 product-card">
                <a href="{{ url_for('products.detail', id=product.id) }}">
                    <img src="{{ product.get_main_image_url() }}"
                         class="card-img-top" alt="{{ product.title }}" style="height: 200px; object-fit: cover;">
                </a>
                <div class="card-body d-flex flex-column">
                    <h6 class="card-title">
                        <a href="{{ url_for('products.detail', id=product.id) }}" class="text-decoration-none">
                            {{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}
                        </a>
                    </h6>
                    <p class="card-text text-muted small flex-grow-1">
                        {{ product.description[:80] }}{% if product.description|length > 80 %}...{% endif %}
                    </p>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-danger fw-bold">¥{{ "%.2f"|format(product.price) }}</span>
                            <small class="text-muted">
                                <i class="fas fa-eye"></i> {{ product.view_count }}
                            </small>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt"></i> {{ product.location or '未知' }}
                            </small>
                            <button class="btn btn-sm btn-outline-danger" 
                                    onclick="toggleFavorite({{ product.id }}, this)"
                                    title="取消收藏">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 商品状态标签 -->
                {% if product.status.value == 'sold' %}
                <div class="position-absolute top-0 start-0 m-2">
                    <span class="badge bg-secondary">已售出</span>
                </div>
                {% elif product.status.value == 'inactive' %}
                <div class="position-absolute top-0 start-0 m-2">
                    <span class="badge bg-warning">已下架</span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    {% if products.pages > 1 %}
    <nav aria-label="收藏分页" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if products.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('products.favorites', page=products.prev_num) }}">上一页</a>
            </li>
            {% endif %}
            
            {% for page_num in products.iter_pages() %}
                {% if page_num %}
                    {% if page_num != products.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('products.favorites', page=page_num) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if products.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('products.favorites', page=products.next_num) }}">下一页</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="text-center py-5 empty-state">
        <i class="fas fa-heart-broken fa-5x text-muted mb-3"></i>
        <h4 class="text-muted">暂无收藏商品</h4>
        <p class="text-muted">快去收藏一些心仪的商品吧！</p>
        <a href="{{ url_for('main.search') }}" class="btn btn-primary">
            <i class="fas fa-search me-1"></i>浏览商品
        </a>
    </div>
    {% endif %}
</div>

{% block scripts %}
<script>
function toggleFavorite(productId, button) {
    $.ajax({
        url: `/products/toggle_favorite/${productId}`,
        method: 'POST',
        data: {
            csrf_token: $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // 从收藏页面移除该商品卡片
                $(button).closest('.col-lg-3').fadeOut(function() {
                    $(this).remove();
                    // 如果页面没有商品了，刷新页面
                    if ($('.product-card').length === 0) {
                        location.reload();
                    }
                });
                showAlert(response.message, 'success');
            } else {
                showAlert(response.message || '操作失败', 'error');
            }
        },
        error: function(xhr) {
            console.error('Favorite error:', xhr);
            showAlert('操作失败，请重试', 'error');
        }
    });
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 80px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 将消息提示添加到body中，避免影响导航栏
    $('body').append(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').last().alert('close');
    }, 3000);
}
</script>
{% endblock %}
{% endblock %}
