package com.second.hand.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

/**
 * 消息卡片组件
 * 用于显示成功、错误、警告和信息提示
 */
@Composable
fun MessageCard(
    message: String,
    type: MessageType = MessageType.INFO,
    modifier: Modifier = Modifier,
    onDismiss: (() -> Unit)? = null,
    autoDismiss: Boolean = false,
    autoDismissDelay: Long = 3000L
) {
    // 自动消失逻辑
    if (autoDismiss && onDismiss != null) {
        LaunchedEffect(message) {
            delay(autoDismissDelay)
            onDismiss()
        }
    }
    
    val colors = when (type) {
        MessageType.SUCCESS -> MessageColors(
            containerColor = Color(0xFF4CAF50).copy(alpha = 0.1f),
            contentColor = Color(0xFF2E7D32),
            iconColor = Color(0xFF4CAF50)
        )
        MessageType.ERROR -> MessageColors(
            containerColor = MaterialTheme.colorScheme.errorContainer,
            contentColor = MaterialTheme.colorScheme.onErrorContainer,
            iconColor = MaterialTheme.colorScheme.error
        )
        MessageType.WARNING -> MessageColors(
            containerColor = Color(0xFFFF9800).copy(alpha = 0.1f),
            contentColor = Color(0xFFE65100),
            iconColor = Color(0xFFFF9800)
        )
        MessageType.INFO -> MessageColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer,
            contentColor = MaterialTheme.colorScheme.onPrimaryContainer,
            iconColor = MaterialTheme.colorScheme.primary
        )
    }
    
    val icon = when (type) {
        MessageType.SUCCESS -> Icons.Default.CheckCircle
        MessageType.ERROR -> Icons.Default.Info // 使用Info图标代替Error
        MessageType.WARNING -> Icons.Default.Warning
        MessageType.INFO -> Icons.Default.Info
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = colors.containerColor
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = type.name,
                tint = colors.iconColor,
                modifier = Modifier.size(24.dp)
            )
            
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = colors.contentColor,
                modifier = Modifier.weight(1f)
            )
            
            if (onDismiss != null) {
                TextButton(
                    onClick = onDismiss,
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = "关闭",
                        style = MaterialTheme.typography.labelSmall,
                        color = colors.contentColor
                    )
                }
            }
        }
    }
}

/**
 * 消息类型枚举
 */
enum class MessageType {
    SUCCESS,
    ERROR,
    WARNING,
    INFO
}

/**
 * 消息颜色数据类
 */
private data class MessageColors(
    val containerColor: Color,
    val contentColor: Color,
    val iconColor: Color
)

/**
 * 成功消息卡片
 */
@Composable
fun SuccessMessageCard(
    message: String,
    modifier: Modifier = Modifier,
    onDismiss: (() -> Unit)? = null,
    autoDismiss: Boolean = true
) {
    MessageCard(
        message = message,
        type = MessageType.SUCCESS,
        modifier = modifier,
        onDismiss = onDismiss,
        autoDismiss = autoDismiss
    )
}

/**
 * 错误消息卡片
 */
@Composable
fun ErrorMessageCard(
    message: String,
    modifier: Modifier = Modifier,
    onDismiss: (() -> Unit)? = null,
    autoDismiss: Boolean = false
) {
    MessageCard(
        message = message,
        type = MessageType.ERROR,
        modifier = modifier,
        onDismiss = onDismiss,
        autoDismiss = autoDismiss
    )
}

/**
 * 警告消息卡片
 */
@Composable
fun WarningMessageCard(
    message: String,
    modifier: Modifier = Modifier,
    onDismiss: (() -> Unit)? = null,
    autoDismiss: Boolean = true
) {
    MessageCard(
        message = message,
        type = MessageType.WARNING,
        modifier = modifier,
        onDismiss = onDismiss,
        autoDismiss = autoDismiss
    )
}

/**
 * 信息消息卡片
 */
@Composable
fun InfoMessageCard(
    message: String,
    modifier: Modifier = Modifier,
    onDismiss: (() -> Unit)? = null,
    autoDismiss: Boolean = true
) {
    MessageCard(
        message = message,
        type = MessageType.INFO,
        modifier = modifier,
        onDismiss = onDismiss,
        autoDismiss = autoDismiss
    )
}
