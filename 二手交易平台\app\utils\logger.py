#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统日志工具
"""

from flask import request, current_app
from flask_login import current_user
from app import db
from app.models import SystemLog
from datetime import datetime, timedelta

def local_now():
    """获取当前本地时间"""
    return datetime.now()

def log_system_activity(level, message, module=None, user_id=None):
    """
    记录系统活动日志
    
    Args:
        level: 日志级别 (INFO, WARNING, ERROR)
        message: 日志消息
        module: 模块名称
        user_id: 用户ID (可选)
    """
    try:
        # 获取请求信息
        ip_address = None
        user_agent = None
        
        if request:
            ip_address = request.remote_addr
            user_agent = request.headers.get('User-Agent', '')
        
        # 如果没有指定用户ID，尝试从当前用户获取
        if user_id is None and current_user.is_authenticated:
            user_id = current_user.id
        
        # 创建日志记录
        log_entry = SystemLog(
            level=level,
            message=message,
            module=module,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        db.session.add(log_entry)
        db.session.commit()
        
    except Exception as e:
        # 如果日志记录失败，不要影响主要功能
        print(f"Failed to log system activity: {e}")
        try:
            db.session.rollback()
        except:
            pass

def log_info(message, module=None, user_id=None):
    """记录信息日志"""
    log_system_activity('INFO', message, module, user_id)

def log_warning(message, module=None, user_id=None):
    """记录警告日志"""
    log_system_activity('WARNING', message, module, user_id)

def log_error(message, module=None, user_id=None):
    """记录错误日志"""
    log_system_activity('ERROR', message, module, user_id)

def log_user_action(action, details=None, module=None):
    """记录用户操作日志"""
    if current_user.is_authenticated:
        message = f"用户 {current_user.username} {action}"
        if details:
            message += f": {details}"
        log_info(message, module, current_user.id)

def log_admin_action(action, details=None):
    """记录管理员操作日志"""
    log_user_action(action, details, 'admin')

def log_auth_event(event, username=None, success=True):
    """记录认证事件"""
    level = 'INFO' if success else 'WARNING'
    message = f"认证事件: {event}"
    if username:
        message += f" (用户: {username})"
    log_system_activity(level, message, 'auth')

def log_product_event(event, product_id, product_title=None):
    """记录商品相关事件"""
    message = f"商品事件: {event} (ID: {product_id})"
    if product_title:
        message += f" - {product_title}"
    log_info(message, 'products')

def log_order_event(event, order_id, details=None):
    """记录订单相关事件"""
    message = f"订单事件: {event} (ID: {order_id})"
    if details:
        message += f" - {details}"
    log_info(message, 'orders')

def log_system_event(event, details=None):
    """记录系统事件"""
    message = f"系统事件: {event}"
    if details:
        message += f" - {details}"
    log_info(message, 'system')

def cleanup_old_logs(days=30):
    """清理旧日志"""
    try:
        cutoff_date = local_now() - timedelta(days=days)
        old_logs = SystemLog.query.filter(SystemLog.created_at < cutoff_date)
        count = old_logs.count()
        old_logs.delete()
        db.session.commit()
        log_info(f"清理了 {count} 条超过 {days} 天的旧日志", 'system')
        return count
    except Exception as e:
        db.session.rollback()
        log_error(f"清理旧日志失败: {str(e)}", 'system')
        return 0

def get_recent_logs(limit=10, level=None):
    """获取最近的日志"""
    query = SystemLog.query
    if level:
        query = query.filter(SystemLog.level == level)
    
    return query.order_by(SystemLog.created_at.desc()).limit(limit).all()

def get_log_stats():
    """获取日志统计信息"""
    from sqlalchemy import func
    
    stats = db.session.query(
        SystemLog.level,
        func.count(SystemLog.id).label('count')
    ).group_by(SystemLog.level).all()
    
    return {stat.level: stat.count for stat in stats}
