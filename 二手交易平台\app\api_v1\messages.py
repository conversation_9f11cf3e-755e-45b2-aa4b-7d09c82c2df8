#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端消息API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import Message, Product, User
from app import db
from app.utils.jwt_utils import jwt_required
from app.utils.api_response import (
    success_response, error_response, validation_error_response,
    paginated_response, not_found_response
)
from app.utils.datetime_utils import local_now
from sqlalchemy import or_, and_, desc, func


def serialize_message(message):
    """序列化消息数据"""
    return {
        'id': message.id,
        'content': message.content,
        'senderId': message.sender_id,
        'receiverId': message.receiver_id,
        'productId': message.product_id,
        'isRead': message.is_read,
        'createdAt': message.created_at.strftime('%Y-%m-%dT%H:%M:%S') + 'Z',
        'sender': {
            'id': message.sender.id,
            'username': message.sender.username,
            'nickname': message.sender.nickname,
            'avatar': message.sender.avatar_url()
        } if message.sender else None
    }


def serialize_conversation(conversation_data):
    """序列化对话数据"""
    product_id, other_user_id, last_message_time, message_count, last_message_content, last_sender_id, unread_count = conversation_data
    
    # 获取对方用户信息
    other_user = User.query.get(other_user_id)
    
    # 获取商品信息
    product = Product.query.get(product_id)
    
    return {
        'productId': product_id,
        'otherUser': {
            'id': other_user.id,
            'username': other_user.username,
            'nickname': other_user.nickname,
            'avatar': other_user.avatar_url()
        } if other_user else None,
        'product': {
            'id': product.id,
            'title': product.title,
            'mainImage': product.main_image_url(),
            'price': float(product.price)
        } if product else None,
        'lastMessage': {
            'content': last_message_content,
            'senderId': last_sender_id,
            'createdAt': last_message_time.isoformat() + 'Z' if last_message_time else None
        } if last_message_content else None,
        'unreadCount': unread_count,
        'messageCount': message_count,
        'lastMessageTime': last_message_time.isoformat() + 'Z' if last_message_time else None
    }


@bp.route('/messages/conversations', methods=['GET'])
@jwt_required
def get_conversations():
    """获取对话列表"""
    try:
        user = request.current_user
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        
        # 构建复杂查询获取对话列表
        # 按商品分组，获取每个对话的最新消息和未读数量
        subquery = db.session.query(
            Message.product_id,
            # 获取对话中的另一个用户ID
            func.max(
                db.case(
                    (Message.sender_id == user.id, Message.receiver_id),
                    else_=Message.sender_id
                )
            ).label('other_user_id'),
            func.max(Message.created_at).label('last_message_time'),
            func.count(Message.id).label('message_count'),
            # 获取最新消息内容
            func.max(Message.content).label('last_message_content'),
            func.max(Message.sender_id).label('last_sender_id')
        ).filter(
            or_(Message.sender_id == user.id, Message.receiver_id == user.id),
            # 排除当前用户已删除的对话
            or_(
                and_(Message.sender_id == user.id, Message.deleted_by_sender == False),
                and_(Message.receiver_id == user.id, Message.deleted_by_receiver == False)
            )
        ).group_by(
            Message.product_id,
            db.case(
                (Message.sender_id == user.id, Message.receiver_id),
                else_=Message.sender_id
            )
        ).subquery()
        
        # 计算未读消息数量
        unread_subquery = db.session.query(
            Message.product_id,
            func.count(Message.id).label('unread_count')
        ).filter(
            Message.receiver_id == user.id,
            Message.is_read == False,
            Message.deleted_by_receiver == False
        ).group_by(Message.product_id).subquery()
        
        # 主查询
        query = db.session.query(
            subquery.c.product_id,
            subquery.c.other_user_id,
            subquery.c.last_message_time,
            subquery.c.message_count,
            subquery.c.last_message_content,
            subquery.c.last_sender_id,
            func.coalesce(unread_subquery.c.unread_count, 0).label('unread_count')
        ).outerjoin(
            unread_subquery,
            subquery.c.product_id == unread_subquery.c.product_id
        ).order_by(desc(subquery.c.last_message_time))
        
        # 分页
        offset = (page - 1) * limit
        conversations_data = query.offset(offset).limit(limit).all()
        total = query.count()
        
        # 序列化数据
        conversations = [serialize_conversation(conv) for conv in conversations_data]
        
        # 构建分页信息
        pagination_info = {
            'page': page,
            'limit': limit,
            'total': total,
            'totalPages': (total + limit - 1) // limit,
            'hasNext': page * limit < total,
            'hasPrev': page > 1
        }
        
        return success_response({
            'items': conversations,
            'pagination': pagination_info
        })
        
    except Exception as e:
        current_app.logger.error(f"获取对话列表失败: {str(e)}")
        return error_response('SYS_001', '获取对话列表失败', http_code=500)


@bp.route('/messages/<int:user_id>/<int:product_id>', methods=['GET'])
@jwt_required
def get_messages(user_id, product_id):
    """获取聊天记录"""
    try:
        user = request.current_user
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 50, type=int), 100)
        
        # 验证商品和用户
        product = Product.query.get(product_id)
        if not product:
            return not_found_response('商品不存在')
        
        other_user = User.query.get(user_id)
        if not other_user:
            return not_found_response('用户不存在')
        
        # 构建查询
        query = Message.query.filter(
            or_(
                and_(Message.sender_id == user.id, Message.receiver_id == user_id),
                and_(Message.sender_id == user_id, Message.receiver_id == user.id)
            ),
            Message.product_id == product_id,
            # 只显示当前用户未删除的消息
            or_(
                and_(Message.sender_id == user.id, Message.deleted_by_sender == False),
                and_(Message.receiver_id == user.id, Message.deleted_by_receiver == False)
            )
        ).order_by(Message.created_at)
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )
        
        # 标记消息为已读
        unread_messages = Message.query.filter(
            Message.sender_id == user_id,
            Message.receiver_id == user.id,
            Message.product_id == product_id,
            Message.is_read == False,
            Message.deleted_by_receiver == False
        ).all()
        
        for msg in unread_messages:
            msg.is_read = True
        
        if unread_messages:
            db.session.commit()
        
        # 序列化数据
        messages = [serialize_message(message) for message in pagination.items]
        
        return paginated_response(messages, pagination)
        
    except Exception as e:
        current_app.logger.error(f"获取聊天记录失败: {str(e)}")
        return error_response('SYS_001', '获取聊天记录失败', http_code=500)


@bp.route('/messages', methods=['POST'])
@jwt_required
def send_message():
    """发送消息"""
    try:
        user = request.current_user
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 验证数据
        receiver_id = data.get('receiverId')
        product_id = data.get('productId')
        content = data.get('content', '').strip()
        
        if not receiver_id:
            return validation_error_response({'receiverId': '接收者ID不能为空'})
        
        if not product_id:
            return validation_error_response({'productId': '商品ID不能为空'})
        
        if not content:
            return validation_error_response({'content': '消息内容不能为空'})
        
        if len(content) > 1000:
            return validation_error_response({'content': '消息内容不能超过1000个字符'})
        
        # 验证接收者
        receiver = User.query.get(receiver_id)
        if not receiver:
            return not_found_response('接收者不存在')
        
        if receiver_id == user.id:
            return error_response('BIZ_002', '不能给自己发送消息')
        
        # 验证商品
        product = Product.query.get(product_id)
        if not product:
            return not_found_response('商品不存在')
        
        # 创建消息
        message = Message(
            sender_id=user.id,
            receiver_id=receiver_id,
            product_id=product_id,
            content=content,
            is_read=False,
            created_at=local_now()
        )
        
        db.session.add(message)
        db.session.commit()
        
        return success_response(
            serialize_message(message),
            '消息发送成功',
            201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"发送消息失败: {str(e)}")
        return error_response('SYS_001', '发送消息失败', http_code=500)


@bp.route('/messages/<int:message_id>/read', methods=['PUT'])
@jwt_required
def mark_message_read(message_id):
    """标记消息已读"""
    try:
        user = request.current_user
        message = Message.query.get(message_id)

        if not message:
            return not_found_response('消息不存在')

        # 权限检查（只有接收者可以标记已读）
        if message.receiver_id != user.id:
            return error_response('AUTH_004', '无权限操作此消息', http_code=403)

        message.is_read = True
        db.session.commit()

        return success_response(None, '消息已标记为已读')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"标记消息已读失败: {str(e)}")
        return error_response('SYS_001', '标记消息已读失败', http_code=500)


@bp.route('/messages/<int:message_id>', methods=['DELETE'])
@jwt_required
def delete_message(message_id):
    """删除消息"""
    try:
        user = request.current_user
        message = Message.query.get(message_id)

        if not message:
            return not_found_response('消息不存在')

        # 权限检查
        if message.sender_id != user.id and message.receiver_id != user.id:
            return error_response('AUTH_004', '无权限删除此消息', http_code=403)

        # 标记删除（软删除）
        if message.sender_id == user.id:
            message.deleted_by_sender = True
        if message.receiver_id == user.id:
            message.deleted_by_receiver = True

        # 如果双方都删除了，则物理删除
        if message.deleted_by_sender and message.deleted_by_receiver:
            db.session.delete(message)

        db.session.commit()

        return success_response(None, '消息删除成功')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除消息失败: {str(e)}")
        return error_response('SYS_001', '删除消息失败', http_code=500)


@bp.route('/messages/unread-count', methods=['GET'])
@jwt_required
def get_unread_message_count():
    """获取未读消息数量"""
    try:
        user = request.current_user

        unread_count = Message.query.filter(
            Message.receiver_id == user.id,
            Message.is_read == False,
            Message.deleted_by_receiver == False
        ).count()

        return success_response({
            'unreadCount': unread_count
        })

    except Exception as e:
        current_app.logger.error(f"获取未读消息数量失败: {str(e)}")
        return error_response('SYS_001', '获取未读消息数量失败', http_code=500)
