#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证工具
"""

import re
from typing import Any, Dict, List, Optional, Union, Tuple


def validate_category_name(name: str) -> Tuple[bool, str]:
    """
    验证分类名称
    
    Args:
        name: 分类名称
        
    Returns:
        (is_valid, error_message)
    """
    if not name or not isinstance(name, str):
        return False, "分类名称不能为空"
    
    name = name.strip()
    
    # 检查长度
    if len(name) < 2:
        return False, "分类名称长度不能少于2个字符"
    if len(name) > 80:
        return False, "分类名称长度不能超过80个字符"
    
    # 检查是否只包含数字
    if name.isdigit():
        return False, "分类名称不能只包含数字"
    
    # 检查特殊字符
    forbidden_chars = ['<', '>', '&', '"', "'", '\\', '/', '|', '*', '?', ':']
    if any(char in name for char in forbidden_chars):
        return False, "分类名称不能包含特殊字符"
    
    # 检查是否以空格开头或结尾
    if name != name.strip():
        return False, "分类名称不能以空格开头或结尾"
    
    return True, ""


def validate_form_data(data: Dict[str, Any], rules: Dict[str, Dict]) -> Tuple[bool, List[str]]:
    """
    验证表单数据
    
    Args:
        data: 表单数据
        rules: 验证规则
        
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    for field, value in data.items():
        if field in rules:
            rule = rules[field]
            
            # 检查必填字段
            if rule.get('required', False) and not value:
                errors.append(f"{field}是必填字段")
                continue
            
            # 类型验证
            if 'type' in rule and value is not None:
                expected_type = rule['type']
                
                if expected_type == int:
                    try:
                        value = int(value)
                        data[field] = value  # 更新转换后的值
                    except (ValueError, TypeError):
                        errors.append(f"{field}必须是整数")
                        continue
                        
                    # 范围验证
                    if 'min' in rule and value < rule['min']:
                        errors.append(f"{field}不能小于{rule['min']}")
                    if 'max' in rule and value > rule['max']:
                        errors.append(f"{field}不能大于{rule['max']}")
                        
                elif expected_type == float:
                    try:
                        value = float(value)
                        data[field] = value
                    except (ValueError, TypeError):
                        errors.append(f"{field}必须是数字")
                        continue
                        
                    if 'min' in rule and value < rule['min']:
                        errors.append(f"{field}不能小于{rule['min']}")
                    if 'max' in rule and value > rule['max']:
                        errors.append(f"{field}不能大于{rule['max']}")
                        
                elif expected_type == str:
                    if not isinstance(value, str):
                        value = str(value)
                        data[field] = value
                    
                    # 长度验证
                    if 'min_length' in rule and len(value) < rule['min_length']:
                        errors.append(f"{field}长度不能少于{rule['min_length']}个字符")
                    if 'max_length' in rule and len(value) > rule['max_length']:
                        errors.append(f"{field}长度不能超过{rule['max_length']}个字符")
                    
                    # 正则验证
                    if 'pattern' in rule and not re.match(rule['pattern'], value):
                        errors.append(f"{field}格式不正确")
    
    return len(errors) == 0, errors


def sanitize_input(value: Any) -> str:
    """
    清理输入数据
    
    Args:
        value: 输入值
        
    Returns:
        清理后的字符串
    """
    if value is None:
        return ""
    
    # 转换为字符串
    if not isinstance(value, str):
        value = str(value)
    
    # 移除危险字符
    value = value.replace('<', '&lt;').replace('>', '&gt;')
    value = value.replace('"', '&quot;').replace("'", '&#x27;')
    value = value.replace('&', '&amp;')
    
    # 移除控制字符
    value = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
    
    return value.strip()


def validate_numeric_id(value: Any, field_name: str = "ID") -> Tuple[bool, Optional[int], str]:
    """
    验证数字ID
    
    Args:
        value: 要验证的值
        field_name: 字段名称
        
    Returns:
        (is_valid, converted_value, error_message)
    """
    if value is None:
        return False, None, f"{field_name}不能为空"
    
    try:
        int_value = int(value)
        if int_value <= 0:
            return False, None, f"{field_name}必须是正整数"
        return True, int_value, ""
    except (ValueError, TypeError):
        return False, None, f"{field_name}必须是有效的数字"


def validate_email(email: str) -> Tuple[bool, str]:
    """
    验证邮箱地址
    
    Args:
        email: 邮箱地址
        
    Returns:
        (is_valid, error_message)
    """
    if not email:
        return False, "邮箱地址不能为空"
    
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return False, "邮箱地址格式不正确"
    
    if len(email) > 254:
        return False, "邮箱地址长度不能超过254个字符"
    
    return True, ""


def validate_phone(phone: str) -> Tuple[bool, str]:
    """
    验证手机号码
    
    Args:
        phone: 手机号码
        
    Returns:
        (is_valid, error_message)
    """
    if not phone:
        return True, ""  # 手机号可以为空
    
    # 中国手机号格式
    phone_pattern = r'^1[3-9]\d{9}$'
    if not re.match(phone_pattern, phone):
        return False, "手机号码格式不正确"
    
    return True, ""


# 预定义的验证规则
CATEGORY_VALIDATION_RULES = {
    'name': {
        'required': True,
        'type': str,
        'min_length': 2,
        'max_length': 80,
        'pattern': r'^(?!\d+$).+'  # 不能只包含数字
    },
    'description': {
        'type': str,
        'max_length': 500
    },
    'icon': {
        'type': str,
        'max_length': 100
    }
}

SYSTEM_CONFIG_VALIDATION_RULES = {
    'site_name': {
        'required': True,
        'type': str,
        'min_length': 1,
        'max_length': 100
    },
    'site_description': {
        'type': str,
        'max_length': 500
    },
    'contact_email': {
        'type': str,
        'max_length': 100
    },
    'contact_phone': {
        'type': str,
        'max_length': 20
    },
    'announcement': {
        'type': str,
        'max_length': 1000
    },
    'products_per_page': {
        'type': int,
        'min': 6,
        'max': 50
    },
    'orders_per_page': {
        'type': int,
        'min': 5,
        'max': 50
    },
    'max_file_size': {
        'type': int,
        'min': 1,
        'max': 100
    },
    'mail_port': {
        'type': int,
        'min': 1,
        'max': 65535
    }
}
