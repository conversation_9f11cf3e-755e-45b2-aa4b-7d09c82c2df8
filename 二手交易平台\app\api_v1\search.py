#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端搜索API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import Product, Category, ProductStatus
from app import db
from app.utils.api_response import success_response, error_response, paginated_response
from sqlalchemy import or_, and_, desc, func
import re


@bp.route('/search', methods=['GET'])
def search_products():
    """搜索商品"""
    try:
        # 获取查询参数
        query = request.args.get('q', '').strip()
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        category_id = request.args.get('category', type=int)
        min_price = request.args.get('minPrice', type=float)
        max_price = request.args.get('maxPrice', type=float)
        condition = request.args.get('condition', '').strip()
        location = request.args.get('location', '').strip()
        sort = request.args.get('sort', 'created_at')
        order = request.args.get('order', 'desc')
        
        # 构建查询
        search_query = Product.query.filter(Product.status == ProductStatus.ACTIVE)
        
        # 关键词搜索
        if query:
            # 分词搜索（简单实现）
            keywords = re.findall(r'\w+', query.lower())
            if keywords:
                conditions = []
                for keyword in keywords:
                    conditions.append(
                        or_(
                            Product.title.contains(keyword),
                            Product.description.contains(keyword)
                        )
                    )
                search_query = search_query.filter(and_(*conditions))
        
        # 分类筛选
        if category_id:
            search_query = search_query.filter(Product.category_id == category_id)
        
        # 价格筛选
        if min_price is not None:
            search_query = search_query.filter(Product.price >= min_price)
        if max_price is not None:
            search_query = search_query.filter(Product.price <= max_price)
        
        # 成色筛选
        if condition:
            search_query = search_query.filter(Product.condition == condition)
        
        # 地区筛选
        if location:
            search_query = search_query.filter(Product.location.contains(location))
        
        # 排序
        if sort == 'price':
            if order == 'asc':
                search_query = search_query.order_by(Product.price.asc())
            else:
                search_query = search_query.order_by(Product.price.desc())
        elif sort == 'view_count':
            search_query = search_query.order_by(Product.view_count.desc())
        elif sort == 'favorite_count':
            search_query = search_query.order_by(Product.favorite_count.desc())
        else:  # created_at
            if order == 'asc':
                search_query = search_query.order_by(Product.created_at.asc())
            else:
                search_query = search_query.order_by(Product.created_at.desc())
        
        # 分页
        pagination = search_query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )
        
        # 序列化数据
        from app.api_v1.products import serialize_product
        products = [serialize_product(product) for product in pagination.items]
        
        return paginated_response(products, pagination)
        
    except Exception as e:
        current_app.logger.error(f"搜索商品失败: {str(e)}")
        return error_response('SYS_001', '搜索商品失败', http_code=500)


@bp.route('/search/suggestions', methods=['GET'])
def search_suggestions():
    """搜索建议"""
    try:
        query = request.args.get('q', '').strip()
        
        if not query or len(query) < 2:
            return success_response([])
        
        # 从商品标题中获取建议
        suggestions = db.session.query(Product.title).filter(
            Product.title.contains(query),
            Product.status == ProductStatus.ACTIVE
        ).distinct().limit(10).all()
        
        suggestion_list = [s.title for s in suggestions]
        
        return success_response(suggestion_list)
        
    except Exception as e:
        current_app.logger.error(f"获取搜索建议失败: {str(e)}")
        return error_response('SYS_001', '获取搜索建议失败', http_code=500)


@bp.route('/search/hot-keywords', methods=['GET'])
def hot_keywords():
    """热门搜索关键词"""
    try:
        # 这里简化实现，返回一些预设的热门关键词
        # 实际应用中可以根据搜索日志统计
        hot_keywords = [
            'iPhone',
            '笔记本电脑',
            '二手车',
            '家具',
            '数码相机',
            '游戏机',
            '手表',
            '包包',
            '书籍',
            '运动器材'
        ]
        
        return success_response(hot_keywords)
        
    except Exception as e:
        current_app.logger.error(f"获取热门关键词失败: {str(e)}")
        return error_response('SYS_001', '获取热门关键词失败', http_code=500)


@bp.route('/search/filters', methods=['GET'])
def search_filters():
    """获取搜索筛选选项"""
    try:
        # 获取所有分类
        categories = Category.query.order_by(Category.id).all()
        category_options = [
            {
                'id': cat.id,
                'name': cat.name,
                'icon': cat.icon
            }
            for cat in categories
        ]
        
        # 获取成色选项
        condition_options = [
            {'value': '全新', 'label': '全新'},
            {'value': '九成新', 'label': '九成新'},
            {'value': '八成新', 'label': '八成新'},
            {'value': '七成新', 'label': '七成新'},
            {'value': '六成新及以下', 'label': '六成新及以下'}
        ]
        
        # 获取价格区间选项
        price_ranges = [
            {'min': 0, 'max': 100, 'label': '100元以下'},
            {'min': 100, 'max': 500, 'label': '100-500元'},
            {'min': 500, 'max': 1000, 'label': '500-1000元'},
            {'min': 1000, 'max': 5000, 'label': '1000-5000元'},
            {'min': 5000, 'max': None, 'label': '5000元以上'}
        ]
        
        # 获取排序选项
        sort_options = [
            {'value': 'created_at', 'label': '最新发布'},
            {'value': 'price', 'label': '价格'},
            {'value': 'view_count', 'label': '浏览量'},
            {'value': 'favorite_count', 'label': '收藏量'}
        ]
        
        return success_response({
            'categories': category_options,
            'conditions': condition_options,
            'priceRanges': price_ranges,
            'sortOptions': sort_options
        })
        
    except Exception as e:
        current_app.logger.error(f"获取搜索筛选选项失败: {str(e)}")
        return error_response('SYS_001', '获取搜索筛选选项失败', http_code=500)
