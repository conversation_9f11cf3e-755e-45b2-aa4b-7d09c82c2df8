{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-bell me-2"></i>通知中心</h2>
        <div>
            <form method="POST" action="{{ url_for('notifications.mark_all_read') }}" class="d-inline">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <button type="submit" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-check-double"></i> 全部标记为已读
                </button>
            </form>
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllNotifications()">
                <i class="fas fa-trash-alt"></i> 清空所有通知
            </button>
        </div>
    </div>

    <!-- 通知筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('notifications.notifications') }}">
                <div class="row g-3">
                    <div class="col-md-4">
                        <select class="form-select" name="type">
                            <option value="all" {{ 'selected' if type_filter == 'all' else '' }}>全部类型</option>
                            <option value="order" {{ 'selected' if type_filter == 'order' else '' }}>订单通知</option>
                            <option value="system" {{ 'selected' if type_filter == 'system' else '' }}>系统通知</option>
                            <option value="audit" {{ 'selected' if type_filter == 'audit' else '' }}>审核通知</option>
                            <option value="review" {{ 'selected' if type_filter == 'review' else '' }}>评价通知</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" name="status">
                            <option value="all" {{ 'selected' if status_filter == 'all' else '' }}>全部状态</option>
                            <option value="unread" {{ 'selected' if status_filter == 'unread' else '' }}>未读</option>
                            <option value="read" {{ 'selected' if status_filter == 'read' else '' }}>已读</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> 筛选
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 通知列表 -->
    <div class="notification-list-container">
        {% if notifications.items %}
        {% for notification in notifications.items %}
        <div class="card mb-3 notification-item {{ 'unread' if not notification.is_read else '' }}"
             data-notification-id="{{ notification.id }}"
             data-type="{{ notification.type }}"
             data-related-id="{{ notification.related_id or '' }}"
             onclick="handleNotificationClick({{ notification.id }}, '{{ notification.type }}', {{ notification.related_id or 'null' }}, {{ 'false' if notification.is_read else 'true' }})"
             style="cursor: pointer;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-bell me-2 text-primary"></i>
                            <h6 class="mb-0 notification-title">{{ notification.title or '系统通知' }}</h6>
                            {% if not notification.is_read %}
                            <span class="badge bg-primary ms-2">新</span>
                            {% endif %}
                        </div>
                        <p class="notification-content mb-2">{{ notification.content or '您有新的通知' }}</p>
                        <small class="text-muted notification-time">
                            <i class="fas fa-clock me-1"></i>
                            {{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else '刚刚' }}
                        </small>
                    </div>
                    <div class="flex-shrink-0 ms-3">
                        <button type="button" class="btn btn-outline-danger btn-sm"
                                onclick="deleteNotification({{ notification.id }}, event)"
                                title="删除通知">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- 分页 -->
        {% if notifications.pages > 1 %}
        <nav aria-label="通知分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if notifications.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('notifications.notifications', page=notifications.prev_num, type=type_filter, status=status_filter) }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in notifications.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != notifications.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('notifications.notifications', page=page_num, type=type_filter, status=status_filter) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if notifications.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('notifications.notifications', page=notifications.next_num, type=type_filter, status=status_filter) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5 empty-state">
            <i class="fas fa-bell-slash fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">暂无通知</h4>
            <p class="text-muted">您还没有收到任何通知</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.notification-item.unread {
    border-left: 4px solid #007bff;
    background-color: #f8f9ff;
}

.notification-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

.empty-state {
    padding: 3rem 0;
}
</style>

<script>
// 处理通知点击
function handleNotificationClick(notificationId, type, relatedId, isUnread) {
    console.log('点击通知:', notificationId, type, relatedId, isUnread);

    // 如果是未读通知，先标记为已读
    if (isUnread === 'true' || isUnread === true) {
        const csrfToken = $('meta[name=csrf-token]').attr('content');
        console.log('CSRF Token:', csrfToken);

        $.ajax({
            url: `/api/notifications/${notificationId}/read`,
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            },
            success: function(response) {
                console.log('标记已读成功:', response);
                if (response.success) {
                    // 更新UI
                    const notificationCard = $(`[data-notification-id="${notificationId}"]`);
                    notificationCard.removeClass('unread');
                    notificationCard.find('.badge').remove();

                    // 立即更新通知计数，避免闪烁
                    updateNotificationCounts();
                }
            },
            error: function(xhr, status, error) {
                console.log('标记通知已读失败:', xhr.status, xhr.responseText, error);
            }
        });
    }

    // 根据通知类型跳转到相应页面
    setTimeout(() => {
        switch(type) {
            case 'order':
                if (relatedId) {
                    window.location.href = `/orders/detail/${relatedId}`;
                } else {
                    window.location.href = '/orders/my_orders';
                }
                break;
            case 'message':
                if (relatedId) {
                    // 根据消息ID获取消息信息并跳转到对应的聊天页面
                    fetch(`/messages/api/message_info/${relatedId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const chatUrl = data.product_id
                                    ? `/messages/chat/${data.sender_id}/${data.product_id}`
                                    : `/messages/chat/${data.sender_id}`;
                                window.location.href = chatUrl;
                            } else {
                                window.location.href = '/messages/';
                            }
                        })
                        .catch(error => {
                            console.error('获取消息信息失败:', error);
                            window.location.href = '/messages/';
                        });
                } else {
                    window.location.href = '/messages/';
                }
                break;
            case 'review':
                window.location.href = '/auth/profile#reviews';
                break;
            case 'audit':
                if (relatedId) {
                    window.location.href = `/products/detail/${relatedId}`;
                } else {
                    window.location.href = '/products/my_products';
                }
                break;
            case 'system':
            default:
                // 系统通知不跳转，只标记已读
                break;
        }
    }, 100);
}

// 删除单个通知
function deleteNotification(notificationId, event) {
    event.stopPropagation(); // 阻止事件冒泡

    if (!confirm('确定要删除这条通知吗？')) {
        return;
    }

    $.ajax({
        url: `/notifications/api/delete/${notificationId}`,
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // 移除通知项
                $(`.notification-item[data-notification-id="${notificationId}"]`).fadeOut(300, function() {
                    $(this).remove();
                    // 检查是否还有通知
                    if ($('.notification-item').length === 0) {
                        location.reload(); // 重新加载页面显示空状态
                    }
                });
                showAlert(response.message, 'success');
            } else {
                showAlert(response.message, 'error');
            }
        },
        error: function() {
            showAlert('删除失败，请重试', 'error');
        }
    });
}

// 清空所有通知
function clearAllNotifications() {
    if (!confirm('确定要清空所有通知吗？此操作不可恢复。')) {
        return;
    }

    $.ajax({
        url: '/notifications/api/clear_all',
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                // 延迟重新加载页面
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                showAlert(response.message, 'error');
            }
        },
        error: function() {
            showAlert('清空失败，请重试', 'error');
        }
    });
}

// 更新通知计数（避免闪烁）
function updateNotificationCounts() {
    // 重置缓存，强制更新
    if (typeof window.cachedNotificationCount !== 'undefined') {
        window.cachedNotificationCount = -1;
    }
    if (typeof window.cachedMessageCount !== 'undefined') {
        window.cachedMessageCount = -1;
    }

    // 调用全局的通知更新函数（如果存在）
    if (typeof loadUnreadCounts === 'function') {
        loadUnreadCounts();
    } else if (typeof refreshNotifications === 'function') {
        refreshNotifications();
    }

    // 同时刷新当前页面的通知列表
    refreshNotificationList();
}

// 刷新通知列表内容
function refreshNotificationList() {
    $.ajax({
        url: '/notifications/api/latest_notifications',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateNotificationListContent(response.notifications);
            }
        },
        error: function() {
            console.log('获取通知列表失败');
        }
    });
}

// 更新通知列表内容
function updateNotificationListContent(notifications) {
    const container = $('.notification-list-container');

    if (notifications && notifications.length > 0) {
        let html = '';
        notifications.forEach(function(notification) {
            const isUnread = !notification.is_read;
            const unreadClass = isUnread ? 'unread' : 'read';
            const unreadBadge = isUnread ? '<span class="badge bg-primary ms-2">新</span>' : '';

            html += `
                <div class="card mb-3 notification-item ${unreadClass}"
                     data-notification-id="${notification.id}"
                     data-type="${notification.type}"
                     data-related-id="${notification.related_id || ''}"
                     onclick="handleNotificationClick(${notification.id}, '${notification.type}', ${notification.related_id || 'null'}, ${isUnread})"
                     style="cursor: pointer;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-bell me-2 text-primary"></i>
                                    <h6 class="mb-0 notification-title">${notification.title}</h6>
                                    ${unreadBadge}
                                </div>
                                <p class="notification-content mb-2">${notification.content}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    ${notification.time_ago}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        container.html(html);
    } else {
        container.html(`
            <div class="text-center py-5 empty-state">
                <i class="fas fa-bell-slash fa-5x text-muted mb-3"></i>
                <h4 class="text-muted">暂无通知</h4>
                <p class="text-muted">您还没有收到任何通知</p>
            </div>
        `);
    }
}

// 显示警告消息
function showAlert(message, type) {
    var alertClass = type === 'error' ? 'alert-danger' : 'alert-' + type;
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.container').first().prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').first().alert('close');
    }, 3000);
}

// 页面加载完成后启动实时更新
$(document).ready(function() {
    // 立即刷新一次通知列表
    refreshNotificationList();

    // 每3秒检查一次新通知
    setInterval(function() {
        refreshNotificationList();
    }, 3000);

    // 页面获得焦点时刷新
    $(window).focus(function() {
        refreshNotificationList();
    });

    // 页面可见性改变时刷新
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            refreshNotificationList();
        }
    });
});
</script>
{% endblock %}
