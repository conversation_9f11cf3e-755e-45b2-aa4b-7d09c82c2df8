{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-comments"></i> 用户反馈</h2>
</div>

<!-- 筛选器 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET">
            <div class="row g-3">
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="all" {{ 'selected' if status_filter == 'all' else '' }}>全部状态</option>
                        <option value="pending" {{ 'selected' if status_filter == 'pending' else '' }}>待处理</option>
                        <option value="processing" {{ 'selected' if status_filter == 'processing' else '' }}>处理中</option>
                        <option value="resolved" {{ 'selected' if status_filter == 'resolved' else '' }}>已解决</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="type" class="form-select">
                        <option value="all" {{ 'selected' if type_filter == 'all' else '' }}>全部类型</option>
                        <option value="bug" {{ 'selected' if type_filter == 'bug' else '' }}>Bug报告</option>
                        <option value="suggestion" {{ 'selected' if type_filter == 'suggestion' else '' }}>建议</option>
                        <option value="complaint" {{ 'selected' if type_filter == 'complaint' else '' }}>投诉</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" name="search" 
                           value="{{ search or '' }}" placeholder="搜索反馈内容">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">筛选</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 反馈列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>用户</th>
                        <th>标题</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>提交时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for feedback in feedbacks.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if feedback.user %}
                                <img src="{{ feedback.user.get_avatar_url() }}"
                                     class="rounded-circle me-2"
                                     width="32" height="32" alt="头像">
                                <div>
                                    <div class="fw-bold">{{ feedback.user.username }}</div>
                                    <small class="text-muted">{{ feedback.user.email }}</small>
                                </div>
                                {% else %}
                                <img src="/static/images/default-avatar.png"
                                     class="rounded-circle me-2"
                                     width="32" height="32" alt="头像">
                                <div>
                                    <div class="fw-bold">访客用户</div>
                                    <small class="text-muted">未注册用户</small>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>
                                    <a href="{{ url_for('admin_panel.feedback_detail', id=feedback.id) }}"
                                       class="text-decoration-none">
                                        {{ feedback.title }}
                                    </a>
                                </strong>
                                <div class="text-muted small">{{ feedback.content[:50] }}...</div>
                            </div>
                        </td>
                        <td>
                            {% if feedback.type == 'bug' %}
                            <span class="badge bg-danger">Bug报告</span>
                            {% elif feedback.type == 'suggestion' %}
                            <span class="badge bg-info">建议</span>
                            {% elif feedback.type == 'complaint' %}
                            <span class="badge bg-warning">投诉</span>
                            {% elif feedback.type == 'contact' %}
                            <span class="badge bg-primary">联系我们</span>
                            {% else %}
                            <span class="badge bg-secondary">其他</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if feedback.status == 'pending' %}
                            <span class="badge bg-warning">待处理</span>
                            {% elif feedback.status == 'processing' %}
                            <span class="badge bg-info">处理中</span>
                            {% elif feedback.status == 'waiting_user' %}
                            <span class="badge bg-primary">等待用户回复</span>
                            {% elif feedback.status == 'resolved' %}
                            <span class="badge bg-success">已解决</span>
                            {% elif feedback.status == 'closed' %}
                            <span class="badge bg-secondary">已关闭</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ feedback.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('admin_panel.feedback_detail', id=feedback.id) }}"
                                   class="btn btn-outline-info" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('admin_panel.update_feedback_status', id=feedback.id) }}"
                                   class="btn btn-outline-warning" title="更新状态">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('admin_panel.reply_feedback', id=feedback.id) }}"
                                   class="btn btn-outline-primary" title="回复">
                                    <i class="fas fa-reply"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if feedbacks.pages > 1 %}
        <nav aria-label="反馈分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if feedbacks.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin_panel.feedbacks', page=feedbacks.prev_num, status=status_filter, type=type_filter, search=search) }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in feedbacks.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != feedbacks.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin_panel.feedbacks', page=page_num, status=status_filter, type=type_filter, search=search) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if feedbacks.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin_panel.feedbacks', page=feedbacks.next_num, status=status_filter, type=type_filter, search=search) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}
