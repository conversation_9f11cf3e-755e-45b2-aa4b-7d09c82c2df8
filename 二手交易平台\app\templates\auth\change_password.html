{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-key"></i> 修改密码</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.old_password.label(class="form-label") }}
                        {{ form.old_password(class="form-control", id="old_password") }}
                        {% for error in form.old_password.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>

                    <!-- 邮箱验证码 -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.email_code.label(class="form-label") }}
                                {{ form.email_code(class="form-control" + (" is-invalid" if form.email_code.errors else ""), placeholder="请输入6位数字验证码") }}
                                {% if form.email_code.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.email_code.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex align-items-center">
                                    <button type="button" id="sendCodeBtn" class="btn btn-outline-warning" onclick="sendChangePasswordCode()">
                                        获取验证码
                                    </button>
                                </div>
                                <small class="text-muted">验证码将发送到您的邮箱：{{ current_user.email }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control") }}
                        {% for error in form.password.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <div class="form-text">密码长度至少6位，建议包含字母和数字</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password2.label(class="form-label") }}
                        {{ form.password2(class="form-control") }}
                        {% for error in form.password2.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>修改密码
                        </button>
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 安全提示 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-shield-alt"></i> 安全提示</h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>密码长度至少6位字符</li>
                    <li>建议使用字母、数字和特殊字符的组合</li>
                    <li>不要使用过于简单的密码</li>
                    <li>定期更换密码以保证账户安全</li>
                    <li>不要在多个网站使用相同密码</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <!-- 用户信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">账户信息</h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <img src="{{ current_user.get_avatar_url() }}" 
                         class="rounded-circle me-3" 
                         width="64" height="64" alt="头像">
                    <div>
                        <h6 class="mb-0">{{ current_user.username }}</h6>
                        <small class="text-muted">{{ current_user.email }}</small>
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>注册时间:</strong></div>
                    <div class="col-7">{{ current_user.created_at.strftime('%Y-%m-%d') if current_user.created_at else '未知' }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>最后登录:</strong></div>
                    <div class="col-7">{{ current_user.last_seen.strftime('%Y-%m-%d %H:%M') if current_user.last_seen else '从未' }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>账户状态:</strong></div>
                    <div class="col-7">
                        {% if current_user.is_active %}
                        <span class="badge bg-success">正常</span>
                        {% else %}
                        <span class="badge bg-danger">已禁用</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近活动 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">账户统计</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="h5 mb-0 text-primary">{{ current_user.products.count() }}</div>
                        <small class="text-muted">发布商品</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 mb-0 text-success">{{ current_user.orders_as_seller.count() }}</div>
                        <small class="text-muted">销售订单</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 mb-0 text-info">{{ current_user.orders_as_buyer.count() }}</div>
                        <small class="text-muted">购买订单</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 发送修改密码验证码
let countdown = 0;
let countdownTimer = null;

function sendChangePasswordCode() {
    const oldPasswordInput = document.getElementById('old_password');
    const sendBtn = document.getElementById('sendCodeBtn');
    const currentPassword = oldPasswordInput.value.trim();

    // 验证当前密码
    if (!currentPassword) {
        alert('请先输入当前密码');
        oldPasswordInput.focus();
        return;
    }

    // 如果正在倒计时，不允许重复发送
    if (countdown > 0) {
        return;
    }

    // 禁用按钮
    sendBtn.disabled = true;
    sendBtn.textContent = '发送中...';

    // 获取CSRF令牌
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;

    // 发送请求
    fetch('{{ url_for("auth.send_change_password_code") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            current_password: currentPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            startCountdown();
        } else {
            alert(data.message);
            sendBtn.disabled = false;
            sendBtn.textContent = '获取验证码';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('发送失败，请稍后重试');
        sendBtn.disabled = false;
        sendBtn.textContent = '获取验证码';
    });
}

function startCountdown() {
    const sendBtn = document.getElementById('sendCodeBtn');
    countdown = 120; // 120秒倒计时

    countdownTimer = setInterval(() => {
        sendBtn.textContent = `${countdown}秒后重新获取`;
        countdown--;

        if (countdown < 0) {
            clearInterval(countdownTimer);
            sendBtn.disabled = false;
            sendBtn.textContent = '获取验证码';
            countdown = 0;
        }
    }, 1000);
}
</script>
{% endblock %}
