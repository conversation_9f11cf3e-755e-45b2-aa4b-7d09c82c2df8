{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-trash"></i> 删除对话功能测试</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>测试页面</strong> - 用于测试删除对话功能
                    </div>
                    
                    <div class="mb-4">
                        <h6>测试删除API</h6>
                        <form id="deleteTestForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">用户ID</label>
                                    <input type="number" class="form-control" id="userId" placeholder="输入要删除对话的用户ID" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">商品ID (可选)</label>
                                    <input type="number" class="form-control" id="productId" placeholder="输入商品ID，留空表示无商品">
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> 测试删除
                                </button>
                                <button type="button" class="btn btn-info" onclick="testAPI()">
                                    <i class="fas fa-test-tube"></i> 测试API连接
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="mb-4">
                        <h6>API响应日志</h6>
                        <div id="apiLog" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                            等待API调用...
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h6>当前对话列表</h6>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadConversations()">
                            <i class="fas fa-refresh"></i> 刷新对话列表
                        </button>
                        <div id="conversationsList" class="mt-3">
                            <div class="text-muted">点击刷新按钮加载对话列表...</div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('messages.index') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> 返回消息中心
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 表单提交处理
    $('#deleteTestForm').on('submit', function(e) {
        e.preventDefault();
        
        const userId = $('#userId').val();
        const productId = $('#productId').val();
        
        if (!userId) {
            alert('请输入用户ID');
            return;
        }
        
        testDeleteConversation(userId, productId);
    });
    
    // 自动加载对话列表
    loadConversations();
});

function logMessage(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logDiv = $('#apiLog');
    const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
    
    logDiv.append(`<div class="${colorClass}">[${timestamp}] ${message}</div>`);
    logDiv.scrollTop(logDiv[0].scrollHeight);
}

function testAPI() {
    logMessage('测试API连接...', 'info');
    
    $.ajax({
        url: '/messages/api/conversations',
        method: 'GET',
        success: function(response) {
            logMessage('API连接成功！', 'success');
            logMessage('响应: ' + JSON.stringify(response, null, 2), 'info');
        },
        error: function(xhr, status, error) {
            logMessage('API连接失败: ' + error, 'error');
            logMessage('状态: ' + status + ', 错误: ' + xhr.responseText, 'error');
        }
    });
}

function testDeleteConversation(userId, productId) {
    logMessage(`开始测试删除对话: userId=${userId}, productId=${productId || '无'}`, 'info');
    
    const data = {
        user_id: userId,
        product_id: productId || '',
        csrf_token: $('meta[name=csrf-token]').attr('content')
    };
    
    logMessage('发送数据: ' + JSON.stringify(data), 'info');
    
    $.ajax({
        url: '/messages/api/delete_conversation',
        method: 'POST',
        data: data,
        beforeSend: function() {
            logMessage('发送删除请求...', 'info');
        },
        success: function(response) {
            logMessage('删除请求成功！', 'success');
            logMessage('响应: ' + JSON.stringify(response, null, 2), 'success');
            
            if (response.success) {
                logMessage(`删除成功: ${response.message}`, 'success');
                logMessage(`删除了 ${response.deleted_count} 条消息`, 'success');
                
                // 刷新对话列表
                setTimeout(loadConversations, 1000);
            } else {
                logMessage(`删除失败: ${response.message}`, 'error');
            }
        },
        error: function(xhr, status, error) {
            logMessage('删除请求失败！', 'error');
            logMessage('状态: ' + status, 'error');
            logMessage('错误: ' + error, 'error');
            logMessage('响应内容: ' + xhr.responseText, 'error');
            
            try {
                const response = JSON.parse(xhr.responseText);
                logMessage('解析的错误响应: ' + JSON.stringify(response, null, 2), 'error');
            } catch (e) {
                logMessage('无法解析错误响应', 'error');
            }
        }
    });
}

function loadConversations() {
    logMessage('加载对话列表...', 'info');
    
    $.ajax({
        url: '/messages/api/conversations',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                logMessage(`加载成功，找到 ${response.conversations.length} 个对话`, 'success');
                displayConversations(response.conversations);
            } else {
                logMessage('加载对话列表失败: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            logMessage('加载对话列表失败: ' + error, 'error');
        }
    });
}

function displayConversations(conversations) {
    const container = $('#conversationsList');
    
    if (conversations.length === 0) {
        container.html('<div class="text-muted">暂无对话</div>');
        return;
    }
    
    let html = '<div class="list-group">';
    conversations.forEach(function(conv) {
        const productInfo = conv.product ? `商品: ${conv.product.title}` : '无关联商品';
        const lastMessage = conv.last_message ? conv.last_message.content.substring(0, 50) : '暂无消息';
        
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">用户: ${conv.other_user.nickname || conv.other_user.username} (ID: ${conv.other_user.id})</h6>
                        <p class="mb-1 text-muted">${productInfo}</p>
                        <small class="text-muted">最后消息: ${lastMessage}</small>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-danger" onclick="testDeleteConversation(${conv.other_user.id}, '${conv.product ? conv.product.id : ''}')">
                            删除
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    container.html(html);
}
</script>
{% endblock %}
