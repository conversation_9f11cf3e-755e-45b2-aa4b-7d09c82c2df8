{% extends "base.html" %}

{% block content %}
<div class="row">
    <!-- 商品图片 -->
    <div class="col-lg-6">
        <div class="product-gallery">
            {% set images = product.get_all_images() %}
            {% if images %}
            <div id="productCarousel" class="carousel slide mb-3" data-bs-ride="carousel">
                <div class="carousel-inner">
                    {% for image in images %}
                    <div class="carousel-item {% if loop.first %}active{% endif %}">
                        <img src="/static/uploads/products/{{ image }}"
                             class="d-block w-100 rounded" alt="{{ product.title }}"
                             style="height: 400px; object-fit: cover;">
                    </div>
                    {% endfor %}
                </div>
                {% if images|length > 1 %}
                <button class="carousel-control-prev" type="button" data-bs-target="#productCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#productCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                </button>
                {% endif %}
            </div>
            
            <!-- 缩略图 -->
            {% if images|length > 1 %}
            <div class="row">
                {% for image in images %}
                <div class="col-3 mb-2">
                    <img src="/static/uploads/products/{{ image }}"
                         class="img-thumbnail thumbnail-image" alt="{{ product.title }}"
                         data-bs-target="#productCarousel" data-bs-slide-to="{{ loop.index0 }}"
                         style="height: 80px; object-fit: cover; cursor: pointer;">
                </div>
                {% endfor %}
            </div>
            {% endif %}
            {% else %}
            <img src="{{ product.get_main_image_url() }}"
                 class="img-fluid rounded" alt="{{ product.title }}"
                 style="height: 400px; object-fit: cover;">
            {% endif %}
        </div>
    </div>
    
    <!-- 商品信息 -->
    <div class="col-lg-6">
        <div class="product-info">
            <h1 class="h3 mb-3">{{ product.title }}</h1>
            
            <!-- 价格 -->
            <div class="price-section mb-4">
                <span class="price text-danger h2 fw-bold">¥{{ "%.2f"|format(product.price) }}</span>
                {% if product.original_price and product.original_price > product.price %}
                <span class="original-price text-muted ms-2">原价: ¥{{ "%.2f"|format(product.original_price) }}</span>
                {% endif %}
            </div>
            
            <!-- 商品信息 -->
            <div class="product-info-list mb-3">
                <div class="info-item mb-2">
                    <small class="text-muted">分类:</small>
                    <span>{{ product.category.name }}</span>
                </div>
                <div class="info-item mb-2">
                    <small class="text-muted">痕迹:</small>
                    <span>{{ product.condition }}</span>
                </div>
                <div class="info-item mb-2">
                    <small class="text-muted">发布者:</small>
                    <span>{{ product.seller.nickname or product.seller.username }}</span>
                </div>
                <div class="info-item mb-2">
                    <small class="text-muted">地址:</small>
                    <span>{{ product.location or '未知' }}</span>
                </div>
                <div class="info-item mb-2">
                    <small class="text-muted">浏览:</small>
                    <span class="view-count">{{ product.view_count }}</span>
                </div>
                <div class="info-item mb-2">
                    <small class="text-muted">点赞:</small>
                    <span class="favorite-count">{{ product.favorite_count }}</span>
                </div>
            </div>

            <!-- 库存和状态信息 -->
            <div class="product-meta mb-4">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">库存数量:</small>
                        <span class="{% if product.get_remaining_quantity() == 0 %}text-danger{% elif product.get_remaining_quantity() <= 5 %}text-warning{% else %}text-success{% endif %}">
                            {{ product.get_remaining_quantity() }}
                        </span>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">商品状态:</small>
                        <span class="badge {% if product.is_sold_out() %}bg-danger{% elif product.status.value == 'active' %}bg-success{% else %}bg-secondary{% endif %}">
                            {{ product.get_status_display() }}
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 卖家信息 -->
            <div class="seller-info card mb-4">
                <div class="card-body">
                    <h6 class="card-title">卖家信息</h6>
                    <div class="d-flex align-items-center">
                        <img src="{{ product.seller.get_avatar_url() }}" 
                             class="rounded-circle me-3" width="50" height="50" alt="卖家头像">
                        <div>
                            <div class="fw-bold">{{ product.seller.nickname or product.seller.username }}</div>
                            <small class="text-muted">
                                上架时间: {{ product.created_at.strftime('%Y-%m-%d') if product.created_at else '未知' }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons mb-4">
                {% if current_user.is_authenticated %}
                    {% if current_user.id == product.seller_id %}
                    <!-- 卖家操作 -->
                    <a href="{{ url_for('products.edit', id=product.id) }}" class="btn btn-warning me-2">
                        <i class="fas fa-edit"></i> 编辑商品
                    </a>
                    {% elif product.status.value == 'active' %}
                    <!-- 买家操作 -->
                    <a href="{{ url_for('orders.create', product_id=product.id) }}" class="btn btn-success btn-lg me-2">
                        <i class="fas fa-shopping-cart"></i> 立即购买
                    </a>
                    <button class="btn btn-outline-danger favorite-btn me-2" data-product-id="{{ product.id }}">
                        <i class="{% if is_favorited %}fas{% else %}far{% endif %} fa-heart"></i>
                        <span class="favorite-count">{{ product.favorite_count }}</span>
                    </button>
                    <a href="{{ url_for('messages.start_chat', product_id=product.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-comment"></i> 联系卖家
                    </a>
                    {% endif %}
                {% else %}
                <a href="{{ url_for('auth.login') }}" class="btn btn-success btn-lg">
                    <i class="fas fa-sign-in-alt"></i> 登录后购买
                </a>
                {% endif %}
            </div>
            
            <!-- 商品状态 -->
            <div class="product-status mb-3">
                {% if product.status.value == 'pending' %}
                <span class="badge bg-warning">待审核</span>
                {% elif product.status.value == 'active' %}
                <span class="badge bg-success">在售中</span>
                {% elif product.status.value == 'sold' %}
                <span class="badge bg-secondary">已售出</span>
                {% elif product.status.value == 'inactive' %}
                <span class="badge bg-danger">已下架</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 商品详情 -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">商品详情</h5>
            </div>
            <div class="card-body">
                <div class="product-description">
                    {{ product.description|nl2br }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 相关商品 -->
{% if related_products %}
<div class="row mt-5">
    <div class="col-12">
        <h4 class="mb-4">相关商品</h4>
        <div class="row">
            {% for related_product in related_products %}
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100 product-card">
                    <a href="{{ url_for('products.detail', id=related_product.id) }}">
                        <img src="{{ related_product.get_main_image_url() }}"
                             class="card-img-top" alt="{{ related_product.title }}" style="height: 200px; object-fit: cover;">
                    </a>
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">
                            <a href="{{ url_for('products.detail', id=related_product.id) }}" class="text-decoration-none">
                                {{ related_product.title[:30] }}{% if related_product.title|length > 30 %}...{% endif %}
                            </a>
                        </h6>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-danger fw-bold">¥{{ "%.2f"|format(related_product.price) }}</span>
                                <small class="text-muted">
                                    <i class="fas fa-eye"></i> {{ related_product.view_count }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
// 测试JavaScript是否正常执行
console.log('JavaScript开始执行');

// 确保jQuery已加载
if (typeof $ === 'undefined') {
    console.error('jQuery未加载！');
} else {
    console.log('jQuery已正确加载');
}

$(document).ready(function() {
    console.log('DOM已准备就绪');

    // 异步更新浏览量 - 所有用户都会增加浏览量
    console.log('页面加载完成，准备更新浏览量');

    // 防重复请求机制
    var viewUpdateInProgress = false;
    var maxRetries = 3;
    var retryCount = 0;

    function updateViewCount() {
        console.log('开始执行updateViewCount函数');

        if (viewUpdateInProgress) {
            console.log('浏览量更新请求正在进行中，跳过重复请求');
            return;
        }

        viewUpdateInProgress = true;
        console.log('发送浏览量更新请求到:', '/api/products/{{ product.id }}/view');

        $.ajax({
            url: '/api/products/{{ product.id }}/view',
            method: 'POST',
            data: {
                csrf_token: $('meta[name=csrf-token]').attr('content')
            },
            timeout: 10000, // 10秒超时
            success: function(response) {
                viewUpdateInProgress = false;
                retryCount = 0; // 重置重试计数

                if (response.success) {
                    if (response.view_count) {
                        // 更新页面上的浏览量显示，添加动画效果
                        var $viewCount = $('.view-count');
                        $viewCount.fadeOut(200, function() {
                            $viewCount.text(response.view_count).fadeIn(200);
                        });
                    }

                    // 记录成功日志
                    console.log('浏览量更新成功:', response.message);
                } else {
                    console.warn('浏览量更新响应失败:', response.message);
                }
            },
            error: function(xhr, status, error) {
                viewUpdateInProgress = false;

                // 详细的错误日志
                console.error('浏览量更新失败:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    error: error,
                    responseText: xhr.responseText
                });

                // 重试机制
                if (retryCount < maxRetries && xhr.status !== 404) {
                    retryCount++;
                    console.log(`浏览量更新失败，${2000 * retryCount}ms后进行第${retryCount}次重试`);

                    setTimeout(function() {
                        updateViewCount();
                    }, 2000 * retryCount); // 递增延迟重试
                } else {
                    console.error('浏览量更新最终失败，已达到最大重试次数或遇到不可重试的错误');
                }
            }
        });
    }

    // 页面加载完成后延迟执行，避免影响页面渲染
    setTimeout(updateViewCount, 500);

    // 缩略图点击切换主图
    $('.thumbnail-image').click(function() {
        var slideIndex = $(this).data('bs-slide-to');
        $('#productCarousel').carousel(slideIndex);
    });
    
    // 收藏功能
    $('.favorite-btn').click(function(e) {
        e.preventDefault();
        var btn = $(this);
        var productId = btn.data('product-id');

        // 防止重复点击
        if (btn.hasClass('processing')) {
            return;
        }

        // 检查用户是否登录
        {% if current_user.is_authenticated %}

        // 设置处理状态
        btn.addClass('processing');
        var originalHtml = btn.html();
        btn.html('<i class="fas fa-spinner fa-spin"></i> 处理中...');
        btn.prop('disabled', true);

        $.ajax({
            url: '/products/toggle_favorite/' + productId,
            method: 'POST',
            data: {
                csrf_token: $('meta[name=csrf-token]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    if (response.is_favorited) {
                        btn.removeClass('btn-outline-danger').addClass('btn-danger');
                        btn.html('<i class="fas fa-heart"></i> <span class="favorite-count">' + response.favorite_count + '</span>');
                    } else {
                        btn.removeClass('btn-danger').addClass('btn-outline-danger');
                        btn.html('<i class="far fa-heart"></i> <span class="favorite-count">' + response.favorite_count + '</span>');
                    }
                    showAlert(response.message, 'success');
                } else {
                    btn.html(originalHtml);
                    showAlert('操作失败：' + response.message, 'error');
                }
            },
            error: function(xhr) {
                console.error('Favorite error:', xhr);
                btn.html(originalHtml);
                if (xhr.status === 401) {
                    showAlert('请先登录', 'warning');
                    window.location.href = '/auth/login';
                } else {
                    showAlert('操作失败，请重试', 'error');
                }
            },
            complete: function() {
                // 恢复按钮状态
                btn.removeClass('processing');
                btn.prop('disabled', false);
            }
        });
        {% else %}
        showAlert('请先登录', 'warning');
        window.location.href = '/auth/login';
        {% endif %}
    });
});

function showAlert(message, type) {
    var alertClass = type === 'error' ? 'alert-danger' : 'alert-' + type;
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 80px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    // 将消息提示添加到body中，避免影响导航栏
    $('body').append(alertHtml);
    setTimeout(function() {
        $('.alert').last().alert('close');
    }, 3000);
}
</script>
{% endblock %}
