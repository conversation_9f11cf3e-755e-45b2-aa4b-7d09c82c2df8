package com.second.hand.data.repository

import com.second.hand.data.api.ApiService
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.auth.TokenManager
import com.second.hand.data.model.User
import com.second.hand.data.model.auth.LoginRequest
import com.second.hand.data.model.auth.LoginResponse
import com.second.hand.data.model.auth.RegisterRequest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 认证数据仓库
 * 处理用户认证相关的数据操作
 */
@Singleton
class AuthRepository @Inject constructor(
    private val apiService: ApiService,
    private val tokenManager: TokenManager
) : BaseRepository() {
    
    /**
     * 用户登录
     */
    suspend fun login(loginRequest: LoginRequest): NetworkResult<LoginResponse> {
        val result = safeApiCall {
            apiService.login(loginRequest)
        }

        // 如果登录成功，保存Token信息
        if (result is NetworkResult.Success) {
            val loginResponse = result.data
            val userInfo: Map<String, Any> = mapOf(
                "id" to loginResponse.user.id as Any,
                "username" to (loginResponse.user.username ?: "") as Any,
                "email" to (loginResponse.user.email ?: "") as Any,
                "nickname" to (loginResponse.user.nickname ?: "") as Any,
                "role" to loginResponse.user.role.name as Any
            )
            tokenManager.saveTokens(
                accessToken = loginResponse.token,
                refreshToken = loginResponse.refreshToken,
                userInfo = userInfo
            )
        }

        return result
    }
    
    /**
     * 用户注册
     */
    suspend fun register(registerRequest: RegisterRequest): NetworkResult<LoginResponse> {
        val result = safeApiCall {
            apiService.register(registerRequest)
        }

        // 如果注册成功，保存Token信息
        if (result is NetworkResult.Success) {
            val loginResponse = result.data
            val userInfo: Map<String, Any> = mapOf(
                "id" to loginResponse.user.id as Any,
                "username" to (loginResponse.user.username ?: "") as Any,
                "email" to (loginResponse.user.email ?: "") as Any,
                "nickname" to (loginResponse.user.nickname ?: "") as Any,
                "role" to loginResponse.user.role.name as Any
            )
            tokenManager.saveTokens(
                accessToken = loginResponse.token,
                refreshToken = loginResponse.refreshToken,
                userInfo = userInfo
            )
        }

        return result
    }
    
    /**
     * 用户登出
     */
    suspend fun logout(): NetworkResult<Boolean> {
        val result = safeApiCallNoData {
            apiService.logout()
        }

        // 无论API调用是否成功，都清除本地Token
        tokenManager.clearTokens()

        return result
    }
    
    /**
     * 获取用户信息
     */
    suspend fun getProfile(): NetworkResult<User> {
        return safeApiCall {
            apiService.getProfile()
        }
    }
    
    /**
     * 更新用户信息
     */
    suspend fun updateProfile(user: User): NetworkResult<User> {
        return safeApiCall {
            apiService.updateProfile(user)
        }
    }
    
    /**
     * 刷新Token
     */
    suspend fun refreshToken(): NetworkResult<LoginResponse> {
        val refreshToken = tokenManager.getRefreshToken()
        if (refreshToken.isNullOrEmpty()) {
            return NetworkResult.Error("刷新Token不存在")
        }

        val result = safeApiCall {
            apiService.refreshToken()
        }

        // 如果刷新成功，更新Token信息
        if (result is NetworkResult.Success) {
            val loginResponse = result.data
            val userInfo: Map<String, Any> = tokenManager.getUserInfo() ?: mapOf(
                "id" to loginResponse.user.id as Any,
                "username" to (loginResponse.user.username ?: "") as Any,
                "email" to (loginResponse.user.email ?: "") as Any,
                "nickname" to (loginResponse.user.nickname ?: "") as Any,
                "role" to loginResponse.user.role.name as Any
            )
            tokenManager.saveTokens(
                accessToken = loginResponse.token,
                refreshToken = loginResponse.refreshToken,
                userInfo = userInfo
            )
        }

        return result
    }

    /**
     * 检查Token有效性
     */
    fun isTokenValid(): Boolean {
        return tokenManager.isTokenValid()
    }

    /**
     * 检查是否需要刷新Token
     */
    fun shouldRefreshToken(): Boolean {
        return tokenManager.shouldRefreshToken()
    }

    /**
     * 获取Token状态
     */
    fun getTokenState() = tokenManager.tokenState
}
