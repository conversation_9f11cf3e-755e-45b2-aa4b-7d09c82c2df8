{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-chart-bar me-2"></i>详细统计</h2>
        <div>
            <button class="btn btn-outline-primary" onclick="refreshStats()">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
        </div>
    </div>

    <!-- 实时系统状态 -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-server me-2"></i>系统状态</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="h3 mb-0 text-success" id="server-status">在线</div>
                            <small class="text-muted">服务状态</small>
                        </div>
                        <div class="col-md-3">
                            <div class="h3 mb-0 text-primary" id="db-status">正常</div>
                            <small class="text-muted">数据库</small>
                        </div>
                        <div class="col-md-3">
                            <div class="h3 mb-0 text-warning" id="disk-usage">85%</div>
                            <small class="text-muted">磁盘使用</small>
                        </div>
                        <div class="col-md-3">
                            <div class="h3 mb-0 text-info" id="memory-usage">2.1GB</div>
                            <small class="text-muted">内存使用</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户统计 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">用户统计</h5>
                </div>
                <div class="card-body">
                    <canvas id="userChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">商品统计</h5>
                </div>
                <div class="card-body">
                    <canvas id="productChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单统计 -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">订单趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="orderChart" width="800" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">热门商品分类</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>分类</th>
                                    <th>商品数量</th>
                                    <th>占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in category_stats %}
                                <tr>
                                    <td>{{ category.name }}</td>
                                    <td>{{ category.product_count }}</td>
                                    <td>{{ "%.1f"|format(category.percentage) }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">活跃用户排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>用户</th>
                                    <th>发布商品</th>
                                    <th>交易次数</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in active_users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ user.get_avatar_url() }}" 
                                                 class="rounded-circle me-2" 
                                                 width="24" height="24" alt="头像">
                                            {{ user.username }}
                                        </div>
                                    </td>
                                    <td>{{ user.product_count }}</td>
                                    <td>{{ user.order_count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 用户统计图表
const userCtx = document.getElementById('userChart').getContext('2d');
const userChart = new Chart(userCtx, {
    type: 'doughnut',
    data: {
        labels: ['活跃用户', '非活跃用户'],
        datasets: [{
            data: [{{ stats.active_users }}, {{ stats.inactive_users }}],
            backgroundColor: ['#28a745', '#6c757d']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// 商品统计图表
const productCtx = document.getElementById('productChart').getContext('2d');
const productChart = new Chart(productCtx, {
    type: 'pie',
    data: {
        labels: ['在售', '已售出', '已下架'],
        datasets: [{
            data: [{{ stats.active_products }}, {{ stats.sold_products }}, {{ stats.inactive_products }}],
            backgroundColor: ['#007bff', '#28a745', '#ffc107']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// 订单趋势图表
const orderCtx = document.getElementById('orderChart').getContext('2d');
const orderChart = new Chart(orderCtx, {
    type: 'line',
    data: {
        labels: {{ order_trend_labels|safe }},
        datasets: [{
            label: '订单数量',
            data: {{ order_trend_data|safe }},
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function refreshStats() {
    location.reload();
}

// 定时刷新系统状态
function updateSystemStatus() {
    fetch('/admin_panel/api/system_status')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // 更新系统状态显示
            const serverStatus = document.getElementById('server-status');
            const dbStatus = document.getElementById('db-status');
            const diskUsage = document.getElementById('disk-usage');
            const memoryUsage = document.getElementById('memory-usage');

            if (serverStatus) serverStatus.textContent = data.server_status || '在线';
            if (dbStatus) dbStatus.textContent = data.db_status || '正常';
            if (diskUsage) diskUsage.textContent = data.disk_usage || '--';
            if (memoryUsage) memoryUsage.textContent = data.memory_usage || '--';
        })
        .catch(error => {
            console.error('获取系统状态失败:', error);
            // 显示错误状态
            const serverStatus = document.getElementById('server-status');
            const dbStatus = document.getElementById('db-status');
            if (serverStatus) serverStatus.textContent = '错误';
            if (dbStatus) dbStatus.textContent = '错误';
        });
}

// 立即更新一次
updateSystemStatus();

// 每5秒刷新一次，与系统监控页面保持一致
setInterval(updateSystemStatus, 5000);
</script>
{% endblock %}
{% endblock %}
