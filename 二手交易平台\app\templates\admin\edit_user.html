{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-edit me-2"></i>编辑用户</h2>
        <a href="{{ url_for('admin_panel.users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回用户列表
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">用户信息</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control") }}
                                {% if form.username.errors %}
                                    {% for error in form.username.errors %}
                                        <div class="text-danger small">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control") }}
                                {% if form.email.errors %}
                                    {% for error in form.email.errors %}
                                        <div class="text-danger small">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.nickname.label(class="form-label") }}
                                {{ form.nickname(class="form-control") }}
                                {% if form.nickname.errors %}
                                    {% for error in form.nickname.errors %}
                                        <div class="text-danger small">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.phone.label(class="form-label") }}
                                {{ form.phone(class="form-control") }}
                                {% if form.phone.errors %}
                                    {% for error in form.phone.errors %}
                                        <div class="text-danger small">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.bio.label(class="form-label") }}
                            {{ form.bio(class="form-control", rows="3") }}
                            {% if form.bio.errors %}
                                {% for error in form.bio.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.role.label(class="form-label") }}
                                {{ form.role(class="form-select") }}
                                {% if form.role.errors %}
                                    {% for error in form.role.errors %}
                                        <div class="text-danger small">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">账户状态</label>
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input", id="is_active") }}
                                    <label class="form-check-label" for="is_active">
                                        <span id="active-status-text">
                                            {% if form.is_active.data %}启用账户{% else %}禁用账户{% endif %}
                                        </span>
                                    </label>
                                    <div class="form-text">
                                        <small class="text-muted" id="active-status-help">
                                            {% if form.is_active.data %}
                                            账户已启用，用户可以正常登录和使用系统
                                            {% else %}
                                            账户已禁用，用户无法登录系统
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                                <div class="form-check mt-3">
                                    {{ form.email_verified(class="form-check-input", id="email_verified") }}
                                    <label class="form-check-label" for="email_verified">
                                        <span id="email-status-text">
                                            {% if form.email_verified.data %}邮箱已验证{% else %}邮箱未验证{% endif %}
                                        </span>
                                    </label>
                                    <div class="form-text">
                                        <small class="text-muted" id="email-status-help">
                                            {% if form.email_verified.data %}
                                            用户邮箱已通过验证
                                            {% else %}
                                            用户邮箱尚未验证
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存更改
                            </button>
                            <a href="{{ url_for('admin_panel.users') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- 用户头像 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">用户头像</h6>
                </div>
                <div class="card-body text-center">
                    <img src="{{ user.get_avatar_url() }}" 
                         class="rounded-circle mb-3" 
                         width="120" height="120" alt="用户头像">
                    <p class="text-muted small">头像文件: {{ user.avatar or '默认头像' }}</p>
                </div>
            </div>

            <!-- 用户统计 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">用户统计</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h4 mb-0 text-primary">{{ user.products.count() }}</div>
                            <small class="text-muted">发布商品</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-success">{{ user.orders_as_buyer.count() }}</div>
                            <small class="text-muted">购买订单</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h4 mb-0 text-warning">{{ user.orders_as_seller.count() }}</div>
                            <small class="text-muted">销售订单</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-info">{{ user.reviews_received.count() }}</div>
                            <small class="text-muted">收到评价</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 账户信息 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">账户信息</h6>
                </div>
                <div class="card-body">
                    <p><strong>用户ID:</strong> {{ user.id }}</p>
                    <p><strong>注册时间:</strong><br>
                       <small>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small></p>
                    <p><strong>最后更新:</strong><br>
                       <small>{{ user.updated_at.strftime('%Y-%m-%d %H:%M:%S') if user.updated_at else '无' }}</small></p>
                    <p><strong>最后活跃:</strong><br>
                       <small>{{ user.last_seen.strftime('%Y-%m-%d %H:%M:%S') if user.last_seen else '从未' }}</small></p>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
$(document).ready(function() {
    // 账户状态切换处理
    $('#is_active').change(function() {
        const isChecked = $(this).is(':checked');
        const statusText = $('#active-status-text');
        const statusHelp = $('#active-status-help');

        if (isChecked) {
            statusText.text('启用账户');
            statusHelp.text('账户已启用，用户可以正常登录和使用系统');
            statusHelp.removeClass('text-danger').addClass('text-muted');
        } else {
            statusText.text('禁用账户');
            statusHelp.text('账户已禁用，用户无法登录系统');
            statusHelp.removeClass('text-muted').addClass('text-danger');
        }
    });

    // 邮箱验证状态切换处理
    $('#email_verified').change(function() {
        const isChecked = $(this).is(':checked');
        const statusText = $('#email-status-text');
        const statusHelp = $('#email-status-help');

        if (isChecked) {
            statusText.text('邮箱已验证');
            statusHelp.text('用户邮箱已通过验证');
            statusHelp.removeClass('text-warning').addClass('text-muted');
        } else {
            statusText.text('邮箱未验证');
            statusHelp.text('用户邮箱尚未验证');
            statusHelp.removeClass('text-muted').addClass('text-warning');
        }
    });

    // 表单提交前确认
    $('form').submit(function(e) {
        const isActive = $('#is_active').is(':checked');
        const emailVerified = $('#email_verified').is(':checked');

        if (!isActive) {
            if (!confirm('确定要禁用该用户账户吗？禁用后用户将无法登录系统。')) {
                e.preventDefault();
                return false;
            }
        }

        // 显示提交状态
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');
    });
});
</script>
{% endblock %}
{% endblock %}
