#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控工具
"""

import time
import functools
import logging
from datetime import datetime
from typing import Dict, Any, Callable
from collections import defaultdict, deque


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_records=1000):
        self.max_records = max_records
        self.metrics = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0,
            'avg_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'recent_times': deque(maxlen=100),  # 保留最近100次调用时间
            'error_count': 0
        })
        self.logger = logging.getLogger(__name__)
    
    def monitor(self, name: str = None):
        """性能监控装饰器"""
        def decorator(func: Callable):
            monitor_name = name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    self._record_success(monitor_name, start_time)
                    return result
                except Exception as e:
                    self._record_error(monitor_name, start_time)
                    raise
            
            return wrapper
        return decorator
    
    def _record_success(self, name: str, start_time: float):
        """记录成功的调用"""
        execution_time = time.time() - start_time
        metrics = self.metrics[name]
        
        metrics['call_count'] += 1
        metrics['total_time'] += execution_time
        metrics['avg_time'] = metrics['total_time'] / metrics['call_count']
        metrics['min_time'] = min(metrics['min_time'], execution_time)
        metrics['max_time'] = max(metrics['max_time'], execution_time)
        metrics['recent_times'].append(execution_time)
        
        # 记录慢查询
        if execution_time > 1.0:  # 超过1秒的调用
            self.logger.warning(f"慢调用检测: {name} 耗时 {execution_time:.3f}s")
    
    def _record_error(self, name: str, start_time: float):
        """记录失败的调用"""
        execution_time = time.time() - start_time
        metrics = self.metrics[name]
        
        metrics['error_count'] += 1
        self.logger.error(f"调用失败: {name} 耗时 {execution_time:.3f}s")
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        result = {}
        for name, metrics in self.metrics.items():
            # 计算最近调用的平均时间
            recent_avg = 0
            if metrics['recent_times']:
                recent_avg = sum(metrics['recent_times']) / len(metrics['recent_times'])
            
            result[name] = {
                'call_count': metrics['call_count'],
                'error_count': metrics['error_count'],
                'error_rate': metrics['error_count'] / max(metrics['call_count'], 1),
                'avg_time': round(metrics['avg_time'], 4),
                'min_time': round(metrics['min_time'], 4) if metrics['min_time'] != float('inf') else 0,
                'max_time': round(metrics['max_time'], 4),
                'recent_avg_time': round(recent_avg, 4),
                'total_time': round(metrics['total_time'], 4)
            }
        
        return result
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        metrics = self.get_metrics()
        
        if not metrics:
            return {
                'total_calls': 0,
                'total_errors': 0,
                'overall_error_rate': 0,
                'slowest_function': None,
                'most_called_function': None
            }
        
        total_calls = sum(m['call_count'] for m in metrics.values())
        total_errors = sum(m['error_count'] for m in metrics.values())
        
        # 找出最慢的函数
        slowest = max(metrics.items(), key=lambda x: x[1]['avg_time'])
        
        # 找出调用最多的函数
        most_called = max(metrics.items(), key=lambda x: x[1]['call_count'])
        
        return {
            'total_calls': total_calls,
            'total_errors': total_errors,
            'overall_error_rate': total_errors / max(total_calls, 1),
            'slowest_function': {
                'name': slowest[0],
                'avg_time': slowest[1]['avg_time']
            },
            'most_called_function': {
                'name': most_called[0],
                'call_count': most_called[1]['call_count']
            },
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def reset_metrics(self):
        """重置性能指标"""
        self.metrics.clear()
        self.logger.info("性能监控指标已重置")


# 全局性能监控实例
performance_monitor = PerformanceMonitor()


def monitor_performance(name: str = None):
    """性能监控装饰器的便捷函数"""
    return performance_monitor.monitor(name)


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, default_ttl=300):  # 默认5分钟过期
        self.cache = {}
        self.default_ttl = default_ttl
        self.logger = logging.getLogger(__name__)
    
    def get(self, key: str):
        """获取缓存值"""
        if key in self.cache:
            value, expiry = self.cache[key]
            if time.time() < expiry:
                self.logger.debug(f"缓存命中: {key}")
                return value
            else:
                # 缓存过期，删除
                del self.cache[key]
                self.logger.debug(f"缓存过期: {key}")
        
        self.logger.debug(f"缓存未命中: {key}")
        return None
    
    def set(self, key: str, value: Any, ttl: int = None):
        """设置缓存值"""
        ttl = ttl or self.default_ttl
        expiry = time.time() + ttl
        self.cache[key] = (value, expiry)
        self.logger.debug(f"缓存设置: {key}, TTL: {ttl}s")
    
    def delete(self, key: str):
        """删除缓存值"""
        if key in self.cache:
            del self.cache[key]
            self.logger.debug(f"缓存删除: {key}")
    
    def clear(self):
        """清空所有缓存"""
        self.cache.clear()
        self.logger.info("所有缓存已清空")
    
    def cleanup_expired(self):
        """清理过期的缓存项"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, expiry) in self.cache.items()
            if current_time >= expiry
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            self.logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        active_count = sum(
            1 for _, expiry in self.cache.values()
            if current_time < expiry
        )
        
        return {
            'total_items': len(self.cache),
            'active_items': active_count,
            'expired_items': len(self.cache) - active_count,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }


# 全局缓存管理器实例
cache_manager = CacheManager()


def cached(key_func: Callable = None, ttl: int = None):
    """缓存装饰器"""
    def decorator(func: Callable):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            return result
        
        return wrapper
    return decorator


def get_performance_report() -> Dict[str, Any]:
    """获取完整的性能报告"""
    return {
        'performance_metrics': performance_monitor.get_metrics(),
        'performance_summary': performance_monitor.get_summary(),
        'cache_stats': cache_manager.get_stats(),
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }


def optimize_system_status_cache():
    """优化系统状态缓存"""
    @cached(ttl=30)  # 缓存30秒
    @monitor_performance('system_status_cached')
    def get_cached_system_status():
        from app.utils.system_monitor import get_simplified_system_status
        return get_simplified_system_status()
    
    return get_cached_system_status


def optimize_online_users_cache():
    """优化在线用户数缓存"""
    @cached(ttl=60)  # 缓存60秒
    @monitor_performance('online_users_cached')
    def get_cached_online_users():
        from app.utils.datetime_utils import get_online_users_count
        return get_online_users_count()
    
    return get_cached_online_users
