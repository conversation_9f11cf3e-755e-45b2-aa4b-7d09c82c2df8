package com.second.hand

import android.app.Application
import dagger.hilt.android.HiltAndroidApp

/**
 * 二手交易平台应用程序入口
 * 使用Hilt进行依赖注入
 */
@HiltAndroidApp
class SecondHandApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 应用初始化逻辑
        initializeApp()
    }
    
    private fun initializeApp() {
        // 强制使用IPv4，解决DNS解析问题
        System.setProperty("java.net.preferIPv4Stack", "true")
        System.setProperty("java.net.preferIPv6Addresses", "false")

        // 网络调试日志
        println("🚀 SecondHandApplication 初始化完成")
        println("🌐 网络配置: IPv4优先")
        println("🌐 Base URL: ${BuildConfig.BASE_URL}")
        println("🌐 Image URL: ${BuildConfig.IMAGE_BASE_URL}")

        // 这里可以添加应用启动时的初始化逻辑
        // 例如：日志配置、崩溃报告、性能监控等
    }
}
