{% extends "base.html" %}

{% block content %}
<div class="row">
    <!-- 用户信息卡片 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                <img src="{{ user.get_avatar_url() }}" class="rounded-circle mb-3 user-avatar user-avatar-lg"
                     alt="用户头像">
                <h4 class="card-title">{{ user.nickname or user.username }}</h4>
                <p class="text-muted">@{{ user.username }}</p>
                
                {% if user.bio %}
                <p class="card-text">{{ user.bio }}</p>
                {% endif %}
                
                <div class="row text-center mt-4">
                    <div class="col-4">
                        <div class="h5 mb-0 text-primary">{{ user.products.count() }}</div>
                        <small class="text-muted">发布商品</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 mb-0 text-success">{{ user.orders_as_seller.count() }}</div>
                        <small class="text-muted">销售订单</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 mb-0 text-info">{{ user.reviews_received.count() }}</div>
                        <small class="text-muted">收到评价</small>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i>编辑资料
                    </a>
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-key me-1"></i>修改密码
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 账户信息 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> 账户信息</h6>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-5"><strong>邮箱:</strong></div>
                    <div class="col-7">
                        {{ user.email }}
                        {% if user.email_verified %}
                        <i class="fas fa-check-circle text-success" title="已验证"></i>
                        {% else %}
                        <i class="fas fa-exclamation-circle text-warning" title="未验证"></i>
                        {% endif %}
                    </div>
                </div>
                
                {% if user.phone %}
                <div class="row mb-2">
                    <div class="col-5"><strong>手机:</strong></div>
                    <div class="col-7">{{ user.phone }}</div>
                </div>
                {% endif %}
                
                <div class="row mb-2">
                    <div class="col-5"><strong>注册时间:</strong></div>
                    <div class="col-7">{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '未知' }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-5"><strong>最后登录:</strong></div>
                    <div class="col-7">{{ user.last_seen.strftime('%Y-%m-%d %H:%M') if user.last_seen else '从未' }}</div>
                </div>
                
                <div class="row">
                    <div class="col-5"><strong>账户状态:</strong></div>
                    <div class="col-7">
                        {% if user.is_active %}
                        <span class="badge bg-success">正常</span>
                        {% else %}
                        <span class="badge bg-danger">已禁用</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="col-lg-8">
        <!-- 导航标签 -->
        <ul class="nav nav-tabs" id="profileTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="products-tab" data-bs-toggle="tab" 
                        data-bs-target="#products" type="button" role="tab">
                    <i class="fas fa-box"></i> 我的商品
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="orders-tab" data-bs-toggle="tab" 
                        data-bs-target="#orders" type="button" role="tab">
                    <i class="fas fa-shopping-cart"></i> 我的订单
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" 
                        data-bs-target="#reviews" type="button" role="tab">
                    <i class="fas fa-star"></i> 我的评价
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="favorites-tab" data-bs-toggle="tab" 
                        data-bs-target="#favorites" type="button" role="tab">
                    <i class="fas fa-heart"></i> 我的收藏
                </button>
            </li>
        </ul>
        
        <!-- 标签内容 -->
        <div class="tab-content" id="profileTabsContent">
            <!-- 我的商品 -->
            <div class="tab-pane fade show active" id="products" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">最近发布的商品</h6>
                        <a href="{{ url_for('products.my_products') }}" class="btn btn-sm btn-outline-primary">
                            查看全部
                        </a>
                    </div>
                    <div class="card-body">
                        {% if recent_products %}
                        <div class="row">
                            {% for product in recent_products %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <img src="{{ product.get_main_image_url() }}"
                                         class="rounded me-3" width="80" height="80" style="object-fit: cover;" alt="{{ product.title }}">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="{{ url_for('products.detail', id=product.id) }}" class="text-decoration-none">
                                                {{ product.title[:30] }}{% if product.title|length > 30 %}...{% endif %}
                                            </a>
                                        </h6>
                                        <p class="text-danger fw-bold mb-1">¥{{ "%.2f"|format(product.price) }}</p>
                                        <small class="text-muted">
                                            {% if product.status.value == 'pending' %}
                                            <span class="badge bg-warning">待审核</span>
                                            {% elif product.status.value == 'active' %}
                                            <span class="badge bg-success">在售</span>
                                            {% elif product.status.value == 'sold' %}
                                            <span class="badge bg-secondary">已售</span>
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">还没有发布商品</p>
                            <a href="{{ url_for('products.publish') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 发布商品
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- 我的订单 -->
            <div class="tab-pane fade" id="orders" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">最近订单</h6>
                        <a href="{{ url_for('orders.my_orders') }}" class="btn btn-sm btn-outline-primary">
                            查看全部
                        </a>
                    </div>
                    <div class="card-body">
                        {% if recent_orders %}
                        <div class="list-group list-group-flush">
                            {% for order in recent_orders %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ order.product.title[:40] }}{% if order.product.title|length > 40 %}...{% endif %}</h6>
                                    <p class="mb-1">订单号: {{ order.order_no }}</p>
                                    <small class="text-muted">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold text-danger">¥{{ "%.2f"|format(order.total_amount) }}</div>
                                    <small>
                                        {% if order.status.value == 'pending' %}
                                        <span class="badge bg-warning">待付款</span>
                                        {% elif order.status.value == 'paid' %}
                                        <span class="badge bg-info">已付款</span>
                                        {% elif order.status.value == 'shipped' %}
                                        <span class="badge bg-primary">已发货</span>
                                        {% elif order.status.value == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                        {% elif order.status.value == 'cancelled' %}
                                        <span class="badge bg-secondary">已取消</span>
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <p class="text-muted">还没有订单</p>
                            <a href="{{ url_for('main.search') }}" class="btn btn-primary">
                                <i class="fas fa-search"></i> 去购物
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- 我的评价 -->
            <div class="tab-pane fade" id="reviews" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">收到的评价</h6>
                    </div>
                    <div class="card-body">
                        {% if recent_reviews %}
                        {% for review in recent_reviews %}
                        <div class="border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="rating text-warning me-3">
                                            {% for i in range(5) %}
                                            <i class="fas fa-star{% if i >= review.rating %} text-muted{% endif %}"></i>
                                            {% endfor %}
                                            <span class="ms-1 text-muted">({{ review.rating }}/5)</span>
                                        </div>
                                        {% if review.order and review.order.product %}
                                        <span class="badge bg-secondary">{{ review.order.product.title[:20] }}{% if review.order.product.title|length > 20 %}...{% endif %}</span>
                                        {% endif %}
                                    </div>
                                    <p class="mb-2">{{ review.content or '用户没有留下评价内容' }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-user"></i> {{ review.reviewer.nickname or review.reviewer.username }} ·
                                            <i class="fas fa-calendar"></i> {{ review.created_at.strftime('%Y-%m-%d %H:%M') }}
                                            {% if review.order %}
                                            · <i class="fas fa-shopping-cart"></i> 订单号: {{ review.order.id }}
                                            {% endif %}
                                        </small>
                                        {% if review.order and review.order.product %}
                                        <a href="{{ url_for('products.detail', id=review.order.product.id) }}"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> 查看商品
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                            <p class="text-muted">还没有收到评价</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- 我的收藏 -->
            <div class="tab-pane fade" id="favorites" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">最近收藏</h6>
                        <a href="{{ url_for('products.favorites') }}" class="btn btn-sm btn-outline-primary">
                            查看全部
                        </a>
                    </div>
                    <div class="card-body">
                        {% if recent_favorites %}
                        <div class="row">
                            {% for product in recent_favorites %}
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="row g-0">
                                        <div class="col-4">
                                            <img src="{{ product.get_main_image_url() }}"
                                                 class="img-fluid rounded-start h-100"
                                                 style="object-fit: cover;" alt="商品图片">
                                        </div>
                                        <div class="col-8">
                                            <div class="card-body p-2">
                                                <h6 class="card-title mb-1">
                                                    <a href="{{ url_for('products.detail', id=product.id) }}"
                                                       class="text-decoration-none">
                                                        {{ product.title[:20] }}{% if product.title|length > 20 %}...{% endif %}
                                                    </a>
                                                </h6>
                                                <p class="card-text small text-muted mb-1">
                                                    {{ product.description[:30] }}{% if product.description|length > 30 %}...{% endif %}
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="text-danger fw-bold small">¥{{ "%.2f"|format(product.price) }}</span>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                            onclick="toggleFavoriteInProfile({{ product.id }}, this)"
                                                            title="取消收藏">
                                                        <i class="fas fa-heart"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                            <p class="text-muted">还没有收藏任何商品</p>
                            <a href="{{ url_for('main.search') }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-search me-1"></i>去逛逛
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 标签切换时加载数据
    $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).data('bs-target');
        // 这里可以添加异步加载数据的逻辑
    });
});

// 个人资料页面的收藏功能
function toggleFavoriteInProfile(productId, button) {
    $.ajax({
        url: `/products/toggle_favorite/${productId}`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                // 从个人资料页面移除该商品卡片
                $(button).closest('.col-md-6').fadeOut(function() {
                    $(this).remove();
                    // 如果没有收藏商品了，显示空状态
                    if ($('#favorites .col-md-6').length === 0) {
                        $('#favorites .card-body').html(`
                            <div class="text-center py-4">
                                <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                                <p class="text-muted">还没有收藏任何商品</p>
                                <a href="/search" class="btn btn-sm btn-primary">
                                    <i class="fas fa-search me-1"></i>去逛逛
                                </a>
                            </div>
                        `);
                    }
                });
                showAlert(response.message, 'success');
            }
        },
        error: function() {
            showAlert('操作失败，请重试', 'error');
        }
    });
}
</script>
{% endblock %}
