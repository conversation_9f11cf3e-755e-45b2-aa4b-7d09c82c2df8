package com.second.hand.ui.debug

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.second.hand.ui.auth.AuthViewModel
import com.second.hand.ui.components.SuccessMessageCard
import com.second.hand.ui.components.InfoMessageCard

/**
 * 认证功能测试界面
 * 用于测试登录注册功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AuthTestScreen(
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val authUiState by authViewModel.uiState.collectAsState()
    val tokenState by authViewModel.getTokenState().collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "🔐 登录注册功能测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        // 当前状态卡片
        CurrentStatusCard(authUiState, tokenState)
        
        // 快速测试按钮
        QuickTestButtons(authViewModel)
        
        // 测试结果显示
        TestResultsCard(authUiState)
        
        // 功能说明
        FeatureInfoCard()
    }
}

@Composable
private fun CurrentStatusCard(
    authUiState: com.second.hand.ui.auth.AuthUiState,
    tokenState: com.second.hand.data.auth.TokenState
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "当前状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            StatusRow("登录状态", if (authUiState.isLoggedIn) "已登录" else "未登录")
            StatusRow("当前模式", if (authUiState.isLoginMode) "登录模式" else "注册模式")
            StatusRow("Token有效", if (tokenState.isValid) "有效" else "无效")
            
            authUiState.currentUser?.let { user ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "用户信息:",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Bold
                )
                StatusRow("用户名", user.username)
                StatusRow("邮箱", user.email ?: "未设置")
                StatusRow("昵称", user.nickname ?: "未设置")
                StatusRow("角色", user.role.name)
            }
        }
    }
}

@Composable
private fun StatusRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun QuickTestButtons(authViewModel: AuthViewModel) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "快速测试",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            // 测试登录按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { 
                        authViewModel.login("testuser", "123456") 
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("测试登录")
                }
                
                Button(
                    onClick = { 
                        authViewModel.register(
                            username = "newuser${System.currentTimeMillis() % 1000}",
                            email = "test${System.currentTimeMillis() % 1000}@example.com",
                            password = "123456",
                            confirmPassword = "123456",
                            emailCode = "123456"
                        )
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("测试注册")
                }
            }
            
            // 其他操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { authViewModel.logout() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("登出")
                }
                
                Button(
                    onClick = { authViewModel.toggleAuthMode() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("切换模式")
                }
            }
        }
    }
}

@Composable
private fun TestResultsCard(authUiState: com.second.hand.ui.auth.AuthUiState) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "测试结果",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            if (authUiState.isLoggedIn) {
                SuccessMessageCard(
                    message = "✅ 登录/注册成功！Token管理正常工作。"
                )
            } else {
                InfoMessageCard(
                    message = "ℹ️ 当前未登录，可以测试登录或注册功能。"
                )
            }
        }
    }
}

@Composable
private fun FeatureInfoCard() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "功能说明",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            val features = listOf(
                "✅ 完整的表单验证（用户名、邮箱、密码格式验证）",
                "✅ 邮箱验证码功能（目前使用默认验证码：123456）",
                "✅ JWT Token自动管理和刷新",
                "✅ 登录状态持久化",
                "✅ 错误处理和用户反馈",
                "✅ 注册成功后自动登录",
                "✅ 与Flask后端API完整集成",
                "✅ 安全的Token存储和验证"
            )
            
            features.forEach { feature ->
                Text(
                    text = feature,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}
