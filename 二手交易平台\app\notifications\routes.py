#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知与反馈路由
"""

from flask import render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from app.notifications import bp
from app.models import Notification, Feedback, User, UserRole
from app.utils.datetime_utils import local_now
from app import db
from sqlalchemy import desc

@bp.route('/notifications')
@login_required
def notifications():
    """用户通知列表"""
    page = request.args.get('page', 1, type=int)
    type_filter = request.args.get('type', 'all')
    status_filter = request.args.get('status', 'all')
    
    # 排除聊天消息通知，只显示系统通知、订单通知等
    query = Notification.query.filter(
        Notification.user_id == current_user.id,
        Notification.type != 'message'  # 排除聊天消息通知
    )

    # 类型筛选
    if type_filter != 'all':
        query = query.filter_by(type=type_filter)
    
    # 状态筛选
    if status_filter == 'unread':
        query = query.filter_by(is_read=False)
    elif status_filter == 'read':
        query = query.filter_by(is_read=True)
    
    notifications = query.order_by(desc(Notification.created_at)).paginate(
        page=page,
        per_page=20,
        error_out=False
    )
    
    return render_template('notifications/notifications.html',
                         title='我的通知',
                         notifications=notifications,
                         type_filter=type_filter,
                         status_filter=status_filter)

@bp.route('/notifications/mark_all_read', methods=['POST'])
@login_required
def mark_all_read():
    """标记所有通知为已读"""
    Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).update({'is_read': True})
    
    db.session.commit()
    flash('所有通知已标记为已读', 'success')
    return redirect(url_for('notifications.notifications'))

@bp.route('/api/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """标记单个通知为已读"""
    notification = Notification.query.filter_by(
        id=notification_id,
        user_id=current_user.id
    ).first()

    if not notification:
        return jsonify({'success': False, 'message': '通知不存在'}), 404

    notification.is_read = True
    db.session.commit()

    return jsonify({'success': True, 'message': '通知已标记为已读'})

@bp.route('/api/delete/<int:notification_id>', methods=['POST'])
@login_required
def api_delete_notification(notification_id):
    """删除单个通知API"""
    try:
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=current_user.id
        ).first()

        if not notification:
            return jsonify({'success': False, 'message': '通知不存在'}), 404

        db.session.delete(notification)
        db.session.commit()

        return jsonify({'success': True, 'message': '通知已删除'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '删除失败'}), 500

@bp.route('/api/clear_all', methods=['POST'])
@login_required
def api_clear_all_notifications():
    """清空所有通知API"""
    try:
        deleted_count = Notification.query.filter_by(
            user_id=current_user.id
        ).delete()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'已清空 {deleted_count} 条通知',
            'deleted_count': deleted_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '清空失败'}), 500

@bp.route('/feedback', methods=['GET', 'POST'])
@login_required
def feedback():
    """用户反馈"""
    from app.notifications.forms import FeedbackForm
    form = FeedbackForm()
    
    if form.validate_on_submit():
        feedback = Feedback(
            user_id=current_user.id,
            title=form.title.data,
            content=form.content.data,
            type=form.type.data
        )
        
        db.session.add(feedback)
        db.session.commit()
        
        flash('反馈提交成功，我们会尽快处理', 'success')
        return redirect(url_for('notifications.my_feedbacks'))
    
    return render_template('notifications/feedback.html',
                         title='意见反馈',
                         form=form)

@bp.route('/my_feedbacks')
@login_required
def my_feedbacks():
    """我的反馈"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    
    query = Feedback.query.filter_by(user_id=current_user.id)
    
    if status_filter != 'all':
        query = query.filter_by(status=status_filter)
    
    feedbacks = query.order_by(desc(Feedback.created_at)).paginate(
        page=page,
        per_page=10,
        error_out=False
    )
    
    return render_template('notifications/my_feedbacks.html',
                         title='我的反馈',
                         feedbacks=feedbacks,
                         status_filter=status_filter)

@bp.route('/feedback/<int:id>')
@login_required
def feedback_detail(id):
    """反馈详情"""
    feedback = Feedback.query.filter_by(
        id=id,
        user_id=current_user.id
    ).first_or_404()
    
    return render_template('notifications/feedback_detail.html',
                         title='反馈详情',
                         feedback=feedback)

@bp.route('/feedback/<int:id>/update_status', methods=['GET', 'POST'])
@login_required
def update_feedback_status(id):
    """用户更新反馈状态"""
    from app.notifications.forms import FeedbackStatusForm
    from app.models import Notification

    feedback = Feedback.query.filter_by(
        id=id,
        user_id=current_user.id
    ).first_or_404()

    form = FeedbackStatusForm()

    if form.validate_on_submit():
        old_status = feedback.status
        new_status = form.status.data

        feedback.status = new_status
        if form.user_note.data:
            # 将用户备注添加到反馈内容中
            feedback.content += f"\n\n[用户备注 - {local_now().strftime('%Y-%m-%d %H:%M')}]\n{form.user_note.data}"

        feedback.updated_at = local_now()

        # 如果状态发生变化，创建通知给管理员
        if old_status != new_status:
            # 获取管理员用户
            admin_users = User.query.filter_by(role=UserRole.ADMIN).all()
            for admin in admin_users:
                notification = Notification(
                    user_id=admin.id,
                    title='用户更新反馈状态',
                    content=f'用户 {current_user.username} 将反馈"{feedback.title}"状态更新为：{dict(form.status.choices)[new_status]}',
                    type='feedback',
                    related_id=feedback.id
                )
                db.session.add(notification)

        db.session.commit()
        flash('反馈状态已更新', 'success')
        return redirect(url_for('notifications.feedback_detail', id=feedback.id))

    elif request.method == 'GET':
        form.status.data = feedback.status

    return render_template('notifications/update_feedback_status.html',
                         title='更新反馈状态',
                         form=form,
                         feedback=feedback)

@bp.route('/api/unread_count')
@login_required
def api_unread_count():
    """获取未读通知数量API"""
    # 获取未读通知数量（排除聊天消息通知）
    unread_notifications = Notification.query.filter(
        Notification.user_id == current_user.id,
        Notification.is_read == False,
        Notification.type != 'message'  # 排除聊天消息通知
    ).count()

    # 获取未读消息数量（排除已删除的消息）
    from app.models import Message
    unread_messages = Message.query.filter(
        Message.receiver_id == current_user.id,
        Message.is_read == False,
        Message.deleted_by_receiver == False  # 排除接收方已删除的消息
    ).count()

    return jsonify({
        'success': True,
        'unread_notifications': unread_notifications,
        'unread_messages': unread_messages,
        'total_unread': unread_notifications + unread_messages
    })

@bp.route('/api/latest_notifications')
@login_required
def api_latest_notifications():
    """获取最新通知API（排除聊天消息通知）"""
    notifications = Notification.query.filter(
        Notification.user_id == current_user.id,
        Notification.type != 'message'  # 排除聊天消息通知
    ).order_by(Notification.created_at.desc()).limit(5).all()

    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'content': notification.content,
            'type': notification.type,
            'is_read': notification.is_read,
            'related_id': notification.related_id,
            'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'time_ago': get_time_ago(notification.created_at)
        })

    return jsonify({
        'success': True,
        'notifications': notifications_data
    })

def get_time_ago(dt):
    """获取相对时间"""
    from datetime import datetime, timedelta
    now = local_now()
    diff = now - dt

    if diff.days > 0:
        return f"{diff.days}天前"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours}小时前"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes}分钟前"
    else:
        return "刚刚"

@bp.route('/test/create_notification')
@login_required
def test_create_notification():
    """测试创建系统通知"""
    try:
        notification = Notification(
            user_id=current_user.id,
            title='系统通知测试',
            content='这是一个测试系统通知，用于验证红色数字提示功能',
            type='system',
            created_at=local_now()
        )
        db.session.add(notification)
        db.session.commit()

        flash('测试通知创建成功！', 'success')
        return redirect(url_for('main.index'))
    except Exception as e:
        db.session.rollback()
        flash('创建测试通知失败', 'error')
        return redirect(url_for('main.index'))
