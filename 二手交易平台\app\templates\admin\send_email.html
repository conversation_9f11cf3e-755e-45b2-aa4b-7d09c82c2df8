{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>发送邮件</h2>
                <a href="{{ url_for('admin_panel.email_management') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回邮件管理
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <form method="POST" id="sendEmailForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <!-- 邮件主题 -->
                        <div class="mb-3">
                            <label for="subject" class="form-label">邮件主题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>

                        <!-- 收件人类型选择 -->
                        <div class="mb-3">
                            <label class="form-label">收件人类型 <span class="text-danger">*</span></label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recipient_type" id="recipientUsers" value="users" checked>
                                <label class="form-check-label" for="recipientUsers">
                                    发送给注册用户
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recipient_type" id="recipientCustom" value="custom">
                                <label class="form-check-label" for="recipientCustom">
                                    发送给自定义邮箱
                                </label>
                            </div>
                        </div>

                        <!-- 注册用户选择 -->
                        <div class="mb-3" id="usersSection">
                            <label class="form-label">选择用户</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAllUsers">
                                        <label class="form-check-label" for="selectAllUsers">
                                            <strong>全选</strong>
                                        </label>
                                    </div>
                                    <hr>
                                    <div style="max-height: 300px; overflow-y: auto;">
                                        {% for user in users %}
                                        <div class="form-check">
                                            <input class="form-check-input user-checkbox" type="checkbox" name="selected_users" value="{{ user.id }}" id="user{{ user.id }}">
                                            <label class="form-check-label" for="user{{ user.id }}">
                                                {{ user.username }} ({{ user.email }})
                                            </label>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-1"></i>
                                        <strong>提示：</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>只显示有邮箱地址的用户</li>
                                            <li>可以选择多个用户</li>
                                            <li>使用"全选"可以快速选择所有用户</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 自定义邮箱输入 -->
                        <div class="mb-3" id="customSection" style="display: none;">
                            <label for="recipients" class="form-label">收件人邮箱</label>
                            <textarea class="form-control" id="recipients" name="recipients" rows="3" placeholder="请输入邮箱地址，多个邮箱用逗号分隔，例如：<EMAIL>, <EMAIL>"></textarea>
                            <div class="form-text">多个邮箱地址请用逗号分隔</div>
                        </div>

                        <!-- 邮件内容 -->
                        <div class="mb-3">
                            <label for="content" class="form-label">邮件内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="10" required placeholder="请输入邮件内容..."></textarea>
                        </div>

                        <!-- 预览区域 -->
                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-info" data-bs-toggle="collapse" data-bs-target="#previewSection">
                                <i class="fas fa-eye me-1"></i>预览邮件
                            </button>
                        </div>

                        <div class="collapse" id="previewSection">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">邮件预览</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>主题：</strong><span id="previewSubject">-</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>收件人：</strong><span id="previewRecipients">-</span>
                                    </div>
                                    <div>
                                        <strong>内容：</strong>
                                        <div id="previewContent" class="border p-2 mt-1" style="background-color: #f8f9fa;">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary" id="sendBtn">
                                <i class="fas fa-paper-plane me-1"></i>发送邮件
                            </button>
                            <a href="{{ url_for('admin_panel.email_management') }}" class="btn btn-secondary ms-2">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const recipientUsers = document.getElementById('recipientUsers');
    const recipientCustom = document.getElementById('recipientCustom');
    const usersSection = document.getElementById('usersSection');
    const customSection = document.getElementById('customSection');
    const selectAllUsers = document.getElementById('selectAllUsers');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const sendBtn = document.getElementById('sendBtn');
    const form = document.getElementById('sendEmailForm');

    // 切换收件人类型
    function toggleRecipientType() {
        if (recipientUsers.checked) {
            usersSection.style.display = 'block';
            customSection.style.display = 'none';
        } else {
            usersSection.style.display = 'none';
            customSection.style.display = 'block';
        }
        updatePreview();
    }

    recipientUsers.addEventListener('change', toggleRecipientType);
    recipientCustom.addEventListener('change', toggleRecipientType);

    // 全选功能
    selectAllUsers.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updatePreview();
    });

    // 监听用户选择变化
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            selectAllUsers.checked = checkedCount === userCheckboxes.length;
            selectAllUsers.indeterminate = checkedCount > 0 && checkedCount < userCheckboxes.length;
            updatePreview();
        });
    });

    // 更新预览
    function updatePreview() {
        const subject = document.getElementById('subject').value || '-';
        const content = document.getElementById('content').value || '-';
        
        document.getElementById('previewSubject').textContent = subject;
        document.getElementById('previewContent').innerHTML = content.replace(/\n/g, '<br>') || '-';
        
        let recipients = '-';
        if (recipientUsers.checked) {
            const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked'))
                .map(cb => cb.nextElementSibling.textContent.trim());
            recipients = selectedUsers.length > 0 ? selectedUsers.join(', ') : '未选择用户';
        } else {
            const customRecipients = document.getElementById('recipients').value.trim();
            recipients = customRecipients || '未输入邮箱';
        }
        
        document.getElementById('previewRecipients').textContent = recipients;
    }

    // 监听输入变化
    document.getElementById('subject').addEventListener('input', updatePreview);
    document.getElementById('content').addEventListener('input', updatePreview);
    document.getElementById('recipients').addEventListener('input', updatePreview);

    // 表单提交处理
    form.addEventListener('submit', function(e) {
        // 验证收件人
        if (recipientUsers.checked) {
            const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;
            if (selectedCount === 0) {
                e.preventDefault();
                alert('请选择至少一个用户');
                return;
            }
        } else {
            const customRecipients = document.getElementById('recipients').value.trim();
            if (!customRecipients) {
                e.preventDefault();
                alert('请输入收件人邮箱');
                return;
            }
        }

        // 验证通过后才显示发送中状态
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>发送中...';
        sendBtn.disabled = true;

        // 设置超时恢复按钮状态（防止页面卡住）
        setTimeout(function() {
            if (sendBtn.disabled) {
                sendBtn.innerHTML = '<i class="fas fa-paper-plane me-1"></i>发送邮件';
                sendBtn.disabled = false;
            }
        }, 30000); // 30秒后自动恢复
    });

    // 初始化
    toggleRecipientType();
    updatePreview();
});
</script>
{% endblock %}
