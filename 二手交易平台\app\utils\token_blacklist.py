#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token黑名单管理模块
"""

import redis
from flask import current_app
from datetime import datetime, timedelta
import jwt


class TokenBlacklist:
    """Token黑名单管理类"""
    
    def __init__(self):
        self.redis_client = None
        self._init_redis()
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            # 如果配置了Redis，使用Redis存储黑名单
            if hasattr(current_app.config, 'REDIS_URL'):
                self.redis_client = redis.from_url(current_app.config['REDIS_URL'])
            else:
                # 否则使用内存存储（仅用于开发环境）
                self.redis_client = None
                self._memory_blacklist = set()
        except Exception as e:
            current_app.logger.warning(f"Redis连接失败，使用内存存储: {str(e)}")
            self.redis_client = None
            self._memory_blacklist = set()
    
    def add_token(self, token, exp_time=None):
        """将token添加到黑名单"""
        try:
            if self.redis_client:
                # 使用Redis存储
                if exp_time:
                    # 设置过期时间
                    ttl = int((exp_time - datetime.utcnow()).total_seconds())
                    if ttl > 0:
                        self.redis_client.setex(f"blacklist:{token}", ttl, "1")
                else:
                    # 默认24小时过期
                    self.redis_client.setex(f"blacklist:{token}", 86400, "1")
            else:
                # 使用内存存储
                self._memory_blacklist.add(token)
            
            return True
        except Exception as e:
            current_app.logger.error(f"添加token到黑名单失败: {str(e)}")
            return False
    
    def is_blacklisted(self, token):
        """检查token是否在黑名单中"""
        try:
            if self.redis_client:
                # 从Redis检查
                return self.redis_client.exists(f"blacklist:{token}")
            else:
                # 从内存检查
                return token in self._memory_blacklist
        except Exception as e:
            current_app.logger.error(f"检查token黑名单失败: {str(e)}")
            return False
    
    def remove_token(self, token):
        """从黑名单中移除token"""
        try:
            if self.redis_client:
                self.redis_client.delete(f"blacklist:{token}")
            else:
                self._memory_blacklist.discard(token)
            
            return True
        except Exception as e:
            current_app.logger.error(f"从黑名单移除token失败: {str(e)}")
            return False
    
    def clear_expired_tokens(self):
        """清理过期的token（Redis会自动过期，这里主要针对内存存储）"""
        if not self.redis_client and hasattr(self, '_memory_blacklist'):
            # 对于内存存储，我们需要手动清理
            # 这里简单地清空所有token，实际应用中可以记录过期时间
            self._memory_blacklist.clear()


# 全局黑名单实例
token_blacklist = TokenBlacklist()


def blacklist_token(token):
    """将token加入黑名单"""
    try:
        # 解析token获取过期时间
        payload = jwt.decode(
            token,
            current_app.config['SECRET_KEY'],
            algorithms=['HS256'],
            options={"verify_exp": False}  # 不验证过期时间
        )
        
        exp_time = datetime.utcfromtimestamp(payload.get('exp', 0))
        return token_blacklist.add_token(token, exp_time)
    except Exception as e:
        current_app.logger.error(f"解析token失败: {str(e)}")
        # 如果解析失败，使用默认过期时间
        return token_blacklist.add_token(token)


def is_token_blacklisted(token):
    """检查token是否被拉黑"""
    return token_blacklist.is_blacklisted(token)


def remove_from_blacklist(token):
    """从黑名单移除token"""
    return token_blacklist.remove_token(token)


def clear_expired_blacklist():
    """清理过期的黑名单token"""
    return token_blacklist.clear_expired_tokens()
