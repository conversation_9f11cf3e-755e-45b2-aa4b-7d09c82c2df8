package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 评价数据模型
 * 对应Flask后端的Review模型
 */
@Entity(tableName = "reviews")
data class Review(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    // 外键
    @SerializedName("order_id")
    var orderId: Int = 0,

    @SerializedName("reviewer_id")
    var reviewerId: Int = 0,

    @SerializedName("reviewee_id")
    var revieweeId: Int = 0,

    // 评价内容
    @SerializedName("rating")
    var rating: Int = 5,  // 1-5星

    @SerializedName("content")
    var content: String? = null,

    // 时间戳
    @SerializedName("created_at")
    var createdAt: String = "",
    
    // 关联数据（API返回时可能包含，不存储到数据库）
    @Ignore
    @SerializedName("reviewer")
    val reviewer: User? = null,

    @Ignore
    @SerializedName("reviewee")
    val reviewee: User? = null,

    @Ignore
    @SerializedName("order")
    val order: Order? = null
) {
    /**
     * 获取评分显示文本
     */
    fun getRatingDisplay(): String {
        return when (rating) {
            5 -> "非常满意"
            4 -> "满意"
            3 -> "一般"
            2 -> "不满意"
            1 -> "非常不满意"
            else -> "未评分"
        }
    }
    
    /**
     * 获取星级显示字符串
     */
    fun getStarString(): String {
        return "★".repeat(rating) + "☆".repeat(5 - rating)
    }
    
    /**
     * 检查是否有评价内容
     */
    fun hasContent(): Boolean {
        return !content.isNullOrBlank()
    }
    
    /**
     * 获取评价内容或默认文本
     */
    fun getContentOrDefault(): String {
        return content?.takeIf { it.isNotBlank() } ?: "用户没有留下评价内容"
    }
}
