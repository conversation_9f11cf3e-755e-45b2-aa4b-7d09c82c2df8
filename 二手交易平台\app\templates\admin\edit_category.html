{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit"></i> 编辑分类</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% for error in form.name.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                        {% for error in form.description.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.icon.label(class="form-label") }}
                        {{ form.icon(class="form-control", placeholder="例如: fas fa-mobile-alt") }}
                        {% for error in form.icon.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <div class="form-text">
                            请输入FontAwesome图标类名，例如：fas fa-mobile-alt
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存更改
                        </button>
                        <a href="{{ url_for('admin_panel.categories') }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- 分类信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">分类信息</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <i class="{{ category.icon or 'fas fa-tag' }} fa-3x text-primary"></i>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>分类ID:</strong></div>
                    <div class="col-7">{{ category.id }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>商品数量:</strong></div>
                    <div class="col-7">{{ category.products.count() }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>创建时间:</strong></div>
                    <div class="col-7">{{ category.created_at.strftime('%Y-%m-%d') if category.created_at else '未知' }}</div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">图标预览</h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i id="icon-preview" class="{{ category.icon or 'fas fa-tag' }} fa-3x text-primary"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('icon').addEventListener('input', function() {
    const iconClass = this.value.trim();
    const preview = document.getElementById('icon-preview');
    
    if (iconClass) {
        preview.className = iconClass + ' fa-3x text-primary';
    } else {
        preview.className = 'fas fa-tag fa-3x text-muted';
    }
});
</script>
{% endblock %}
