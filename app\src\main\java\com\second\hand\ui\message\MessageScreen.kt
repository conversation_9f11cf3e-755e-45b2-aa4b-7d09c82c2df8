package com.second.hand.ui.message

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.second.hand.ui.navigation.NavigationDestinations

/**
 * 消息页面Screen - 显示对话列表
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MessageScreen(
    navController: NavController
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = {
                Text(
                    text = "消息",
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                IconButton(
                    onClick = {
                        navController.navigate(NavigationDestinations.SEARCH)
                    }
                ) {
                    Icon(Icons.Default.Search, contentDescription = "搜索")
                }
            }
        )

        // 消息列表
        if (getConversations().isEmpty()) {
            EmptyMessageState()
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(1.dp)
            ) {
                items(getConversations()) { conversation ->
                    ConversationItem(
                        conversation = conversation,
                        onClick = {
                            navController.navigate(
                                NavigationDestinations.chatWithUser(conversation.userId)
                            )
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun EmptyMessageState() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "💬",
                style = MaterialTheme.typography.displayLarge
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "暂无消息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "与其他用户的聊天记录将显示在这里",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ConversationItem(
    conversation: Conversation,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (conversation.hasUnread) {
                MaterialTheme.colorScheme.surfaceVariant
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 用户头像
            Card(
                modifier = Modifier.size(50.dp),
                shape = CircleShape,
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = conversation.userName.take(1).uppercase(),
                        style = MaterialTheme.typography.titleMedium,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 消息内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = conversation.userName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = if (conversation.hasUnread) FontWeight.Bold else FontWeight.Medium,
                        modifier = Modifier.weight(1f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Text(
                        text = conversation.lastMessageTime,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = conversation.lastMessage,
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (conversation.hasUnread) {
                            MaterialTheme.colorScheme.onSurface
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        },
                        modifier = Modifier.weight(1f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    if (conversation.unreadCount > 0) {
                        Badge(
                            modifier = Modifier.padding(start = 8.dp)
                        ) {
                            Text(
                                text = if (conversation.unreadCount > 99) "99+" else conversation.unreadCount.toString()
                            )
                        }
                    }
                }
            }
        }
    }
}

// 数据类
data class Conversation(
    val userId: String,
    val userName: String,
    val userAvatar: String? = null,
    val lastMessage: String,
    val lastMessageTime: String,
    val hasUnread: Boolean = false,
    val unreadCount: Int = 0
)

// 示例数据
private fun getConversations() = listOf(
    Conversation(
        userId = "1",
        userName = "张三",
        lastMessage = "这个商品还在吗？",
        lastMessageTime = "10:30",
        hasUnread = true,
        unreadCount = 2
    ),
    Conversation(
        userId = "2",
        userName = "李四",
        lastMessage = "好的，明天见面交易",
        lastMessageTime = "昨天",
        hasUnread = false,
        unreadCount = 0
    ),
    Conversation(
        userId = "3",
        userName = "王五",
        lastMessage = "价格可以再便宜一点吗？",
        lastMessageTime = "前天",
        hasUnread = true,
        unreadCount = 1
    ),
    Conversation(
        userId = "4",
        userName = "赵六",
        lastMessage = "谢谢，商品收到了",
        lastMessageTime = "3天前",
        hasUnread = false,
        unreadCount = 0
    ),
    Conversation(
        userId = "5",
        userName = "钱七",
        lastMessage = "可以看看实物吗？",
        lastMessageTime = "1周前",
        hasUnread = false,
        unreadCount = 0
    )
)
