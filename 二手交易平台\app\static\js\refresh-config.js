/**
 * 全局刷新系统配置文件
 * 用户可以通过修改这个文件来自定义刷新行为
 */

window.RefreshConfig = {
    // 默认刷新间隔（毫秒）
    defaultRefreshRate: 500, // 0.5秒
    
    // 默认刷新模式
    // 'smart' - 智能刷新（推荐）
    // 'full' - 完全刷新页面
    // 'off' - 关闭自动刷新
    defaultRefreshMode: 'smart',
    
    // 用户活动检测超时时间（毫秒）
    // 用户停止活动多长时间后才开始刷新
    userActivityTimeout: 2000, // 2秒
    
    // 是否默认启用刷新系统
    enableByDefault: true,
    
    // 是否显示控制面板提示
    showControlPanelHint: true,
    
    // 控制面板提示显示时间（毫秒）
    controlPanelHintDuration: 3000, // 3秒
    
    // 页面特定配置
    pageConfigs: {
        // 消息页面
        '/messages': {
            refreshMode: 'smart',
            refreshRate: 500,
            useCustomRefresh: true // 使用页面自定义的刷新逻辑
        },
        
        // 管理页面
        '/admin': {
            refreshMode: 'smart',
            refreshRate: 1000, // 1秒
            useCustomRefresh: true
        },
        
        // 商品页面
        '/products': {
            refreshMode: 'smart',
            refreshRate: 2000, // 2秒
            useCustomRefresh: false
        },
        
        // 首页
        '/': {
            refreshMode: 'smart',
            refreshRate: 1000, // 1秒
            useCustomRefresh: false
        },
        
        // 用户资料页面
        '/profile': {
            refreshMode: 'smart',
            refreshRate: 5000, // 5秒
            useCustomRefresh: false
        },
        
        // 订单页面
        '/orders': {
            refreshMode: 'smart',
            refreshRate: 1000, // 1秒
            useCustomRefresh: false
        }
    },
    
    // 需要暂停刷新的页面路径（正则表达式）
    pauseRefreshPages: [
        /\/edit/, // 编辑页面
        /\/create/, // 创建页面
        /\/form/, // 表单页面
        /\/upload/ // 上传页面
    ],
    
    // 需要暂停刷新的元素选择器
    pauseRefreshElements: [
        'input[type="text"]',
        'input[type="email"]',
        'input[type="password"]',
        'textarea',
        'select',
        '[contenteditable="true"]'
    ],
    
    // 快捷键配置
    shortcuts: {
        toggleControlPanel: 'Ctrl+R', // 显示/隐藏控制面板
        togglePause: 'Ctrl+P', // 暂停/恢复刷新
        manualRefresh: 'Ctrl+Shift+R' // 手动刷新
    },
    
    // 刷新模式说明
    refreshModeDescriptions: {
        'smart': '智能刷新 - 检测用户活动，在合适时机刷新内容',
        'full': '完全刷新 - 每隔指定时间重新加载整个页面',
        'off': '关闭刷新 - 不进行自动刷新'
    },
    
    // 可选的刷新间隔（毫秒）
    availableRefreshRates: [
        { value: 500, label: '0.5秒' },
        { value: 1000, label: '1秒' },
        { value: 2000, label: '2秒' },
        { value: 3000, label: '3秒' },
        { value: 5000, label: '5秒' },
        { value: 10000, label: '10秒' },
        { value: 30000, label: '30秒' }
    ],
    
    // 控制面板样式配置
    controlPanelStyle: {
        position: 'fixed',
        top: '10px',
        left: '10px',
        background: 'rgba(0,0,0,0.9)',
        color: 'white',
        padding: '15px',
        borderRadius: '8px',
        fontSize: '12px',
        zIndex: 9999,
        minWidth: '250px',
        fontFamily: 'Arial, sans-serif',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
    },
    
    // 提示样式配置
    hintStyle: {
        position: 'fixed',
        bottom: '10px',
        left: '10px',
        background: 'rgba(0,0,0,0.7)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        fontSize: '11px',
        zIndex: 9998
    },
    
    // 调试模式
    debugMode: false, // 设置为true可以在控制台看到刷新日志
    
    // 性能监控
    performanceMonitoring: {
        enabled: false, // 是否启用性能监控
        logInterval: 10000, // 每10秒记录一次性能数据
        maxLogEntries: 100 // 最多保存100条性能记录
    },
    
    // 获取当前页面的配置
    getCurrentPageConfig: function() {
        const currentPath = window.location.pathname;
        
        // 检查是否有特定页面配置
        for (const path in this.pageConfigs) {
            if (currentPath.startsWith(path)) {
                return { ...this.pageConfigs[path] };
            }
        }
        
        // 返回默认配置
        return {
            refreshMode: this.defaultRefreshMode,
            refreshRate: this.defaultRefreshRate,
            useCustomRefresh: false
        };
    },
    
    // 检查是否应该暂停刷新
    shouldPauseRefresh: function() {
        const currentPath = window.location.pathname;
        
        // 检查路径是否匹配暂停规则
        return this.pauseRefreshPages.some(pattern => pattern.test(currentPath));
    },
    
    // 记录调试信息
    log: function(message, data = null) {
        if (this.debugMode) {
            console.log(`[GlobalRefresh] ${message}`, data || '');
        }
    }
};

// 导出配置（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.RefreshConfig;
}
