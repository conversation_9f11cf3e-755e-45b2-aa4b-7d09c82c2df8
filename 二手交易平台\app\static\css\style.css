/* 现代化二手交易平台样式 */

/* CSS 变量定义 */
:root {
    /* 主色调 */
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --primary-light: #66b3ff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;

    /* 中性色 */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #6c757d;
    --text-dark: #212529;
    --text-muted: #6c757d;

    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
    --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.16);

    /* 圆角 */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 过渡 */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', 'Source Han Sans SC', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;

    /* 响应式断点 */
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-2xl: 1400px;
}

/* 全局重置和基础样式 */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* 全局样式 */
body {
    font-family: var(--font-family);
    background: var(--white);
    min-height: 100vh;
    line-height: 1.6;
    color: var(--text-dark);
    font-weight: 400;
    font-size: var(--font-size-base);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* 排版和字体优化 */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.025em;
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
    color: var(--text-dark);
}

.text-muted {
    color: var(--text-muted) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

/* 链接样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* 表单元素现代化 */
.form-control,
.form-select {
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    background: var(--white);
    color: var(--text-dark);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    outline: none;
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-label {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

/* 现代化按钮系统 */
.btn {
    font-weight: 500;
    letter-spacing: 0.025em;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    vertical-align: middle;
}

/* 确保按钮内图标居中 */
.btn i {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    line-height: 1;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #003d82 100%);
    border-color: var(--primary-dark);
    color: var(--white);
}

.btn-outline-primary {
    background: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-secondary {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
    border-color: var(--success-color);
    color: var(--white);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
    border-color: var(--danger-color);
    color: var(--white);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    border-radius: var(--radius-lg);
}

/* 布局和容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

main.container {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    min-height: calc(100vh - 200px);
}

/* 导航栏容器不应用背景样式 */
.navbar .container {
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    margin: 0;
    box-shadow: none;
    max-width: 1200px;
}

/* 现代化导航栏 */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    box-shadow: var(--shadow-md);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: var(--white) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.navbar-nav .nav-link {
    color: var(--white) !important;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
    margin: 0 var(--spacing-xs);
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white) !important;
    transform: translateY(-1px);
}

/* 导航栏搜索框 */
.navbar .form-control {
    background-color: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--text-dark);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all var(--transition-normal);
}

.navbar .form-control:focus {
    background-color: var(--white);
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
    color: var(--text-dark);
}

.navbar .form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
}

.navbar .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.8);
    color: var(--white);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
}

.navbar .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: var(--white);
    color: var(--white);
    transform: translateY(-1px);
}

/* 现代化轮播图系统 */
.hero-carousel {
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

.carousel-item {
    position: relative;
    overflow: hidden;
    min-height: 500px;
}

.carousel-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.05), rgba(255,255,255,0.05));
    z-index: 1;
}

.carousel-content {
    position: relative;
    z-index: 2;
    padding: var(--spacing-2xl);
    min-height: 500px;
    display: flex;
    align-items: center;
}

.carousel-slide-1 {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    position: relative;
}

.carousel-slide-2 {
    background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
    position: relative;
}

.carousel-slide-3 {
    background: linear-gradient(135deg, var(--info-color) 0%, #117a8b 100%);
    position: relative;
}

.carousel-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: slideInLeft 1s ease-out;
    color: var(--white) !important;
    line-height: 1.2;
}

.carousel-subtitle {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-xl);
    opacity: 0.95;
    animation: slideInLeft 1s ease-out 0.2s both;
    color: var(--white) !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    line-height: 1.4;
}

.carousel-btn {
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    border-radius: 50px;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.2);
    color: var(--white);
    text-decoration: none;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    animation: slideInLeft 1s ease-out 0.4s both;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.carousel-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

.carousel-icon {
    font-size: 8rem;
    opacity: 0.3;
    animation: float 3s ease-in-out infinite;
}

.carousel-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    animation: slideInLeft 1s ease-out;
    color: #ffffff !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.carousel-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    opacity: 0.95;
    animation: slideInLeft 1s ease-out 0.2s both;
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.carousel-btn {
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    animation: slideInLeft 1s ease-out 0.4s both;
}

.carousel-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    color: white;
}

.carousel-icon {
    font-size: 8rem;
    opacity: 0.3;
    animation: float 3s ease-in-out infinite;
}

/* 轮播图控制按钮现代化 */
.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.7) !important;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.9);
    top: 50%;
    transform: translateY(-50%);
    transition: all var(--transition-normal);
    opacity: 0.9 !important;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.carousel-control-prev {
    left: var(--spacing-xl);
}

.carousel-control-next {
    right: var(--spacing-xl);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background: rgba(0, 0, 0, 0.9) !important;
    border-color: var(--white);
    transform: translateY(-50%) scale(1.15);
    opacity: 1 !important;
    box-shadow: var(--shadow-lg);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 28px;
    height: 28px;
    filter: brightness(0) invert(1);
    background-size: 100% 100% !important;
}

/* 轮播图指示器现代化 */
.carousel-indicators {
    bottom: var(--spacing-xl);
    z-index: 10;
    gap: var(--spacing-sm);
}

.carousel-indicators [data-bs-target] {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.8);
    background: rgba(0, 0, 0, 0.3);
    transition: all var(--transition-normal);
    opacity: 0.8;
    margin: 0 var(--spacing-xs);
}

.carousel-indicators .active {
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--white);
    transform: scale(1.3);
    opacity: 1;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* 轮播图指示器 */
.carousel-indicators {
    bottom: 20px;
}

.carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.8);
    background: rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    opacity: 0.8;
}

.carousel-indicators .active {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 1);
    transform: scale(1.2);
    opacity: 1;
}

/* 动画效果 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}





/* 动画效果 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #ffffff !important;
}

.navbar {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: #007bff !important;
}

.navbar-nav .nav-link {
    color: #ffffff !important;
    font-weight: 500;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.navbar-nav .nav-link:hover {
    color: #e3f2fd !important;
}

/* 导航栏搜索框样式 */
.navbar .form-control {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #495057;
}

.navbar .form-control:focus {
    background-color: #ffffff;
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    color: #495057;
}

.navbar .form-control::placeholder {
    color: #6c757d;
    opacity: 0.8;
}

.navbar .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.8);
    color: #ffffff;
}

.navbar .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: #ffffff;
    color: #ffffff;
}

/* 导航栏认证按钮样式 */
.nav-auth-btn {
    border-radius: 25px !important;
    padding: 8px 20px !important;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.nav-auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-outline-light.nav-auth-btn {
    border: 2px solid rgba(255, 255, 255, 0.9);
    color: white !important;
    background: rgba(255, 255, 255, 0.15);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-outline-light.nav-auth-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: white;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-light.nav-auth-btn {
    background: white !important;
    color: #007bff !important;
    border: 2px solid white;
    font-weight: 700;
}

.btn-light.nav-auth-btn:hover {
    background: #f8f9fa !important;
    color: #0056b3 !important;
    border-color: #f8f9fa;
}

/* 通知样式 */
.notification-badge {
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    /* 移除呼吸灯动画，保持常亮 */
    background-color: #dc3545 !important; /* 纯红色，覆盖Bootstrap的渐变色 */
    background-image: none !important; /* 移除渐变背景 */
    border: none !important;
}

/* 更强的选择器优先级来覆盖Bootstrap的bg-danger类 */
.badge.bg-danger.notification-badge,
.position-absolute.badge.bg-danger.notification-badge,
span.badge.bg-danger.notification-badge,
.badge.notification-badge,
span.badge.notification-badge {
    background-color: #dc3545 !important;
    background-image: none !important;
    background: #dc3545 !important;
    /* 覆盖所有可能的渐变属性 */
    background-attachment: initial !important;
    background-blend-mode: initial !important;
    background-clip: initial !important;
    background-origin: initial !important;
    background-position: initial !important;
    background-repeat: initial !important;
    background-size: initial !important;
}

.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
}

.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notification-item .notification-title {
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 4px;
}

.notification-item .notification-content {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.notification-item .notification-time {
    color: #a0aec0;
    font-size: 0.8rem;
}

.nav-link .fa-bell {
    transition: all 0.3s ease;
}

.nav-link:hover .fa-bell {
    transform: rotate(15deg);
    color: #ffd700;
}

/* 现代化商品卡片系统 */
.product-card {
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-sm);
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--white);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.02), rgba(0, 123, 255, 0.05));
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 1;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.product-card:hover::before {
    opacity: 1;
}

.product-card .card-img-top {
    transition: transform var(--transition-normal);
    position: relative;
    z-index: 2;
    height: 220px;
    object-fit: cover;
    width: 100%;
}

.product-card:hover .card-img-top {
    transform: scale(1.03);
}

.product-card .card-body {
    position: relative;
    z-index: 2;
    padding: var(--spacing-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-card .card-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
    font-size: var(--font-size-base);
}

.product-card .card-title a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.product-card:hover .card-title a {
    color: var(--primary-color);
}

.product-card .card-text {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    flex: 1;
    margin-bottom: var(--spacing-md);
}

.product-card .price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--danger-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-sm);
}

.product-card .text-muted {
    color: var(--text-muted) !important;
    font-size: var(--font-size-xs);
}

.product-card .badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    z-index: 3;
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    font-size: var(--font-size-xs);
    box-shadow: var(--shadow-sm);
}

/* 现代化分类卡片 */
.category-card {
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-sm);
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--white);
    position: relative;
    height: 100%;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.03), rgba(0, 123, 255, 0.08));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.category-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.category-card:hover::before {
    opacity: 1;
}

.category-card .card-body {
    position: relative;
    z-index: 2;
    padding: var(--spacing-2xl) var(--spacing-lg);
    text-align: center;
}

.category-card i {
    transition: all var(--transition-normal);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    display: block;
}

.category-card:hover i {
    transform: scale(1.15) rotate(5deg);
    color: var(--primary-dark);
}

.category-card .card-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
    transition: color var(--transition-normal);
    font-size: var(--font-size-lg);
}

.category-card:hover .card-title {
    color: var(--primary-color);
}

.category-card .card-text {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    transition: color var(--transition-normal);
    line-height: 1.5;
}

.category-card:hover .card-text {
    color: var(--text-dark);
}

/* 商品图片样式 */
.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.375rem;
}

/* 价格样式 */
.price {
    font-size: 1.25rem;
    font-weight: bold;
    color: #dc3545;
}

.original-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.9rem;
}

/* 状态标签样式 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 用户头像样式 */
.user-avatar {
    width: 40px;
    height: 40px;
    object-fit: cover;
}

.user-avatar-sm {
    width: 24px;
    height: 24px;
    object-fit: cover;
}

.user-avatar-lg {
    width: 80px;
    height: 80px;
    object-fit: cover;
}

/* 消息样式 */
.message-item {
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.375rem;
}

.message-item.sent {
    border-left-color: #28a745;
    background-color: #d4edda;
    margin-left: 2rem;
}

.message-item.received {
    border-left-color: #007bff;
    background-color: #d1ecf1;
    margin-right: 2rem;
}

/* 通知样式 */
.notification-item {
    border-left: 4px solid #007bff;
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.notification-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
}

.notification-item.unread {
    background-color: #f8f9ff;
    border-left-color: #007bff;
    border-left-width: 4px;
}

.notification-item .notification-title {
    color: #212529;
    font-weight: 600;
}

.notification-item .notification-content {
    color: #6c757d;
    line-height: 1.5;
}

.notification-item .notification-time {
    color: #adb5bd;
    font-size: 0.875rem;
}

/* 搜索过滤器样式 */
.filter-section {
    background-color: white;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 统计卡片样式 */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 0.5rem;
}

.stat-card.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 表格样式 */
.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 按钮样式 */
.btn-rounded {
    border-radius: 50px;
}

/* 页面加载动画 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

.loader-text {
    color: #007bff;
    font-size: 1.2rem;
    margin-top: 1rem;
    animation: pulse 2s ease-in-out infinite;
    font-weight: 600;
}

/* 按钮加载动画 */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 页面过渡效果 */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* 微交互效果 */
.btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 卡片进入动画 */
.card-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-animate.in-view {
    opacity: 1;
    transform: translateY(0);
}

.card-animate:nth-child(1) { transition-delay: 0.1s; }
.card-animate:nth-child(2) { transition-delay: 0.2s; }
.card-animate:nth-child(3) { transition-delay: 0.3s; }
.card-animate:nth-child(4) { transition-delay: 0.4s; }

/* 滚动指示器 */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 1000;
}

.scroll-progress {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.1s ease;
}

/* 现代化动画系统 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.1);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.1);
    }
    70% {
        transform: scale(1);
    }
}

/* 全面响应式设计系统 */

/* 超大屏幕 (≥1400px) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .carousel-title {
        font-size: 4rem;
    }

    .carousel-subtitle {
        font-size: 1.75rem;
    }

    .product-card .card-img-top {
        height: 240px;
    }
}

/* 大屏幕 (≥1200px) */
@media (max-width: 1199.98px) {
    .container {
        max-width: 1140px;
        padding: 0 var(--spacing-md);
    }

    main.container {
        margin-top: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
        padding: var(--spacing-xl);
    }

    .carousel-title {
        font-size: 3rem;
    }

    .carousel-subtitle {
        font-size: 1.4rem;
    }

    .carousel-content {
        padding: var(--spacing-xl);
        min-height: 450px;
    }
}

/* 中等屏幕 (≥992px) */
@media (max-width: 991.98px) {
    .container {
        max-width: 960px;
    }

    main.container {
        padding: var(--spacing-lg);
        margin-top: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }

    .carousel-title {
        font-size: 2.5rem;
    }

    .carousel-subtitle {
        font-size: 1.25rem;
    }

    .carousel-content {
        padding: var(--spacing-xl) var(--spacing-lg);
        min-height: 400px;
    }

    .carousel-icon {
        font-size: 6rem;
    }

    .carousel-control-prev {
        left: var(--spacing-md);
    }

    .carousel-control-next {
        right: var(--spacing-md);
    }

    /* 导航栏适配 */
    .navbar-nav .nav-link {
        padding: var(--spacing-xs) var(--spacing-sm) !important;
        margin: var(--spacing-xs) 0;
    }

    /* 卡片网格调整 */
    .product-card {
        margin-bottom: var(--spacing-lg);
    }

    .category-card .card-body {
        padding: var(--spacing-lg);
    }
}

/* 平板设备 (≥768px) */
/* 添加强制桌面版类，避免在某些情况下误触发移动端样式 */
@media (max-width: 767.98px) and (not (.force-desktop)) {
    .container {
        max-width: 720px;
        padding: 0 var(--spacing-sm);
    }

    main.container {
        margin-top: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
    }

    /* 导航栏移动端优化 */
    .navbar {
        padding: var(--spacing-sm) 0;
    }

    .navbar-brand {
        font-size: var(--font-size-lg);
    }

    .navbar-toggler {
        border: none;
        padding: var(--spacing-xs);
    }

    .navbar-collapse {
        margin-top: var(--spacing-sm);
        background: rgba(255, 255, 255, 0.95);
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        backdrop-filter: blur(10px);
    }

    .navbar-nav .nav-link {
        color: var(--text-dark) !important;
        padding: var(--spacing-sm) !important;
        border-radius: var(--radius-sm);
        margin: var(--spacing-xs) 0;
    }

    .navbar-nav .nav-link:hover {
        background: var(--light-gray);
        color: var(--primary-color) !important;
    }

    /* 轮播图平板适配 */
    .hero-carousel {
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-xl);
    }

    .carousel-title {
        font-size: 2.25rem;
        margin-bottom: var(--spacing-sm);
    }

    .carousel-subtitle {
        font-size: 1.125rem;
        margin-bottom: var(--spacing-lg);
    }

    .carousel-content {
        padding: var(--spacing-xl) var(--spacing-md);
        min-height: 350px;
        text-align: center;
    }

    .carousel-icon {
        font-size: 5rem;
        margin-top: var(--spacing-md);
    }

    .carousel-btn {
        padding: var(--spacing-sm) var(--spacing-xl);
        font-size: var(--font-size-base);
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 50px;
        height: 50px;
    }

    .carousel-control-prev {
        left: var(--spacing-sm);
    }

    .carousel-control-next {
        right: var(--spacing-sm);
    }

    /* 卡片平板适配 */
    .product-card {
        margin-bottom: var(--spacing-lg);
    }

    .product-card:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .category-card:hover {
        transform: translateY(-4px) scale(1.02);
    }

    /* 过滤器平板适配 */
    .filter-section {
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-lg);
    }

    /* 搜索表单平板优化 */
    .navbar .form-control {
        margin-bottom: var(--spacing-sm);
        width: 100%;
    }

    .navbar .d-flex {
        flex-direction: column;
        width: 100%;
    }

    .navbar .btn-outline-light {
        width: 100%;
        justify-content: center;
    }
}

/* 手机设备 (≤576px) */
@media (max-width: 575.98px) {
    .container {
        max-width: 100%;
        padding: 0 var(--spacing-sm);
    }

    main.container {
        margin-top: var(--spacing-xs);
        margin-bottom: var(--spacing-xs);
        padding: var(--spacing-sm);
        border-radius: var(--radius-sm);
    }

    /* 导航栏手机优化 */
    .navbar {
        padding: var(--spacing-xs) 0;
    }

    .navbar-brand {
        font-size: var(--font-size-base);
    }

    .navbar-brand i {
        font-size: var(--font-size-lg);
    }

    /* 轮播图手机适配 */
    .hero-carousel {
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-lg);
    }

    .carousel-title {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    .carousel-subtitle {
        font-size: 0.95rem;
        line-height: 1.4;
    }

    .carousel-content {
        padding: var(--spacing-lg) var(--spacing-sm);
        min-height: 280px;
    }

    .carousel-icon {
        font-size: 3.5rem;
        margin-top: var(--spacing-sm);
    }

    .carousel-btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
        border-radius: 25px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 40px;
        height: 40px;
    }

    .carousel-control-prev {
        left: var(--spacing-xs);
    }

    .carousel-control-next {
        right: var(--spacing-xs);
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 20px;
        height: 20px;
    }

    /* 卡片手机适配 */
    .product-card .card-body {
        padding: var(--spacing-md);
    }

    .product-card .card-img-top {
        height: 180px;
    }

    .category-card .card-body {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    /* 按钮手机适配 */
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .btn-lg {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    /* 表单手机优化 */
    .form-control,
    .form-select {
        padding: var(--spacing-sm);
        font-size: var(--font-size-base);
    }

    /* 过滤器手机适配 */
    .filter-section {
        padding: var(--spacing-sm);
        border-radius: var(--radius-sm);
        margin-bottom: var(--spacing-md);
    }

    .filter-section .row {
        margin: 0;
    }

    .filter-section .col-md-2,
    .filter-section .col-md-4 {
        padding: 0;
        margin-bottom: var(--spacing-sm);
    }

    /* 搜索结果手机优化 */
    .btn-group {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }

    .btn-group .btn {
        flex: 1;
    }

    /* 价格显示优化 */
    .price {
        font-size: var(--font-size-lg) !important;
    }

    /* 文字大小调整 */
    h1 { font-size: var(--font-size-2xl); }
    h2 { font-size: var(--font-size-xl); }
    h3 { font-size: var(--font-size-lg); }
    h4 { font-size: var(--font-size-base); }
}

/* 图片上传预览 */
.image-preview {
    position: relative;
    display: inline-block;
    margin: 0.5rem;
}

.image-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 0.375rem;
    border: 2px solid #dee2e6;
}

.image-preview .remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
}

/* 星级评分样式 */
.rating {
    color: #ffc107;
}

.rating .fa-star {
    margin-right: 2px;
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 面包屑样式 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 修复布局问题 */
.seller-info .d-flex {
    align-items: flex-start !important;
    gap: 1rem;
}

.seller-info img {
    flex-shrink: 0;
}

.seller-info .fw-bold {
    word-break: break-word;
    line-height: 1.4;
}

.seller-info small {
    display: block;
    margin-top: 0.25rem;
    line-height: 1.3;
}

/* 防止按钮重复点击时的样式 */
.favorite-btn.processing {
    opacity: 0.7;
    pointer-events: none;
}

/* 改善卡片布局 */
.card-body {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 修复用户头像显示 */
.rounded-circle {
    object-fit: cover;
    border: 2px solid #e9ecef;
}

/* 改善按钮组布局 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 修复表格布局 */
.table td {
    vertical-align: middle;
    word-break: break-word;
}

.table .btn-group {
    white-space: nowrap;
}

/* 商品分类和使用痕迹样式 */
.product-category-condition {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #007bff;
}

.product-category-condition .badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    display: inline-block;
}

.product-category-condition small {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.product-category-condition > div {
    line-height: 1.5;
}

/* 改善商品元信息显示 */
.product-meta small {
    font-weight: 500;
    color: #6c757d;
}

.product-meta .badge {
    margin-left: 0.25rem;
    font-weight: 500;
}

/* 搜索页面行显示的商品元信息 */
.list-view .product-meta {
    margin-top: 0.5rem;
}

.list-view .product-meta > div {
    margin-bottom: 0.25rem;
}

/* 首页商品卡片的元信息 */
.card .product-meta {
    border-top: 1px solid #e9ecef;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.card .product-meta .badge {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
}

/* 改善响应式布局 */
@media (max-width: 768px) {
    .seller-info .d-flex {
        flex-direction: column;
        text-align: center;
    }

    .seller-info img {
        align-self: center;
        margin-bottom: 0.5rem;
    }

    .product-category-condition .row {
        text-align: center;
    }

    .product-category-condition .col-6 {
        margin-bottom: 0.5rem;
    }
}

/* 商品信息列表样式 */
.product-info-list {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-left: 3px solid #007bff;
}

.product-info-list .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.product-info-list .info-item small {
    min-width: 50px;
    margin-right: 0.5rem;
    font-weight: 600;
    color: #6c757d;
}

.product-info-list .info-item span {
    color: #495057;
    font-weight: 500;
}

/* 九宫格视图的商品信息样式 */
.product-info-list-grid {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    border-left: 3px solid #007bff;
}

.product-info-list-grid .info-item-grid {
    display: flex;
    align-items: center;
    margin-bottom: 0.2rem;
    font-size: 0.8rem;
}

.product-info-list-grid .info-item-grid small {
    min-width: 40px;
    margin-right: 0.3rem;
    font-weight: 600;
    color: #6c757d;
}

.product-info-list-grid .info-item-grid span {
    color: #495057;
    font-weight: 500;
    font-size: 0.8rem;
}

/* 管理员操作按钮样式 */
.btn-group .btn {
    position: relative;
    z-index: 1;
    border-radius: 0.375rem !important;
    margin-right: 2px;
    min-width: 40px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.btn-group .btn:hover {
    z-index: 2;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-group .btn:active {
    transform: translateY(0);
}

/* 确保按钮可点击 */
.btn-group .btn {
    pointer-events: auto !important;
    cursor: pointer !important;
}

.btn-group .btn i {
    font-size: 14px;
}

/* 管理员操作按钮容器 */
.admin-action-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
}

.admin-action-buttons .btn {
    min-width: 36px;
    height: 32px;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer !important;
    pointer-events: auto !important;
    position: relative !important;
    z-index: 999 !important;
    background: white;
    border: 1px solid;
    text-decoration: none;
    vertical-align: middle;
}

.admin-action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.admin-action-buttons .btn:active {
    transform: translateY(0);
}

.admin-action-buttons .btn i {
    font-size: 13px;
    pointer-events: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    line-height: 1;
}

/* 强制覆盖任何可能的样式冲突 */
.admin-action-buttons .btn.status-btn,
.admin-action-buttons .btn.delete-btn,
.admin-action-buttons .btn.view-btn {
    cursor: pointer !important;
    pointer-events: auto !important;
    user-select: none !important;
}

/* 现代化加载和状态组件 */
.loading-skeleton {
    background: linear-gradient(90deg, var(--light-gray) 25%, var(--medium-gray) 50%, var(--light-gray) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-sm);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: var(--spacing-md) auto;
}

/* 现代化通知和徽章 */
.notification-badge {
    background: var(--danger-color) !important;
    color: var(--white) !important;
    font-size: var(--font-size-xs);
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.message-badge {
    background: var(--success-color) !important;
    color: var(--white) !important;
}

/* 现代化下拉菜单 */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) 0;
    margin-top: var(--spacing-xs);
}

.dropdown-item {
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all var(--transition-fast);
    color: var(--text-dark);
}

.dropdown-item:hover {
    background: var(--light-gray);
    color: var(--primary-color);
}

.dropdown-header {
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 600;
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 现代化分页 */
.pagination {
    gap: var(--spacing-xs);
}

.page-link {
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-sm);
    color: var(--text-dark);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all var(--transition-normal);
    text-decoration: none;
}

.page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* 现代化表格 */
.table {
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background: var(--light-gray);
    border: none;
    font-weight: 600;
    color: var(--text-dark);
    padding: var(--spacing-md);
}

.table td {
    border: none;
    padding: var(--spacing-md);
    vertical-align: middle;
}

.table tbody tr {
    transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
    background: var(--light-gray);
}

/* 现代化警告框 */
.alert {
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    border-left: 4px solid;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
    color: #856404;
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
    color: var(--info-color);
}

/* 现代化页脚样式 */
footer {
    margin-top: auto;
}

.footer-brand h5 {
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.footer-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: var(--spacing-xs);
}

.footer-links a {
    color: var(--text-muted);
    text-decoration: none;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    padding: var(--spacing-xs) 0;
}

.footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.footer-links a i {
    width: 16px;
    text-align: center;
}

.social-links a {
    color: var(--text-muted);
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0, 123, 255, 0.1);
}

.social-links a:hover {
    color: var(--white);
    background: var(--primary-color);
    transform: translateY(-2px);
}

.contact-info p {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.contact-info i {
    width: 20px;
    text-align: center;
}

/* 页面加载动画增强 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

.loader-text {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-md);
    animation: pulse 2s ease-in-out infinite;
    font-weight: 600;
}

/* 页面过渡效果 */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* 卡片进入动画 */
.card-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-animate.in-view {
    opacity: 1;
    transform: translateY(0);
}

.card-animate:nth-child(1) { transition-delay: 0.1s; }
.card-animate:nth-child(2) { transition-delay: 0.2s; }
.card-animate:nth-child(3) { transition-delay: 0.3s; }
.card-animate:nth-child(4) { transition-delay: 0.4s; }

/* 滚动指示器 */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 1000;
}

.scroll-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    width: 0%;
    transition: width 0.1s ease;
}

/* 统计信息样式 */
.stat-item {
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    border-radius: var(--radius-md);
}

.stat-item:hover {
    transform: translateY(-5px);
    background: var(--white);
    box-shadow: var(--shadow-md);
}

.stat-item i {
    transition: all var(--transition-normal);
}

.stat-item:hover i {
    transform: scale(1.1);
}

.stat-item h5 {
    font-weight: 600;
    color: var(--text-dark);
}

/* 空状态增强 */
.empty-state {
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    background: var(--light-gray);
    margin: var(--spacing-xl) 0;
}

.empty-state i {
    opacity: 0.5;
    animation: float 3s ease-in-out infinite;
}

/* 导航栏认证按钮增强 */
.nav-auth-btn {
    border-radius: 25px !important;
    padding: var(--spacing-sm) var(--spacing-lg) !important;
    font-weight: 600;
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
    text-decoration: none !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    position: relative;
    overflow: hidden;
}

.nav-auth-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.nav-auth-btn:active::before {
    width: 300px;
    height: 300px;
}

.nav-auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-light.nav-auth-btn {
    border: 2px solid rgba(255, 255, 255, 0.9);
    color: var(--white) !important;
    background: rgba(255, 255, 255, 0.15);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-outline-light.nav-auth-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: var(--white);
    color: var(--white) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-light.nav-auth-btn {
    background: var(--white) !important;
    color: var(--primary-color) !important;
    border: 2px solid var(--white);
    font-weight: 700;
}

.btn-light.nav-auth-btn:hover {
    background: var(--light-gray) !important;
    color: var(--primary-dark) !important;
    border-color: var(--light-gray);
}

/* 移动端特殊优化 */
@media (max-width: 575.98px) {
    .stat-item {
        padding: var(--spacing-md);
    }

    .stat-item i {
        font-size: 1.5rem !important;
    }

    .stat-item h5 {
        font-size: var(--font-size-sm);
    }

    .empty-state {
        padding: var(--spacing-lg);
        margin: var(--spacing-md) 0;
    }

    .nav-auth-btn span {
        display: none;
    }

    .nav-auth-btn {
        min-width: 40px;
        padding: var(--spacing-sm) !important;
    }
}
