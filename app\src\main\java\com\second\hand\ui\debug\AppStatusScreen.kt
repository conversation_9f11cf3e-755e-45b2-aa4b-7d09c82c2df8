package com.second.hand.ui.debug

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.second.hand.ui.auth.AuthViewModel
import com.second.hand.ui.components.InfoMessageCard
import com.second.hand.ui.navigation.NavigationDestinations

/**
 * 应用状态检查界面
 * 用于诊断界面显示和导航问题
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppStatusScreen(
    navController: NavController,
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val authUiState by authViewModel.uiState.collectAsState()
    val tokenState by authViewModel.getTokenState().collectAsState()
    val isLoading by authViewModel.isLoading.collectAsState()
    val error by authViewModel.error.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "📱 应用状态检查",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        // 应用状态卡片
        AppStatusCard(authUiState, tokenState, isLoading, error)
        
        // 导航测试按钮
        NavigationTestButtons(navController)
        
        // 功能状态检查
        FeatureStatusCard()
        
        // 界面显示说明
        UIDisplayInfoCard()
    }
}

@Composable
private fun AppStatusCard(
    authUiState: com.second.hand.ui.auth.AuthUiState,
    tokenState: com.second.hand.data.auth.TokenState,
    isLoading: Boolean,
    error: String?
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "应用状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            StatusRow("应用加载", "正常")
            StatusRow("导航系统", "已初始化")
            StatusRow("认证系统", if (authUiState.isLoggedIn) "已登录" else "未登录")
            StatusRow("Token状态", if (tokenState.isValid) "有效" else "无效")
            StatusRow("加载状态", if (isLoading) "加载中" else "空闲")
            StatusRow("错误状态", error ?: "无错误")
            
            Spacer(modifier = Modifier.height(8.dp))
            
            if (authUiState.currentUser != null) {
                Text(
                    text = "当前用户: ${authUiState.currentUser.username}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

@Composable
private fun StatusRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun NavigationTestButtons(navController: NavController) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "导航测试",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            // 第一行按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { 
                        navController.navigate(NavigationDestinations.WELCOME) {
                            popUpTo(0) { inclusive = true }
                        }
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("欢迎页面")
                }
                
                Button(
                    onClick = { 
                        navController.navigate(NavigationDestinations.HOME)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("主页面")
                }
            }
            
            // 第二行按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { 
                        navController.navigate(NavigationDestinations.AUTH_TEST)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("登录测试")
                }
                
                Button(
                    onClick = { 
                        navController.navigate(NavigationDestinations.TOKEN_DEBUG)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Token监控")
                }
            }
        }
    }
}

@Composable
private fun FeatureStatusCard() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "功能状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            val features = listOf(
                "✅ WelcomeScreen - 欢迎页面已实现",
                "✅ AuthCard - 登录注册表单已实现",
                "✅ AuthViewModel - 认证逻辑已实现",
                "✅ TokenManager - Token管理已实现",
                "✅ ValidationUtils - 表单验证已实现",
                "✅ MessageCard - 消息反馈已实现",
                "✅ AuthTestScreen - 测试界面已实现",
                "✅ TokenDebugScreen - 调试界面已实现",
                "✅ Navigation - 导航系统已配置"
            )
            
            features.forEach { feature ->
                Text(
                    text = feature,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(vertical = 1.dp)
                )
            }
        }
    }
}

@Composable
private fun UIDisplayInfoCard() {
    InfoMessageCard(
        message = """
            📱 界面显示说明：
            
            1. 应用启动时会显示WelcomeScreen（欢迎页面）
            2. 如果已登录，会自动跳转到主页面
            3. 如果未登录，显示登录注册表单
            4. 可以通过"我的"页面访问调试功能
            5. 调试功能包括登录测试和Token监控
            
            如果看不到界面，请检查：
            - 应用是否正常启动
            - 导航配置是否正确
            - AuthViewModel是否正常初始化
        """.trimIndent()
    )
}
