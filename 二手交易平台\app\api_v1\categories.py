#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端分类API
"""

from flask import current_app
from app.api_v1 import bp
from app.models import Category, Product, ProductStatus
from app import db
from app.utils.api_response import success_response, error_response
from sqlalchemy import func, and_
from datetime import datetime


def format_datetime_safe(dt):
    """安全地格式化日期时间"""
    if dt is None:
        return None
    try:
        # 如果是字符串，先解析为datetime对象
        if isinstance(dt, str):
            # 尝试解析包含微秒的格式
            try:
                dt = datetime.strptime(dt, '%Y-%m-%dT%H:%M:%S.%f')
            except ValueError:
                try:
                    dt = datetime.strptime(dt, '%Y-%m-%dT%H:%M:%S')
                except ValueError:
                    # 如果都解析失败，返回原字符串
                    return dt

        # 格式化为标准ISO格式
        return dt.strftime('%Y-%m-%dT%H:%M:%S') + 'Z'
    except Exception as e:
        current_app.logger.error(f"日期格式化失败: {str(e)}, 原始值: {dt}")
        return str(dt) if dt else None


@bp.route('/categories', methods=['GET'])
def get_categories():
    """获取分类列表"""
    try:
        # 获取所有分类，并统计每个分类的商品数量
        categories_query = db.session.query(
            Category,
            func.count(Product.id).label('product_count')
        ).outerjoin(
            Product,
            and_(
                Product.category_id == Category.id,
                Product.status == ProductStatus.ACTIVE
            )
        ).group_by(Category.id).order_by(Category.id)
        
        categories_data = []
        for category, product_count in categories_query:
            categories_data.append({
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'icon': category.icon,
                'color': category.color or category.get_color(),  # 使用数据库字段或生成的颜色
                'productCount': product_count,
                'createdAt': format_datetime_safe(category.created_at)
            })
        
        return success_response(categories_data, '获取分类列表成功')
        
    except Exception as e:
        current_app.logger.error(f"获取分类列表失败: {str(e)}")
        return error_response('SYS_001', '获取分类列表失败', http_code=500)


@bp.route('/categories/<int:category_id>', methods=['GET'])
def get_category(category_id):
    """获取分类详情"""
    try:
        category = Category.query.get(category_id)
        if not category:
            return error_response('BIZ_001', '分类不存在', http_code=404)
        
        # 统计该分类的商品数量
        product_count = Product.query.filter_by(
            category_id=category_id,
            status=ProductStatus.ACTIVE
        ).count()
        
        category_data = {
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'icon': category.icon,
            'color': category.color or category.get_color(),  # 使用数据库字段或生成的颜色
            'productCount': product_count,
            'createdAt': format_datetime_safe(category.created_at)
        }
        
        return success_response(category_data, '获取分类详情成功')
        
    except Exception as e:
        current_app.logger.error(f"获取分类详情失败: {str(e)}")
        return error_response('SYS_001', '获取分类详情失败', http_code=500)
