package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 商品图片数据模型
 * 对应Flask后端的ProductImage模型
 */
@Entity(tableName = "product_images")
data class ProductImage(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    @SerializedName("filename")
    var filename: String = "",

    @SerializedName("is_main")
    var isMain: Boolean = false,

    @SerializedName("sort_order")
    var sortOrder: Int = 0,

    @SerializedName("product_id")
    var productId: Int = 0,

    @SerializedName("created_at")
    var createdAt: String = ""
) {
    /**
     * 获取图片完整URL
     */
    fun getImageUrl(baseUrl: String): String {
        return "$baseUrl/static/uploads/products/$filename"
    }
    
    /**
     * 获取缩略图URL
     */
    fun getThumbnailUrl(baseUrl: String): String {
        // 如果后端支持缩略图，可以在这里实现
        return getImageUrl(baseUrl)
    }
}
