{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-comments text-primary"></i> 商品消息</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                                <li class="breadcrumb-item"><a href="{{ url_for('orders.my_orders') }}">我的订单</a></li>
                                <li class="breadcrumb-item active">商品消息</li>
                            </ol>
                        </nav>
                    </div>
                    <a href="{{ url_for('products.detail', id=product.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> 查看商品
                    </a>
                </div>

                <!-- 商品信息卡片 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <img src="{{ product.get_main_image_url() }}"
                                     class="img-fluid rounded" alt="{{ product.title }}" style="max-height: 80px;">
                            </div>
                            <div class="col-md-8">
                                <h5 class="mb-1">{{ product.title }}</h5>
                                <p class="text-muted mb-1">{{ product.description[:100] }}{% if product.description|length > 100 %}...{% endif %}</p>
                                <div class="d-flex align-items-center">
                                    <span class="text-danger fw-bold me-3">¥{{ "%.2f"|format(product.price) }}</span>
                                    <span class="badge bg-{{ 'success' if product.status.value == 'active' else 'secondary' }}">
                                        {{ product.status.value|title }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-2 text-end">
                                <small class="text-muted">卖家：{{ product.seller.nickname or product.seller.username }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 消息列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history"></i> 聊天记录</h5>
                    </div>
                    <div class="card-body">
                        {% if messages.items %}
                        <div class="message-container" style="max-height: 500px; overflow-y: auto;">
                            {% for message in messages.items %}
                            <div class="message-item mb-3 {% if message.sender_id == current_user.id %}sent{% else %}received{% endif %}">
                                <div class="d-flex {% if message.sender_id == current_user.id %}justify-content-end{% endif %}">
                                    <div class="message-bubble p-3 rounded {% if message.sender_id == current_user.id %}bg-primary text-white{% else %}bg-light border{% endif %}" 
                                         style="max-width: 70%;">
                                        <div class="message-content mb-2">{{ message.content|nl2br|safe }}</div>
                                        <div class="message-meta d-flex justify-content-between align-items-center">
                                            <small class="{% if message.sender_id == current_user.id %}text-white-50{% else %}text-muted{% endif %}">
                                                {% if message.sender_id == current_user.id %}
                                                    我
                                                {% else %}
                                                    {{ product.seller.nickname or product.seller.username }}
                                                {% endif %}
                                            </small>
                                            <small class="{% if message.sender_id == current_user.id %}text-white-50{% else %}text-muted{% endif %}">
                                                {{ message.created_at.strftime('%m-%d %H:%M') }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- 分页 -->
                        {% if messages.pages > 1 %}
                        <nav aria-label="消息分页" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if messages.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('orders.messages', product_id=product.id, page=messages.prev_num) }}">
                                        <i class="fas fa-chevron-left"></i> 上一页
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% for page_num in messages.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != messages.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('orders.messages', product_id=product.id, page=page_num) }}">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if messages.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('orders.messages', product_id=product.id, page=messages.next_num) }}">
                                        下一页 <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                        {% else %}
                        <!-- 空状态 -->
                        <div class="text-center py-5">
                            <i class="fas fa-comment-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无消息记录</h5>
                            <p class="text-muted">还没有关于这个商品的消息</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- 发送消息区域 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-paper-plane"></i> 发送消息</h6>
                    </div>
                    <div class="card-body">
                        <a href="{{ url_for('orders.send_message', product_id=product.id) }}" class="btn btn-primary">
                            <i class="fas fa-comment"></i> 发送新消息
                        </a>
                        <a href="{{ url_for('messages.chat', user_id=product.seller_id, product_id=product.id) }}" class="btn btn-outline-info ms-2">
                            <i class="fas fa-comments"></i> 进入聊天室
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.message-item.sent {
    text-align: right;
}

.message-item.received {
    text-align: left;
}

.message-bubble {
    display: inline-block;
    word-wrap: break-word;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-container {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
}

.message-container::-webkit-scrollbar {
    width: 6px;
}

.message-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.message-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.message-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 自动滚动到最新消息
    const messageContainer = document.querySelector('.message-container');
    if (messageContainer) {
        messageContainer.scrollTop = messageContainer.scrollHeight;
    }
});
</script>
{% endblock %}
