{% extends "base.html" %}

{% block content %}
<div class="container">
    <h2 class="mb-4">头像系统测试</h2>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">用户头像展示</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for user in users %}
                        <div class="col-md-3 mb-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <img src="{{ user.get_avatar_url() }}" 
                                         class="rounded-circle user-avatar user-avatar-lg mb-3" 
                                         alt="{{ user.username }}"
                                         onerror="handleAvatarError(this)">
                                    <h6 class="card-title">{{ user.nickname or user.username }}</h6>
                                    <p class="card-text small text-muted">@{{ user.username }}</p>
                                    <p class="card-text small">
                                        <span class="badge" style="background-color: {{ user.get_avatar_color() }}; color: white;">
                                            {{ user.get_avatar_char() }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">不同尺寸头像测试</h5>
                </div>
                <div class="card-body">
                    {% if users %}
                    {% set test_user = users[0] %}
                    <div class="d-flex align-items-center gap-3">
                        <div class="text-center">
                            <img src="{{ test_user.get_avatar_url() }}" 
                                 class="rounded-circle user-avatar user-avatar-sm" 
                                 alt="{{ test_user.username }}">
                            <div class="small mt-1">小 (24px)</div>
                        </div>
                        <div class="text-center">
                            <img src="{{ test_user.get_avatar_url() }}" 
                                 class="rounded-circle user-avatar user-avatar-md" 
                                 alt="{{ test_user.username }}">
                            <div class="small mt-1">中 (48px)</div>
                        </div>
                        <div class="text-center">
                            <img src="{{ test_user.get_avatar_url() }}" 
                                 class="rounded-circle user-avatar user-avatar-lg" 
                                 alt="{{ test_user.username }}">
                            <div class="small mt-1">大 (120px)</div>
                        </div>
                        <div class="text-center">
                            <img src="{{ test_user.get_avatar_url() }}" 
                                 class="rounded-circle user-avatar user-avatar-xl" 
                                 alt="{{ test_user.username }}">
                            <div class="small mt-1">超大 (200px)</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">中文字符测试</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 实际生成的中文用户头像 -->
                        <div class="col-md-2 mb-3 text-center">
                            <img src="{{ url_for('main.test_chinese_avatar', char='系') }}"
                                 class="rounded-circle user-avatar"
                                 style="width: 80px; height: 80px;"
                                 alt="系统管理员">
                            <div class="small mt-2">系统管理员</div>
                        </div>
                        <div class="col-md-2 mb-3 text-center">
                            <img src="{{ url_for('main.test_chinese_avatar', char='张') }}"
                                 class="rounded-circle user-avatar"
                                 style="width: 80px; height: 80px;"
                                 alt="张三">
                            <div class="small mt-2">张三</div>
                        </div>
                        <div class="col-md-2 mb-3 text-center">
                            <img src="{{ url_for('main.test_chinese_avatar', char='李') }}"
                                 class="rounded-circle user-avatar"
                                 style="width: 80px; height: 80px;"
                                 alt="李四">
                            <div class="small mt-2">李四</div>
                        </div>
                        <div class="col-md-2 mb-3 text-center">
                            <img src="{{ url_for('main.test_chinese_avatar', char='王') }}"
                                 class="rounded-circle user-avatar"
                                 style="width: 80px; height: 80px;"
                                 alt="王五">
                            <div class="small mt-2">王五</div>
                        </div>
                        <div class="col-md-2 mb-3 text-center">
                            <img src="{{ url_for('main.test_chinese_avatar', char='A') }}"
                                 class="rounded-circle user-avatar"
                                 style="width: 80px; height: 80px;"
                                 alt="Admin">
                            <div class="small mt-2">Admin</div>
                        </div>
                        <div class="col-md-2 mb-3 text-center">
                            <img src="{{ url_for('main.test_chinese_avatar', char='U') }}"
                                 class="rounded-circle user-avatar"
                                 style="width: 80px; height: 80px;"
                                 alt="User123">
                            <div class="small mt-2">User123</div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">更多中文字符测试</h6>
                    <div class="row">
                        {% set chinese_chars = ['陈', '刘', '黄', '周', '吴', '徐', '孙', '马', '朱', '胡', '林', '郭'] %}
                        {% for char in chinese_chars %}
                        <div class="col-md-1 mb-3 text-center">
                            <img src="{{ url_for('main.test_chinese_avatar', char=char) }}"
                                 class="rounded-circle user-avatar"
                                 style="width: 60px; height: 60px;"
                                 alt="{{ char }}">
                            <div class="small mt-1">{{ char }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 头像系统说明</h6>
                <ul class="mb-0">
                    <li><strong>中文支持</strong>：完美支持中文字符显示，使用微软雅黑等中文字体</li>
                    <li><strong>智能识别</strong>：自动识别中文、英文和数字字符，正确显示</li>
                    <li><strong>多层备用</strong>：PIL图片生成 → SVG生成 → Canvas生成 → 默认图标</li>
                    <li><strong>个性化颜色</strong>：根据用户ID自动分配20种不同颜色</li>
                    <li><strong>响应式尺寸</strong>：支持小(24px)、中(48px)、大(120px)、超大(200px)</li>
                    <li><strong>完美居中</strong>：文字自动居中对齐，支持中英文混合</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
