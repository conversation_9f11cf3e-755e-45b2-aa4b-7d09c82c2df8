package com.second.hand.ui.auth

import androidx.lifecycle.viewModelScope
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.auth.TokenManager
import com.second.hand.data.auth.TokenRefreshService
import com.second.hand.data.model.auth.LoginRequest
import com.second.hand.data.model.auth.RegisterRequest
import com.second.hand.data.preferences.PreferencesManager
import com.second.hand.data.repository.AuthRepository
import com.second.hand.data.user.UserStateManager
import com.second.hand.ui.base.BaseViewModel
import com.second.hand.utils.ValidationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 认证ViewModel
 * 处理登录、注册等认证相关功能
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val preferencesManager: PreferencesManager,
    private val tokenManager: TokenManager,
    private val tokenRefreshService: TokenRefreshService,
    private val userStateManager: UserStateManager
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(AuthUiState())
    val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()

    init {
        // 立即检查登录状态，避免状态不一致导致的页面闪烁
        checkLoginStatusSync()

        // 异步启动后台服务，不阻塞UI初始化
        launchSafely(showLoading = false) {
            // 启动自动Token刷新监控
            tokenRefreshService.startAutoRefreshMonitoring()
        }

        // 监听用户状态变化
        observeUserState()
    }
    
    /**
     * 用户登录
     */
    fun login(username: String, password: String) {
        // 表单验证
        val validationErrors = ValidationUtils.validateLoginForm(username, password)
        if (validationErrors.isNotEmpty()) {
            setError(validationErrors.first().message)
            return
        }

        launchSafely {
            setLoading(true)
            clearError()

            // 添加调试日志
            println("🔐 开始登录请求 - 用户名: $username")

            val loginRequest = LoginRequest(
                username = username.trim(),
                password = password
            )

            println("🔐 登录请求数据: $loginRequest")

            when (val result = authRepository.login(loginRequest)) {
                is NetworkResult.Success -> {
                    val loginResponse = result.data
                    println("🔐 登录成功 - 用户: ${loginResponse.user.username}")

                    // 更新用户状态管理器
                    userStateManager.onUserLoggedIn(loginResponse.user)

                    // 更新UI状态
                    _uiState.value = _uiState.value.copy(
                        isLoggedIn = true,
                        currentUser = loginResponse.user
                    )

                    // 显示成功消息
                    println("🎉 登录成功，欢迎 ${loginResponse.user.nickname ?: loginResponse.user.username}")
                }
                is NetworkResult.Error -> {
                    println("🔐 登录失败 - 错误: ${result.message}")
                    setError(result.message)
                }
                is NetworkResult.Loading -> {
                    // 已经在setLoading(true)中处理
                }
            }

            setLoading(false)
        }
    }
    
    /**
     * 用户注册
     */
    fun register(username: String, email: String, password: String, confirmPassword: String, emailCode: String = "123456") {
        // 表单验证
        val validationErrors = ValidationUtils.validateRegisterForm(
            username = username,
            email = email,
            password = password,
            confirmPassword = confirmPassword,
            emailCode = emailCode,
            nickname = username.trim()
        )

        if (validationErrors.isNotEmpty()) {
            setError(validationErrors.first().message)
            return
        }

        launchSafely {
            setLoading(true)
            clearError()

            println("🔐 开始注册请求 - 用户名: $username, 邮箱: $email")

            val registerRequest = RegisterRequest(
                username = username.trim(),
                email = email.trim(),
                password = password,
                emailCode = emailCode, // 邮箱验证码
                nickname = username.trim() // 默认使用用户名作为昵称
            )

            when (val result = authRepository.register(registerRequest)) {
                is NetworkResult.Success -> {
                    val loginResponse = result.data
                    println("🔐 注册成功 - 用户: ${loginResponse.user.username}")

                    // 更新用户状态管理器
                    userStateManager.onUserLoggedIn(loginResponse.user)

                    // 更新UI状态
                    _uiState.value = _uiState.value.copy(
                        isLoggedIn = true,
                        currentUser = loginResponse.user
                    )

                    // 显示成功消息
                    println("🎉 注册成功，欢迎加入 ${loginResponse.user.nickname ?: loginResponse.user.username}")
                }
                is NetworkResult.Error -> {
                    println("🔐 注册失败 - 错误: ${result.message}")
                    setError(result.message)
                }
                is NetworkResult.Loading -> {
                    // 已经在setLoading(true)中处理
                }
            }

            setLoading(false)
        }
    }
    
    /**
     * 用户登出
     */
    fun logout() {
        launchSafely {
            // 停止自动刷新监控
            tokenRefreshService.stopAutoRefreshMonitoring()

            // 更新用户状态管理器
            userStateManager.onUserLoggedOut("用户主动登出")

            // 调用后端登出API并清除本地Token
            authRepository.logout()

            _uiState.value = _uiState.value.copy(
                isLoggedIn = false,
                currentUser = null
            )
        }
    }
    
    /**
     * 同步检查登录状态（用于初始化）
     */
    private fun checkLoginStatusSync() {
        val isLoggedIn = tokenManager.isLoggedIn()
        _uiState.value = _uiState.value.copy(isLoggedIn = isLoggedIn)
    }

    /**
     * 检查登录状态（异步版本，保留向后兼容）
     */
    fun checkLoginStatus() {
        launchSafely {
            val isLoggedIn = tokenManager.isLoggedIn()
            _uiState.value = _uiState.value.copy(isLoggedIn = isLoggedIn)
        }
    }

    /**
     * 获取Token状态
     */
    fun getTokenState() = tokenManager.tokenState

    /**
     * 手动刷新Token状态
     */
    fun refreshTokenState() {
        tokenManager.refreshTokenState()
    }
    
    /**
     * 切换登录/注册模式
     */
    fun toggleAuthMode() {
        _uiState.value = _uiState.value.copy(
            isLoginMode = !_uiState.value.isLoginMode
        )
        clearError()
    }
    
    /**
     * 发送邮箱验证码
     */
    fun sendEmailCode(email: String) {
        // 验证邮箱格式
        val emailValidation = ValidationUtils.validateEmail(email)
        if (emailValidation is com.second.hand.utils.ValidationResult.Error) {
            setError(emailValidation.message)
            return
        }

        launchSafely {
            println("📧 发送邮箱验证码到: $email")
            // TODO: 实现发送邮箱验证码的API调用
            // 目前使用模拟实现
            setError("邮箱验证码发送功能暂未实现，请使用默认验证码：123456")
        }
    }

    /**
     * 清除特定字段的错误
     */
    fun clearFieldError() {
        clearError()
    }

    /**
     * 手动刷新Token
     */
    fun manualRefreshToken() {
        launchSafely {
            setLoading(true)
            val success = tokenRefreshService.refreshToken("手动刷新")
            if (success) {
                checkLoginStatus()
            }
            setLoading(false)
        }
    }

    /**
     * 获取Token刷新状态
     */
    fun getRefreshStatus() = tokenRefreshService.refreshStatus

    /**
     * 获取Token刷新用户消息
     */
    fun getRefreshUserMessage() = tokenRefreshService.userMessage

    /**
     * 清除刷新用户消息
     */
    fun clearRefreshMessage() {
        tokenRefreshService.clearUserMessage()
    }

    /**
     * 获取Token健康状态
     */
    fun getTokenHealthStatus() = tokenRefreshService.checkTokenHealth()

    /**
     * 获取Token健康状态描述
     */
    fun getTokenHealthDescription() = tokenRefreshService.getTokenHealthDescription()

    /**
     * 监听用户状态变化
     */
    private fun observeUserState() {
        launchSafely {
            userStateManager.userState.collect { userState ->
                _uiState.value = _uiState.value.copy(
                    isLoggedIn = userState.isLoggedIn,
                    currentUser = userState.user
                )
            }
        }

        // 监听用户状态事件
        launchSafely {
            userStateManager.userStateEvents.collect { event ->
                handleUserStateEvent(event)
            }
        }
    }

    /**
     * 处理用户状态事件
     */
    private fun handleUserStateEvent(event: com.second.hand.data.user.UserStateEvent) {
        when (event) {
            is com.second.hand.data.user.UserStateEvent.LoggedIn -> {
                println("👤 用户状态事件: 登录成功 - ${event.user.username}")
            }
            is com.second.hand.data.user.UserStateEvent.LoggedOut -> {
                println("👤 用户状态事件: 登出 - ${event.reason}")
            }
            is com.second.hand.data.user.UserStateEvent.UserInfoUpdated -> {
                println("👤 用户状态事件: 信息更新 - ${event.user.username}")
            }
            is com.second.hand.data.user.UserStateEvent.UserInfoSynced -> {
                println("👤 用户状态事件: 信息同步成功 - ${event.user.username}")
            }
            is com.second.hand.data.user.UserStateEvent.SyncFailed -> {
                println("👤 用户状态事件: 同步失败 - ${event.error}")
            }
            is com.second.hand.data.user.UserStateEvent.Error -> {
                println("👤 用户状态事件: 错误 - ${event.message}")
                setError(event.message)
            }
        }
    }

    /**
     * 获取用户状态流
     */
    fun getUserState() = userStateManager.userState

    /**
     * 获取当前用户流
     */
    fun getCurrentUser() = userStateManager.currentUser

    /**
     * 同步用户信息
     */
    fun syncUserInfo() {
        launchSafely {
            userStateManager.syncUserInfoFromServer()
        }
    }

    /**
     * 更新用户信息
     */
    fun updateUserInfo(user: com.second.hand.data.model.User) {
        launchSafely {
            userStateManager.updateUserInfo(user)
        }
    }
}

/**
 * 认证UI状态
 */
data class AuthUiState(
    val isLoggedIn: Boolean = false,
    val isLoginMode: Boolean = true, // true: 登录模式, false: 注册模式
    val currentUser: com.second.hand.data.model.User? = null
)
