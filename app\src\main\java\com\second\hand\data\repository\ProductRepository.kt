package com.second.hand.data.repository

import com.second.hand.data.api.ApiService
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.model.BaseResponse
import com.second.hand.data.model.PagedResponse
import com.second.hand.data.model.Product
import com.second.hand.data.model.enums.ProductStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 商品数据仓库
 * 处理商品相关的数据操作
 */
@Singleton
class ProductRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    /**
     * 获取商品列表
     */
    suspend fun getProducts(
        page: Int = 1,
        limit: Int = 20,
        categoryId: Int? = null,
        status: String = "active",
        search: String? = null,
        sort: String = "created_at",
        order: String = "desc",
        minPrice: Double? = null,
        maxPrice: Double? = null,
        condition: String? = null,
        location: String? = null
    ): NetworkResult<PagedResponse<Product>> {
        return safeApiCall {
            apiService.getProducts(
                page = page,
                limit = limit,
                categoryId = categoryId,
                status = status,
                search = search,
                sort = sort,
                order = order,
                minPrice = minPrice,
                maxPrice = maxPrice,
                condition = condition,
                location = location
            )
        }
    }

    /**
     * 获取商品详情
     */
    suspend fun getProduct(productId: Int): NetworkResult<Product> {
        return safeApiCall { apiService.getProduct(productId) }
    }

    /**
     * 搜索商品
     */
    suspend fun searchProducts(
        query: String,
        page: Int = 1,
        limit: Int = 20,
        categoryId: Int? = null,
        minPrice: Double? = null,
        maxPrice: Double? = null,
        condition: String? = null,
        location: String? = null,
        sort: String = "created_at",
        order: String = "desc"
    ): NetworkResult<PagedResponse<Product>> {
        return safeApiCall {
            apiService.searchProducts(
                query = query,
                page = page,
                limit = limit,
                categoryId = categoryId,
                minPrice = minPrice,
                maxPrice = maxPrice,
                condition = condition,
                location = location,
                sort = sort,
                order = order
            )
        }
    }

    /**
     * 获取推荐商品
     */
    suspend fun getFeaturedProducts(limit: Int = 8): NetworkResult<List<Product>> {
        return when (val result = safeApiCall {
            apiService.getProducts(
                page = 1,
                limit = limit,
                status = "active",
                sort = "view_count",
                order = "desc"
            )
        }) {
            is NetworkResult.Success -> NetworkResult.Success(result.data.items)
            is NetworkResult.Error -> NetworkResult.Error(result.message)
            is NetworkResult.Loading -> NetworkResult.Loading()
        }
    }

    /**
     * 获取用户发布的商品
     */
    suspend fun getUserProducts(
        userId: Int,
        page: Int = 1,
        limit: Int = 20,
        status: String? = null
    ): NetworkResult<PagedResponse<Product>> {
        return safeApiCall {
            apiService.getUserProducts(
                userId = userId,
                page = page,
                limit = limit,
                status = status
            )
        }
    }

    /**
     * 收藏/取消收藏商品
     */
    suspend fun toggleFavorite(productId: Int): NetworkResult<Boolean> {
        return when (val result = safeApiCall { apiService.toggleFavorite(productId) }) {
            is NetworkResult.Success -> {
                val isFavorited = result.data?.get("is_favorited") as? Boolean ?: false
                NetworkResult.Success(isFavorited)
            }
            is NetworkResult.Error -> NetworkResult.Error(result.message)
            is NetworkResult.Loading -> NetworkResult.Loading()
        }
    }

    /**
     * 增加商品浏览量
     */
    suspend fun incrementViewCount(productId: Int): NetworkResult<Unit> {
        return when (val result = safeApiCall { apiService.incrementViewCount(productId) }) {
            is NetworkResult.Success -> NetworkResult.Success(Unit)
            is NetworkResult.Error -> NetworkResult.Error(result.message)
            is NetworkResult.Loading -> NetworkResult.Loading()
        }
    }
}
