{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-edit"></i> 编辑商品</h3>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                {{ form.title.label(class="form-label") }}
                                {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                                {% if form.title.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.title.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.description.label(class="form-label") }}
                                {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="6") }}
                                {% if form.description.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.description.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.price.label(class="form-label") }}
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            {{ form.price(class="form-control" + (" is-invalid" if form.price.errors else "")) }}
                                        </div>
                                        {% if form.price.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.price.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.original_price.label(class="form-label") }}
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            {{ form.original_price(class="form-control" + (" is-invalid" if form.original_price.errors else "")) }}
                                        </div>
                                        {% if form.original_price.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.original_price.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.category_id.label(class="form-label") }}
                                        {{ form.category_id(class="form-select" + (" is-invalid" if form.category_id.errors else "")) }}
                                        {% if form.category_id.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.category_id.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.condition.label(class="form-label") }}
                                        {{ form.condition(class="form-select" + (" is-invalid" if form.condition.errors else "")) }}
                                        {% if form.condition.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.condition.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.location.label(class="form-label") }}
                                {{ form.location(class="form-control" + (" is-invalid" if form.location.errors else "")) }}
                                {% if form.location.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.location.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- 当前图片 -->
                            <div class="mb-3">
                                <label class="form-label">当前图片</label>
                                <div class="current-images">
                                    {% for image in product.images %}
                                    <div class="image-item mb-2" data-image-id="{{ image.id }}">
                                        <img src="/static/uploads/products/{{ image.filename }}"
                                             class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                                        {% if image.is_main %}
                                            <span class="badge bg-primary">主图</span>
                                        {% endif %}
                                        <button type="button" class="btn btn-sm btn-danger delete-image-btn" 
                                                data-image-id="{{ image.id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            
                            <!-- 新增图片 -->
                            <div class="mb-3">
                                {{ form.images.label(class="form-label") }}
                                {{ form.images(class="form-control" + (" is-invalid" if form.images.errors else ""), multiple=True) }}
                                {% if form.images.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.images.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">支持jpg、jpeg、png、gif格式，最多上传5张图片</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products.detail', id=product.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 删除图片功能
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.delete-image-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const imageId = this.getAttribute('data-image-id');
            if (confirm('确定要删除这张图片吗？')) {
                fetch(`{{ url_for('products.delete_image', image_id=0) }}`.replace('0', imageId), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.closest('.image-item').remove();
                        showAlert('success', data.message);
                    } else {
                        showAlert('error', data.message || '删除失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('error', '删除失败');
                });
            }
        });
    });
});

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.querySelector('.card-body').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
{% endblock %}