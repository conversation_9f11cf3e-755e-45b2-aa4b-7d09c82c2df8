#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证邮件功能
"""

from flask import render_template, current_app
from flask_mail import Message
from app import mail
try:
    import jwt
except ImportError:
    # 如果没有安装PyJWT，可以使用itsdangerous作为替代
    from itsdangerous import URLSafeTimedSerializer
from time import time

def send_email(subject, sender, recipients, text_body, html_body):
    """发送邮件"""
    msg = Message(subject, sender=sender, recipients=recipients)
    msg.body = text_body
    msg.html = html_body
    mail.send(msg)

def send_password_reset_email(user):
    """发送密码重置邮件"""
    token = get_reset_password_token(user)
    send_email(
        subject='[二手交易平台] 重置密码',
        sender=current_app.config['MAIL_DEFAULT_SENDER'],
        recipients=[user.email],
        text_body=render_template('email/reset_password.txt', user=user, token=token),
        html_body=render_template('email/reset_password.html', user=user, token=token)
    )

def get_reset_password_token(user, expires_in=600):
    """生成密码重置令牌"""
    return jwt.encode(
        {'reset_password': user.id, 'exp': time() + expires_in},
        current_app.config['SECRET_KEY'],
        algorithm='HS256'
    )

def verify_reset_password_token(token):
    """验证密码重置令牌"""
    try:
        id = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])['reset_password']
    except:
        return None
    from app.models import User
    return User.query.get(id)

def send_verification_code_email(email):
    """发送邮箱验证码"""
    from app.models import EmailVerificationCode, db

    # 检查是否可以发送新验证码（120秒冷却时间）
    if not EmailVerificationCode.can_send_new_code(email, cooldown_seconds=120):
        return False, '请等待120秒后再次获取验证码'

    # 创建新的验证码
    verification_code = EmailVerificationCode(email=email, expires_in_minutes=10)

    try:
        # 保存到数据库
        db.session.add(verification_code)
        db.session.commit()

        # 发送邮件
        send_email(
            subject='[二手交易平台] 邮箱验证码',
            sender=current_app.config['MAIL_DEFAULT_SENDER'],
            recipients=[email],
            text_body=render_template('email/verification_code.txt',
                                    code=verification_code.code),
            html_body=render_template('email/verification_code.html',
                                    code=verification_code.code)
        )

        return True, '验证码已发送，请查收邮件'

    except Exception as e:
        # 如果发送失败，回滚数据库操作
        db.session.rollback()
        current_app.logger.error(f'发送验证码邮件失败: {str(e)}')
        return False, '验证码发送失败，请稍后重试'

def send_change_password_verification_code_email(email):
    """发送修改密码邮箱验证码"""
    from app.models import EmailVerificationCode, db

    # 检查是否可以发送新验证码（120秒冷却时间）
    if not EmailVerificationCode.can_send_new_code(email, cooldown_seconds=120):
        return False, '请等待120秒后再次获取验证码'

    # 创建新的验证码
    verification_code = EmailVerificationCode(email=email, expires_in_minutes=10)

    try:
        # 保存到数据库
        db.session.add(verification_code)
        db.session.commit()

        # 发送邮件
        send_email(
            subject='[二手交易平台] 修改密码验证码',
            sender=current_app.config['MAIL_DEFAULT_SENDER'],
            recipients=[email],
            text_body=render_template('email/change_password_code.txt',
                                    code=verification_code.code),
            html_body=render_template('email/change_password_code.html',
                                    code=verification_code.code)
        )

        return True, '验证码已发送，请查收邮件'

    except Exception as e:
        # 如果发送失败，回滚数据库操作
        db.session.rollback()
        current_app.logger.error(f'发送修改密码验证码邮件失败: {str(e)}')
        return False, '验证码发送失败，请稍后重试'
