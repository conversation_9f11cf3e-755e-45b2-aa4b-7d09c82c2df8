#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证路由
"""

import os
import random
import string
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
from flask import render_template, redirect, url_for, flash, request, current_app, session, make_response
from flask_login import login_user, logout_user, current_user, login_required
from werkzeug.urls import url_parse
from werkzeug.utils import secure_filename
from app import db
from app.auth import bp
from app.auth.forms import (LoginForm, RegistrationForm, EditProfileForm,
                           ChangePasswordForm, ResetPasswordRequestForm, ResetPasswordForm)
from app.models import User
from app.auth.email import send_password_reset_email
from app.utils.file_utils import allowed_file, save_uploaded_file, generate_unique_filename
from datetime import datetime
import uuid

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = LoginForm()
    if form.validate_on_submit():
        # 支持用户名或邮箱登录
        user = User.query.filter(
            (User.username == form.username.data) |
            (User.email == form.username.data)
        ).first()

        if user is None or not user.check_password(form.password.data):
            flash('用户名/邮箱或密码错误', 'error')
            return redirect(url_for('auth.login'))

        if not user.is_active:
            flash('账户已被禁用，请联系管理员', 'error')
            return redirect(url_for('auth.login'))

        login_user(user, remember=form.remember_me.data)
        user.last_seen = datetime.now()
        try:
            db.session.commit()
        except:
            db.session.rollback()

        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('main.index')

        flash(f'欢迎回来，{user.nickname or user.username}！', 'success')

        # 在session中设置标记，表示需要刷新通知和消息
        session['refresh_notifications'] = True
        session['refresh_messages'] = True

        return redirect(next_page)
    else:
        # 如果是POST请求但表单验证失败，显示错误信息
        if request.method == 'POST':
            if form.errors:
                for _, errors in form.errors.items():
                    for error in errors:
                        flash(error, 'error')
            else:
                flash('登录失败，请检查输入信息', 'error')

    return render_template('auth/login.html', title='登录', form=form)

@bp.route('/logout')
def logout():
    """用户登出"""
    # 在登出前将用户的最后活跃时间设为较早时间，确保立即从在线列表中移除
    if current_user.is_authenticated:
        from datetime import datetime, timedelta
        # 将最后活跃时间设为20分钟前，确保不会被计算为在线用户
        current_user.last_seen = datetime.now() - timedelta(minutes=20)
        try:
            db.session.commit()
        except:
            db.session.rollback()

    logout_user()
    flash('您已成功登出', 'info')
    return redirect(url_for('main.index'))

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        # 验证图形验证码
        if form.captcha.data.upper() != session.get('captcha', '').upper():
            flash('图形验证码错误，请重新输入', 'error')
            return render_template('auth/register.html', title='注册', form=form)

        # 验证邮箱验证码
        from app.models import EmailVerificationCode
        verification_code = EmailVerificationCode.get_latest_valid_code(form.email.data)

        if not verification_code:
            flash('邮箱验证码不存在或已过期，请重新获取', 'error')
            return render_template('auth/register.html', title='注册', form=form)

        if verification_code.code != form.email_code.data:
            flash('邮箱验证码错误，请重新输入', 'error')
            return render_template('auth/register.html', title='注册', form=form)

        # 标记验证码为已使用
        verification_code.mark_as_used()

        user = User(
            username=form.username.data,
            email=form.email.data,
            phone=form.phone.data if form.phone.data else None
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()

        # 清除验证码
        session.pop('captcha', None)

        flash('注册成功！请登录', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/register.html', title='注册', form=form)

@bp.route('/captcha')
def captcha():
    """生成验证码图片"""
    # 生成4位随机验证码
    code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
    session['captcha'] = code

    # 创建图片
    width, height = 120, 40
    image = Image.new('RGB', (width, height), color=(255, 255, 255))
    draw = ImageDraw.Draw(image)

    # 添加干扰线
    for _ in range(5):
        x1 = random.randint(0, width)
        y1 = random.randint(0, height)
        x2 = random.randint(0, width)
        y2 = random.randint(0, height)
        draw.line([(x1, y1), (x2, y2)], fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))

    # 添加验证码文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()

    # 绘制验证码字符
    for i, char in enumerate(code):
        x = 20 + i * 20 + random.randint(-5, 5)
        y = 8 + random.randint(-3, 3)
        color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))
        draw.text((x, y), char, font=font, fill=color)

    # 添加干扰点
    for _ in range(100):
        x = random.randint(0, width)
        y = random.randint(0, height)
        draw.point((x, y), fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))

    # 保存到内存
    img_buffer = BytesIO()
    image.save(img_buffer, format='PNG')
    img_buffer.seek(0)

    response = make_response(img_buffer.getvalue())
    response.headers['Content-Type'] = 'image/png'
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

@bp.route('/send_verification_code', methods=['POST'])
def send_verification_code():
    """发送邮箱验证码API"""
    from flask import jsonify
    from app.auth.email import send_verification_code_email
    from app.models import User
    import re

    # 手动跳过CSRF检查
    from flask import g
    g.csrf_valid = True

    try:
        # 获取邮箱地址
        email = request.json.get('email', '').strip()

        if not email:
            return jsonify({'success': False, 'message': '请输入邮箱地址'})

        # 验证邮箱格式
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return jsonify({'success': False, 'message': '请输入有效的邮箱地址'})

        # 检查邮箱是否已被注册
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            return jsonify({'success': False, 'message': '该邮箱已被注册，请使用其他邮箱'})

        # 清理过期验证码（每次发送新验证码时清理）
        from app.models import EmailVerificationCode
        try:
            cleaned_count = EmailVerificationCode.cleanup_expired_codes()
            if cleaned_count > 0:
                current_app.logger.info(f'清理了 {cleaned_count} 个过期验证码')
        except Exception as e:
            current_app.logger.error(f'清理过期验证码失败: {str(e)}')

        # 发送验证码
        success, message = send_verification_code_email(email)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        current_app.logger.error(f'发送验证码API错误: {str(e)}')
        return jsonify({'success': False, 'message': '系统错误，请稍后重试'})

@bp.route('/send_change_password_code', methods=['POST'])
@login_required
def send_change_password_code():
    """发送修改密码邮箱验证码API"""
    from flask import jsonify
    from app.auth.email import send_change_password_verification_code_email

    # 手动跳过CSRF检查
    from flask import g
    g.csrf_valid = True

    try:
        # 验证用户当前密码
        current_password = request.json.get('current_password', '').strip()

        if not current_password:
            return jsonify({'success': False, 'message': '请输入当前密码'})

        if not current_user.check_password(current_password):
            return jsonify({'success': False, 'message': '当前密码错误'})

        # 清理过期验证码
        from app.models import EmailVerificationCode
        try:
            cleaned_count = EmailVerificationCode.cleanup_expired_codes()
            if cleaned_count > 0:
                current_app.logger.info(f'清理了 {cleaned_count} 个过期验证码')
        except Exception as e:
            current_app.logger.error(f'清理过期验证码失败: {str(e)}')

        # 发送验证码到当前用户邮箱
        success, message = send_change_password_verification_code_email(current_user.email)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        current_app.logger.error(f'发送修改密码验证码API错误: {str(e)}')
        return jsonify({'success': False, 'message': '系统错误，请稍后重试'})

@bp.route('/profile')
@login_required
def profile():
    """个人资料页面"""
    from app.models import Order, Review, Product, Favorite
    from sqlalchemy import desc

    # 准备用户数据
    recent_orders = current_user.orders_as_buyer.order_by(desc(Order.created_at)).limit(5).all()
    recent_reviews = current_user.reviews_received.order_by(desc(Review.created_at)).limit(5).all()
    recent_products = current_user.products.order_by(desc(Product.created_at)).limit(5).all()

    # 获取最近收藏的商品
    recent_favorites = db.session.query(Product).join(Favorite).filter(
        Favorite.user_id == current_user.id
    ).order_by(desc(Favorite.created_at)).limit(4).all()

    return render_template('auth/profile.html',
                         title='个人资料',
                         user=current_user,
                         recent_orders=recent_orders,
                         recent_reviews=recent_reviews,
                         recent_products=recent_products,
                         recent_favorites=recent_favorites)

@bp.route('/edit_profile', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """编辑个人资料"""
    form = EditProfileForm(
        original_username=current_user.username,
        original_email=current_user.email,
        original_phone=current_user.phone
    )
    
    if form.validate_on_submit():
        current_user.username = form.username.data
        current_user.email = form.email.data
        current_user.phone = form.phone.data if form.phone.data else None
        current_user.nickname = form.nickname.data
        current_user.bio = form.bio.data
        
        # 处理头像上传
        if form.avatar.data:
            avatar_file = form.avatar.data
            if avatar_file and allowed_file(avatar_file.filename):
                filename = secure_filename(avatar_file.filename)
                # 生成唯一文件名
                if '.' in filename:
                    ext = filename.rsplit('.', 1)[1].lower()
                else:
                    ext = 'jpg'  # 默认扩展名
                filename = f"{uuid.uuid4().hex}.{ext}"
                
                # 确保上传目录存在
                upload_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'avatars')
                if not os.path.exists(upload_dir):
                    os.makedirs(upload_dir)
                
                avatar_path = os.path.join(upload_dir, filename)
                avatar_file.save(avatar_path)
                
                # 删除旧头像
                if current_user.avatar:
                    old_avatar_path = os.path.join(upload_dir, current_user.avatar)
                    if os.path.exists(old_avatar_path):
                        os.remove(old_avatar_path)
                
                current_user.avatar = filename
        
        db.session.commit()
        flash('个人资料已更新', 'success')
        return redirect(url_for('auth.profile'))
    
    elif request.method == 'GET':
        form.username.data = current_user.username
        form.email.data = current_user.email
        form.phone.data = current_user.phone
        form.nickname.data = current_user.nickname
        form.bio.data = current_user.bio
    
    return render_template('auth/edit_profile.html', title='编辑资料', form=form)

@bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    form = ChangePasswordForm()

    if form.validate_on_submit():
        if not current_user.check_password(form.old_password.data):
            flash('当前密码错误', 'error')
            return redirect(url_for('auth.change_password'))

        # 验证邮箱验证码
        from app.models import EmailVerificationCode
        verification_code = EmailVerificationCode.get_latest_valid_code(current_user.email)

        if not verification_code:
            flash('邮箱验证码不存在或已过期，请重新获取', 'error')
            return redirect(url_for('auth.change_password'))

        if verification_code.code != form.email_code.data:
            flash('邮箱验证码错误，请重新输入', 'error')
            return redirect(url_for('auth.change_password'))

        # 标记验证码为已使用
        verification_code.mark_as_used()

        current_user.set_password(form.password.data)
        db.session.commit()
        flash('密码已成功修改', 'success')
        return redirect(url_for('auth.profile'))

    return render_template('auth/change_password.html', title='修改密码', form=form)

@bp.route('/reset_password_request', methods=['GET', 'POST'])
def reset_password_request():
    """重置密码请求"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = ResetPasswordRequestForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user:
            send_password_reset_email(user)
        flash('密码重置邮件已发送，请检查您的邮箱', 'info')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/reset_password_request.html', title='重置密码', form=form)

@bp.route('/reset_password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """重置密码"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    from app.auth.email import verify_reset_password_token
    user = verify_reset_password_token(token)
    if not user:
        flash('无效或已过期的重置链接', 'error')
        return redirect(url_for('auth.reset_password_request'))
    
    form = ResetPasswordForm()
    if form.validate_on_submit():
        user.set_password(form.password.data)
        db.session.commit()
        flash('密码已重置，请使用新密码登录', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/reset_password.html', title='重置密码', form=form)

# allowed_file函数已在文件开头定义，删除重复定义
