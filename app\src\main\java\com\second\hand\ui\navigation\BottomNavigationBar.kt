package com.second.hand.ui.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.outlined.Add
import androidx.compose.material.icons.outlined.List
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.Email
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState

/**
 * 底部导航栏数据类
 */
data class BottomNavItem(
    val route: String,
    val title: String,
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector,
    val hasNews: Boolean = false,
    val badgeCount: Int? = null
)

/**
 * 底部导航栏组件
 */
@Composable
fun BottomNavigationBar(
    navController: NavController,
    items: List<BottomNavItem> = getBottomNavItems()
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    NavigationBar {
        items.forEach { item ->
            val isSelected = currentRoute == item.route
            
            NavigationBarItem(
                icon = {
                    BadgedBox(
                        badge = {
                            if (item.badgeCount != null && item.badgeCount > 0) {
                                Badge {
                                    Text(
                                        text = if (item.badgeCount > 99) "99+" else item.badgeCount.toString()
                                    )
                                }
                            } else if (item.hasNews) {
                                Badge()
                            }
                        }
                    ) {
                        Icon(
                            imageVector = if (isSelected) item.selectedIcon else item.unselectedIcon,
                            contentDescription = item.title
                        )
                    }
                },
                label = { Text(item.title) },
                selected = isSelected,
                onClick = {
                    if (currentRoute != item.route) {
                        navController.navigate(item.route) {
                            // 优化导航配置，避免页面闪烁
                            // 弹出到HOME页面，保持底部导航页面在同一层级
                            popUpTo(NavigationDestinations.HOME) {
                                saveState = true
                                inclusive = false
                            }
                            launchSingleTop = true
                            restoreState = true
                        }
                    }
                }
            )
        }
    }
}

/**
 * 获取底部导航项列表
 */
private fun getBottomNavItems(): List<BottomNavItem> {
    return listOf(
        BottomNavItem(
            route = NavigationDestinations.HOME,
            title = "首页",
            selectedIcon = Icons.Filled.Home,
            unselectedIcon = Icons.Outlined.Home
        ),
        BottomNavItem(
            route = NavigationDestinations.CATEGORY,
            title = "分类",
            selectedIcon = Icons.Filled.List,
            unselectedIcon = Icons.Outlined.List
        ),
        BottomNavItem(
            route = NavigationDestinations.PUBLISH,
            title = "发布",
            selectedIcon = Icons.Filled.Add,
            unselectedIcon = Icons.Outlined.Add
        ),
        BottomNavItem(
            route = NavigationDestinations.MESSAGE,
            title = "消息",
            selectedIcon = Icons.Filled.Email,
            unselectedIcon = Icons.Outlined.Email,
            hasNews = false // 可以根据实际消息状态设置
        ),
        BottomNavItem(
            route = NavigationDestinations.PROFILE,
            title = "我的",
            selectedIcon = Icons.Filled.Person,
            unselectedIcon = Icons.Outlined.Person
        )
    )
}

/**
 * 检查当前路由是否应该显示底部导航栏
 */
fun shouldShowBottomBar(currentRoute: String?): Boolean {
    val bottomNavRoutes = listOf(
        NavigationDestinations.HOME,
        NavigationDestinations.CATEGORY,
        NavigationDestinations.PUBLISH,
        NavigationDestinations.MESSAGE,
        NavigationDestinations.PROFILE
    )
    return currentRoute in bottomNavRoutes
}
