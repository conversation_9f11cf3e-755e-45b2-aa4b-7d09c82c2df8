{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tags"></i> 分类管理</h2>
    <a href="{{ url_for('admin_panel.add_category') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 添加分类
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>分类</th>
                        <th>描述</th>
                        <th>商品数量</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for category in categories %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="{{ category.icon or 'fas fa-tag' }} me-2"></i>
                                <strong>{{ category.name }}</strong>
                            </div>
                        </td>
                        <td>{{ category.description or '无描述' }}</td>
                        <td>
                            <span class="badge bg-info">{{ category.products.count() }}</span>
                        </td>
                        <td>
                            {% if category.is_active %}
                            <span class="badge bg-success">启用</span>
                            {% else %}
                            <span class="badge bg-secondary">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('admin_panel.edit_category', id=category.id) }}" 
                                   class="btn btn-outline-primary" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger" 
                                        onclick="deleteCategory({{ category.id }})"
                                        title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
function deleteCategory(categoryId) {
    if (confirm('确定要删除这个分类吗？')) {
        // 获取CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        fetch(`/admin_panel/api/delete_category/${categoryId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showAlert('分类删除成功', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('删除失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Delete category error:', error);
            showAlert('删除失败: ' + error.message, 'error');
        });
    }
}

// 显示提示信息
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="margin-bottom: 20px; position: relative; z-index: 1050;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 优先在页面内容区域的最顶部显示提示
    const pageTransition = document.querySelector('.page-transition');
    const targetContainer = pageTransition || document.querySelector('.container-fluid') || document.querySelector('.container') || document.body;

    // 移除现有的提示（只移除我们创建的提示，不影响其他alert）
    const existingAlerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
    existingAlerts.forEach(alert => {
        if (alert.style.zIndex === '1050') {
            alert.remove();
        }
    });

    // 在目标容器最顶部插入新提示
    if (pageTransition) {
        // 如果有page-transition容器，在其第一个子元素之前插入
        const firstChild = pageTransition.firstElementChild;
        if (firstChild) {
            firstChild.insertAdjacentHTML('beforebegin', alertHtml);
        } else {
            pageTransition.insertAdjacentHTML('afterbegin', alertHtml);
        }
    } else {
        targetContainer.insertAdjacentHTML('afterbegin', alertHtml);
    }

    // 滚动到页面顶部以确保用户看到提示
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 3秒后自动消失
    setTimeout(() => {
        const alerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
        alerts.forEach(alert => {
            if (alert.style.zIndex === '1050') {
                alert.remove();
            }
        });
    }, 3000);
}
</script>
{% endblock %}
