package com.second.hand.data.api

/**
 * 网络请求结果包装类
 * 统一处理网络请求的成功、失败和加载状态
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val message: String, val code: Int? = null) : NetworkResult<T>()
    class Loading<T> : NetworkResult<T>()
    
    /**
     * 检查是否成功
     */
    fun isSuccess(): Boolean = this is Success
    
    /**
     * 检查是否失败
     */
    fun isError(): Boolean = this is Error
    
    /**
     * 检查是否加载中
     */
    fun isLoading(): Boolean = this is Loading
    
    /**
     * 获取数据（如果成功）
     */
    fun getDataOrNull(): T? = if (this is Success) data else null
    
    /**
     * 获取错误信息（如果失败）
     */
    fun getErrorMessage(): String? = if (this is Error) message else null
}

/**
 * 扩展函数：处理成功情况
 */
inline fun <T> NetworkResult<T>.onSuccess(action: (T) -> Unit): NetworkResult<T> {
    if (this is NetworkResult.Success) {
        action(data)
    }
    return this
}

/**
 * 扩展函数：处理错误情况
 */
inline fun <T> NetworkResult<T>.onError(action: (String, Int?) -> Unit): NetworkResult<T> {
    if (this is NetworkResult.Error) {
        action(message, code)
    }
    return this
}

/**
 * 扩展函数：处理加载情况
 */
inline fun <T> NetworkResult<T>.onLoading(action: () -> Unit): NetworkResult<T> {
    if (this is NetworkResult.Loading) {
        action()
    }
    return this
}
