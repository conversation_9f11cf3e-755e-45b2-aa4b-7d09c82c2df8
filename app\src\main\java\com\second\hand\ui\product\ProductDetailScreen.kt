package com.second.hand.ui.product

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import coil.compose.AsyncImage
import com.second.hand.data.model.Product
import com.second.hand.ui.common.LoadingIndicator
import com.second.hand.ui.common.ErrorMessage
import com.second.hand.BuildConfig
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

/**
 * 商品详情页面
 * 显示商品的详细信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductDetailScreen(
    productId: String,
    navController: NavController,
    viewModel: ProductDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val product by viewModel.product.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    // 加载商品详情
    LaunchedEffect(productId) {
        viewModel.loadProductDetail(productId.toIntOrNull() ?: 0)
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部导航栏
        TopAppBar(
            title = { Text("商品详情") },
            navigationIcon = {
                IconButton(onClick = { navController.navigateUp() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(onClick = { /* TODO: 分享功能 */ }) {
                    Icon(Icons.Default.Share, contentDescription = "分享")
                }
            }
        )

        // 内容区域
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            when {
                isLoading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center),
                        message = "加载商品详情..."
                    )
                }
                error != null -> {
                    ErrorMessage(
                        message = error ?: "加载失败",
                        modifier = Modifier.align(Alignment.Center),
                        onRetry = { viewModel.loadProductDetail(productId.toIntOrNull() ?: 0) }
                    )
                }
                product != null -> {
                    ProductDetailContent(
                        product = product!!,
                        onFavoriteClick = { viewModel.toggleFavorite() },
                        onContactSeller = { /* TODO: 联系卖家 */ },
                        onBuyNow = { /* TODO: 立即购买 */ }
                    )
                }
            }
        }
    }
}

@Composable
private fun ProductDetailContent(
    product: Product,
    onFavoriteClick: () -> Unit,
    onContactSeller: () -> Unit,
    onBuyNow: () -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(bottom = 80.dp) // 为底部按钮留空间
    ) {
        // 商品图片轮播
        item {
            ProductImageCarousel(
                images = product.getAllImageUrls(BuildConfig.IMAGE_BASE_URL),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp)
            )
        }

        // 商品基本信息
        item {
            ProductBasicInfo(
                product = product,
                onFavoriteClick = onFavoriteClick,
                modifier = Modifier.padding(16.dp)
            )
        }

        // 商品描述
        item {
            ProductDescription(
                description = product.description,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }

        // 卖家信息
        item {
            SellerInfo(
                seller = product.seller,
                modifier = Modifier.padding(16.dp)
            )
        }
    }

    // 底部操作栏
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        BottomActionBar(
            onContactSeller = onContactSeller,
            onBuyNow = onBuyNow,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}

@Composable
private fun ProductImageCarousel(
    images: List<String>,
    modifier: Modifier = Modifier
) {
    if (images.isNotEmpty()) {
        LazyRow(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            items(images) { imageUrl ->
                AsyncImage(
                    model = imageUrl,
                    contentDescription = "商品图片",
                    modifier = Modifier
                        .fillMaxHeight()
                        .aspectRatio(1f)
                        .clip(RoundedCornerShape(8.dp)),
                    contentScale = ContentScale.Crop
                )
            }
        }
    } else {
        // 默认图片
        Card(
            modifier = modifier.padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "暂无图片",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun ProductBasicInfo(
    product: Product,
    onFavoriteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
    ) {
        // 标题和收藏按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Top
        ) {
            Text(
                text = product.title,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.weight(1f),
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )
            
            IconButton(onClick = onFavoriteClick) {
                Icon(
                    imageVector = if (product.isFavorited) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                    contentDescription = if (product.isFavorited) "取消收藏" else "收藏",
                    tint = if (product.isFavorited) Color.Red else MaterialTheme.colorScheme.onSurface
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 价格信息
        Row(
            verticalAlignment = Alignment.Bottom
        ) {
            Text(
                text = "¥${NumberFormat.getNumberInstance(Locale.CHINA).format(product.price)}",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            product.originalPrice?.let { originalPrice ->
                if (originalPrice > product.price) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "¥${NumberFormat.getNumberInstance(Locale.CHINA).format(originalPrice)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textDecoration = androidx.compose.ui.text.style.TextDecoration.LineThrough
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // 商品属性
        ProductAttributes(product = product)
    }
}

@Composable
private fun ProductAttributes(
    product: Product
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 位置信息
        product.location?.let { location ->
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.LocationOn,
                    contentDescription = "位置",
                    modifier = Modifier.size(16.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = location,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 商品状态
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "商品状态：",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = product.getStatusDisplay(),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }

        // 浏览量
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "浏览量：",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "${product.viewCount}次",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun ProductDescription(
    description: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "商品描述",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = description.ifBlank { "暂无描述" },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun SellerInfo(
    seller: com.second.hand.data.model.User?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 卖家头像
            Card(
                modifier = Modifier.size(48.dp),
                shape = RoundedCornerShape(24.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    if (seller?.avatar != null) {
                        AsyncImage(
                            model = "${BuildConfig.IMAGE_BASE_URL}/static/uploads/avatars/${seller.avatar}",
                            contentDescription = "卖家头像",
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop
                        )
                    } else {
                        Icon(
                            Icons.Default.Person,
                            contentDescription = "默认头像",
                            tint = Color.White
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 卖家信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = seller?.nickname ?: "未知用户",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = "点击查看详情",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 联系按钮
            OutlinedButton(
                onClick = { /* TODO: 联系卖家 */ }
            ) {
                Text("联系")
            }
        }
    }
}

@Composable
private fun BottomActionBar(
    onContactSeller: () -> Unit,
    onBuyNow: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        shadowElevation = 8.dp,
        color = MaterialTheme.colorScheme.surface
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 联系卖家按钮
            OutlinedButton(
                onClick = onContactSeller,
                modifier = Modifier.weight(1f)
            ) {
                Text("联系卖家")
            }

            // 立即购买按钮
            Button(
                onClick = onBuyNow,
                modifier = Modifier.weight(1f)
            ) {
                Text("立即购买")
            }
        }
    }
}
