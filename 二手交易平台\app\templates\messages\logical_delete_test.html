{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4><i class="fas fa-user-friends"></i> 逻辑删除功能测试</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 新的删除逻辑说明</h5>
                        <ul class="mb-0">
                            <li><strong>独立删除</strong>：每个用户可以独立删除自己看到的对话</li>
                            <li><strong>消息保留</strong>：删除不会影响对方的消息记录</li>
                            <li><strong>重新联系</strong>：删除后仍可以重新发送消息</li>
                            <li><strong>历史隔离</strong>：删除方只能看到删除后的新消息</li>
                        </ul>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>🧪 测试功能</h6>
                            <div class="card">
                                <div class="card-body">
                                    <h6>创建测试消息</h6>
                                    <form id="createTestMessage">
                                        <div class="mb-3">
                                            <label class="form-label">接收方用户ID</label>
                                            <input type="number" class="form-control" id="receiverId" placeholder="输入用户ID" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">商品ID (可选)</label>
                                            <input type="number" class="form-control" id="productId" placeholder="留空表示无商品">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">消息内容</label>
                                            <textarea class="form-control" id="messageContent" rows="3" placeholder="输入测试消息内容" required></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> 发送测试消息
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>🗑️ 删除测试</h6>
                            <div class="card">
                                <div class="card-body">
                                    <h6>逻辑删除对话</h6>
                                    <form id="deleteTestForm">
                                        <div class="mb-3">
                                            <label class="form-label">对方用户ID</label>
                                            <input type="number" class="form-control" id="deleteUserId" placeholder="输入用户ID" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">商品ID (可选)</label>
                                            <input type="number" class="form-control" id="deleteProductId" placeholder="留空表示无商品">
                                        </div>
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> 逻辑删除对话
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6>📋 当前对话列表</h6>
                            <button class="btn btn-outline-primary btn-sm mb-3" onclick="loadConversations()">
                                <i class="fas fa-refresh"></i> 刷新对话列表
                            </button>
                            <div id="conversationsList">
                                <div class="text-muted">点击刷新按钮加载对话列表...</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6>📝 操作日志</h6>
                            <div id="operationLog" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                                等待操作...
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('messages.index') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> 返回消息中心
                        </a>
                        <a href="{{ url_for('messages.delete_test') }}" class="btn btn-secondary">
                            <i class="fas fa-tools"></i> 物理删除测试
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 发送测试消息
    $('#createTestMessage').on('submit', function(e) {
        e.preventDefault();
        
        const receiverId = $('#receiverId').val();
        const productId = $('#productId').val();
        const content = $('#messageContent').val();
        
        if (!receiverId || !content) {
            alert('请填写接收方ID和消息内容');
            return;
        }
        
        sendTestMessage(receiverId, productId, content);
    });
    
    // 删除对话
    $('#deleteTestForm').on('submit', function(e) {
        e.preventDefault();
        
        const userId = $('#deleteUserId').val();
        const productId = $('#deleteProductId').val();
        
        if (!userId) {
            alert('请输入用户ID');
            return;
        }
        
        deleteConversation(userId, productId);
    });
    
    // 自动加载对话列表
    loadConversations();
});

function logMessage(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logDiv = $('#operationLog');
    const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
    
    logDiv.append(`<div class="${colorClass}">[${timestamp}] ${message}</div>`);
    logDiv.scrollTop(logDiv[0].scrollHeight);
}

function sendTestMessage(receiverId, productId, content) {
    logMessage(`发送测试消息: 接收方=${receiverId}, 商品=${productId || '无'}, 内容="${content}"`, 'info');
    
    $.ajax({
        url: '/messages/send_message',
        method: 'POST',
        data: {
            receiver_id: receiverId,
            product_id: productId || '',
            content: content,
            csrf_token: $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                logMessage('消息发送成功！', 'success');
                $('#messageContent').val('');
                setTimeout(loadConversations, 1000);
            } else {
                logMessage('消息发送失败: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            logMessage('消息发送失败: ' + error, 'error');
        }
    });
}

function deleteConversation(userId, productId) {
    logMessage(`逻辑删除对话: 用户=${userId}, 商品=${productId || '无'}`, 'info');
    
    $.ajax({
        url: '/messages/api/delete_conversation',
        method: 'POST',
        data: {
            user_id: userId,
            product_id: productId || '',
            csrf_token: $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                logMessage('对话逻辑删除成功: ' + response.message, 'success');
                setTimeout(loadConversations, 1000);
            } else {
                logMessage('删除失败: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            logMessage('删除失败: ' + error, 'error');
        }
    });
}

function loadConversations() {
    logMessage('加载对话列表...', 'info');
    
    $.ajax({
        url: '/messages/api/conversations',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                logMessage(`加载成功，找到 ${response.conversations.length} 个对话`, 'success');
                displayConversations(response.conversations);
            } else {
                logMessage('加载对话列表失败: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            logMessage('加载对话列表失败: ' + error, 'error');
        }
    });
}

function displayConversations(conversations) {
    const container = $('#conversationsList');
    
    if (conversations.length === 0) {
        container.html('<div class="alert alert-warning">暂无对话</div>');
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>对方用户</th><th>商品</th><th>最后消息</th><th>未读数</th><th>操作</th></tr></thead><tbody>';
    
    conversations.forEach(function(conv) {
        const productInfo = conv.product ? `${conv.product.title} (ID: ${conv.product.id})` : '无关联商品';
        const lastMessage = conv.last_message ? conv.last_message.content.substring(0, 30) + '...' : '暂无消息';
        const unreadCount = conv.unread_count > 0 ? `<span class="badge bg-danger">${conv.unread_count}</span>` : '0';
        
        html += `
            <tr>
                <td>
                    <strong>${conv.other_user.nickname || conv.other_user.username}</strong><br>
                    <small class="text-muted">ID: ${conv.other_user.id}</small>
                </td>
                <td>${productInfo}</td>
                <td>
                    <small>${lastMessage}</small><br>
                    <small class="text-muted">${conv.last_time}</small>
                </td>
                <td>${unreadCount}</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="deleteConversation(${conv.other_user.id}, '${conv.product ? conv.product.id : ''}')">
                        逻辑删除
                    </button>
                    <a href="/messages/chat/${conv.other_user.id}${conv.product ? '/' + conv.product.id : ''}" class="btn btn-sm btn-primary">
                        查看
                    </a>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.html(html);
}
</script>
{% endblock %}
