package com.second.hand.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.second.hand.ui.auth.AuthViewModel
import com.second.hand.ui.auth.WelcomeScreen
import com.second.hand.ui.category.CategoryScreen
import com.second.hand.ui.debug.AppStatusScreen
import com.second.hand.ui.debug.AuthTestScreen
import com.second.hand.ui.debug.TokenDebugScreen
import com.second.hand.ui.debug.TokenRefreshTestScreen
import com.second.hand.ui.home.HomeScreen
import com.second.hand.ui.message.MessageScreen
import com.second.hand.ui.profile.ProfileScreen
import com.second.hand.ui.publish.PublishScreen
import com.second.hand.ui.product.ProductListScreen
import com.second.hand.ui.product.ProductDetailScreen
import com.second.hand.ui.search.SearchScreen

/**
 * 应用主导航组件
 * 管理所有页面的导航逻辑
 */
@Composable
fun AppNavigation(
    navController: NavHostController,
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val authUiState by authViewModel.uiState.collectAsState()

    // 使用固定的起始页面，避免动态startDestination导致的闪烁
    // 在WelcomeScreen中根据登录状态自动导航
    NavHost(
        navController = navController,
        startDestination = NavigationDestinations.WELCOME
    ) {
        // 认证相关页面
        composable(NavigationDestinations.WELCOME) {
            WelcomeScreen(
                viewModel = authViewModel,
                onNavigateToMain = {
                    navController.navigate(NavigationDestinations.HOME) {
                        popUpTo(NavigationDestinations.WELCOME) { inclusive = true }
                    }
                },
                navController = navController
            )
        }

        // 主要页面（底部导航）
        composable(NavigationDestinations.HOME) {
            HomeScreen(
                navController = navController,
                onNavigateToWelcome = {
                    navController.navigate(NavigationDestinations.WELCOME) {
                        popUpTo(0) { inclusive = true }
                    }
                }
            )
        }

        composable(NavigationDestinations.CATEGORY) {
            CategoryScreen(
                navController = navController
            )
        }

        composable(NavigationDestinations.PUBLISH) {
            PublishScreen(
                navController = navController
            )
        }

        composable(NavigationDestinations.MESSAGE) {
            MessageScreen(
                navController = navController
            )
        }

        composable(NavigationDestinations.PROFILE) {
            ProfileScreen(
                navController = navController,
                authViewModel = authViewModel,
                onNavigateToWelcome = {
                    navController.navigate(NavigationDestinations.WELCOME) {
                        popUpTo(0) { inclusive = true }
                    }
                }
            )
        }

        // 详情页面（带参数）
        composable(
            route = NavigationDestinations.PRODUCT_DETAIL_WITH_ID,
            arguments = listOf(
                navArgument(NavigationDestinations.PRODUCT_ID_KEY) {
                    type = NavType.StringType
                }
            )
        ) { backStackEntry ->
            val productId = backStackEntry.arguments?.getString(NavigationDestinations.PRODUCT_ID_KEY) ?: ""
            ProductDetailScreen(
                productId = productId,
                navController = navController
            )
        }

        composable(
            route = NavigationDestinations.USER_PROFILE_WITH_ID,
            arguments = listOf(
                navArgument(NavigationDestinations.USER_ID_KEY) {
                    type = NavType.StringType
                }
            )
        ) { backStackEntry ->
            val userId = backStackEntry.arguments?.getString(NavigationDestinations.USER_ID_KEY) ?: ""
            // TODO: 实现用户详情页面
            // UserProfileScreen(userId = userId, navController = navController)
        }

        composable(
            route = NavigationDestinations.ORDER_DETAIL_WITH_ID,
            arguments = listOf(
                navArgument(NavigationDestinations.ORDER_ID_KEY) {
                    type = NavType.StringType
                }
            )
        ) { backStackEntry ->
            val orderId = backStackEntry.arguments?.getString(NavigationDestinations.ORDER_ID_KEY) ?: ""
            // TODO: 实现订单详情页面
            // OrderDetailScreen(orderId = orderId, navController = navController)
        }

        composable(
            route = NavigationDestinations.CHAT_WITH_USER,
            arguments = listOf(
                navArgument(NavigationDestinations.USER_ID_KEY) {
                    type = NavType.StringType
                }
            )
        ) { backStackEntry ->
            val userId = backStackEntry.arguments?.getString(NavigationDestinations.USER_ID_KEY) ?: ""
            // TODO: 实现聊天页面
            // ChatScreen(userId = userId, navController = navController)
        }

        // 其他功能页面
        composable(NavigationDestinations.PRODUCT_LIST) {
            ProductListScreen(
                onProductClick = { product ->
                    navController.navigate(NavigationDestinations.productDetail(product.id.toString()))
                },
                onSearchClick = {
                    navController.navigate(NavigationDestinations.SEARCH)
                },
                onFilterClick = {
                    // TODO: 实现筛选页面
                }
            )
        }

        composable(NavigationDestinations.SEARCH) {
            SearchScreen(navController = navController)
        }

        composable(NavigationDestinations.FAVORITES) {
            // TODO: 实现收藏页面
            // FavoritesScreen(navController = navController)
        }

        composable(NavigationDestinations.MY_PRODUCTS) {
            // TODO: 实现我的商品页面
            // MyProductsScreen(navController = navController)
        }

        composable(NavigationDestinations.ORDERS) {
            // TODO: 实现订单列表页面
            // OrdersScreen(navController = navController)
        }

        composable(NavigationDestinations.SETTINGS) {
            // TODO: 实现设置页面
            // SettingsScreen(navController = navController)
        }

        composable(NavigationDestinations.EDIT_PROFILE) {
            // TODO: 实现编辑资料页面
            // EditProfileScreen(navController = navController)
        }

        // 调试页面
        composable(NavigationDestinations.AUTH_TEST) {
            AuthTestScreen(authViewModel = authViewModel)
        }

        composable(NavigationDestinations.TOKEN_DEBUG) {
            TokenDebugScreen(authViewModel = authViewModel)
        }

        composable(NavigationDestinations.APP_STATUS) {
            AppStatusScreen(
                navController = navController,
                authViewModel = authViewModel
            )
        }

        composable(NavigationDestinations.TOKEN_REFRESH_TEST) {
            TokenRefreshTestScreen(authViewModel = authViewModel)
        }
    }
}
