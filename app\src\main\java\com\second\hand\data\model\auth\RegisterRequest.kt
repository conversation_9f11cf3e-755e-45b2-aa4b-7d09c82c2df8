package com.second.hand.data.model.auth

import com.google.gson.annotations.SerializedName

/**
 * 注册请求数据模型
 */
data class RegisterRequest(
    @SerializedName("username")
    val username: String,
    
    @SerializedName("email")
    val email: String,
    
    @SerializedName("password")
    val password: String,
    
    @SerializedName("emailCode")
    val emailCode: String,
    
    @SerializedName("nickname")
    val nickname: String? = null
)
