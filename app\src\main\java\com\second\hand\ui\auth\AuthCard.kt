package com.second.hand.ui.auth

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.second.hand.ui.components.ErrorMessageCard

/**
 * 认证卡片组件
 * 包含登录和注册表单
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AuthCard(
    isLoginMode: Boolean,
    isLoading: Boolean,
    error: String?,
    onLogin: (String, String) -> Unit,
    onRegister: (String, String, String, String, String) -> Unit, // 添加邮箱验证码参数
    onToggleMode: () -> Unit,
    onClearError: () -> Unit,
    onSendEmailCode: ((String) -> Unit)? = null // 添加发送验证码回调
) {
    var username by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var emailCode by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    var confirmPasswordVisible by remember { mutableStateOf(false) }
    var isEmailCodeSent by remember { mutableStateOf(false) }
    
    val focusManager = LocalFocusManager.current
    
    // 清除表单数据当模式切换时
    LaunchedEffect(isLoginMode) {
        username = ""
        email = ""
        password = ""
        confirmPassword = ""
        emailCode = ""
        passwordVisible = false
        confirmPasswordVisible = false
        isEmailCodeSent = false
        onClearError()
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 标题
            Text(
                text = if (isLoginMode) "登录账户" else "注册账户",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 用户名输入框
            OutlinedTextField(
                value = username,
                onValueChange = { 
                    username = it
                    onClearError()
                },
                label = { Text("用户名") },
                leadingIcon = {
                    Icon(Icons.Default.Person, contentDescription = "用户名")
                },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,
                    imeAction = if (isLoginMode) ImeAction.Next else ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) }
                ),
                enabled = !isLoading
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 邮箱输入框（仅注册时显示）
            if (!isLoginMode) {
                OutlinedTextField(
                    value = email,
                    onValueChange = {
                        email = it
                        onClearError()
                    },
                    label = { Text("邮箱") },
                    leadingIcon = {
                        Icon(Icons.Default.Email, contentDescription = "邮箱")
                    },
                    trailingIcon = {
                        if (onSendEmailCode != null) {
                            TextButton(
                                onClick = {
                                    onSendEmailCode(email)
                                    isEmailCodeSent = true
                                },
                                enabled = !isLoading && email.isNotBlank()
                            ) {
                                Text(
                                    text = if (isEmailCodeSent) "已发送" else "发送验证码",
                                    style = MaterialTheme.typography.labelSmall
                                )
                            }
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Email,
                        imeAction = ImeAction.Next
                    ),
                    keyboardActions = KeyboardActions(
                        onNext = { focusManager.moveFocus(FocusDirection.Down) }
                    ),
                    enabled = !isLoading
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 邮箱验证码输入框
                OutlinedTextField(
                    value = emailCode,
                    onValueChange = {
                        emailCode = it
                        onClearError()
                    },
                    label = { Text("邮箱验证码") },
                    leadingIcon = {
                        Icon(Icons.Default.Lock, contentDescription = "验证码")
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Number,
                        imeAction = ImeAction.Next
                    ),
                    keyboardActions = KeyboardActions(
                        onNext = { focusManager.moveFocus(FocusDirection.Down) }
                    ),
                    enabled = !isLoading,
                    placeholder = { Text("请输入6位数字验证码") }
                )

                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 密码输入框
            OutlinedTextField(
                value = password,
                onValueChange = { 
                    password = it
                    onClearError()
                },
                label = { Text("密码") },
                leadingIcon = {
                    Icon(Icons.Default.Lock, contentDescription = "密码")
                },
                trailingIcon = {
                    IconButton(onClick = { passwordVisible = !passwordVisible }) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = if (passwordVisible) "隐藏密码" else "显示密码"
                        )
                    }
                },
                visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Password,
                    imeAction = if (isLoginMode) ImeAction.Done else ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) },
                    onDone = { 
                        if (isLoginMode) {
                            onLogin(username, password)
                        } else {
                            focusManager.moveFocus(FocusDirection.Down)
                        }
                    }
                ),
                enabled = !isLoading
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 确认密码输入框（仅注册时显示）
            if (!isLoginMode) {
                OutlinedTextField(
                    value = confirmPassword,
                    onValueChange = { 
                        confirmPassword = it
                        onClearError()
                    },
                    label = { Text("确认密码") },
                    leadingIcon = {
                        Icon(Icons.Default.Lock, contentDescription = "确认密码")
                    },
                    trailingIcon = {
                        IconButton(onClick = { confirmPasswordVisible = !confirmPasswordVisible }) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = if (confirmPasswordVisible) "隐藏密码" else "显示密码"
                            )
                        }
                    },
                    visualTransformation = if (confirmPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Password,
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = { onRegister(username, email, password, confirmPassword, emailCode) }
                    ),
                    enabled = !isLoading
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 错误信息
            error?.let { errorMessage ->
                ErrorMessageCard(
                    message = errorMessage,
                    onDismiss = onClearError
                )
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 提交按钮
            Button(
                onClick = {
                    if (isLoginMode) {
                        onLogin(username, password)
                    } else {
                        onRegister(username, email, password, confirmPassword, emailCode)
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading && username.isNotBlank() && password.isNotBlank() &&
                        (isLoginMode || (email.isNotBlank() && confirmPassword.isNotBlank() && emailCode.isNotBlank()))
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text(if (isLoginMode) "登录" else "注册")
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 切换模式按钮
            TextButton(
                onClick = onToggleMode,
                enabled = !isLoading
            ) {
                Text(
                    if (isLoginMode) "还没有账户？点击注册" else "已有账户？点击登录"
                )
            }
        }
    }
}
