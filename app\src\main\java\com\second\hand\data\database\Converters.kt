package com.second.hand.data.database

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.second.hand.data.model.ProductImage
import com.second.hand.data.model.enums.UserRole
import com.second.hand.data.model.enums.ProductStatus
import com.second.hand.data.model.enums.OrderStatus
import com.second.hand.data.model.enums.NotificationType

/**
 * Room数据库类型转换器
 * 用于转换复杂数据类型
 */
class Converters {
    
    private val gson = Gson()
    
    // UserRole转换
    @TypeConverter
    fun fromUserRole(role: UserRole): String {
        return role.name
    }
    
    @TypeConverter
    fun toUserRole(role: String): UserRole {
        return UserRole.valueOf(role)
    }
    
    // ProductStatus转换
    @TypeConverter
    fun fromProductStatus(status: ProductStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toProductStatus(status: String): ProductStatus {
        return ProductStatus.valueOf(status)
    }
    
    // OrderStatus转换
    @TypeConverter
    fun fromOrderStatus(status: OrderStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toOrderStatus(status: String): OrderStatus {
        return OrderStatus.valueOf(status)
    }
    
    // NotificationType转换
    @TypeConverter
    fun fromNotificationType(type: NotificationType): String {
        return type.name
    }
    
    @TypeConverter
    fun toNotificationType(type: String): NotificationType {
        return NotificationType.valueOf(type)
    }
    
    // List<ProductImage>转换
    @TypeConverter
    fun fromProductImageList(images: List<ProductImage>?): String? {
        return images?.let { gson.toJson(it) }
    }
    
    @TypeConverter
    fun toProductImageList(imagesJson: String?): List<ProductImage>? {
        return imagesJson?.let {
            val type = object : TypeToken<List<ProductImage>>() {}.type
            gson.fromJson(it, type)
        }
    }
    
    // List<String>转换
    @TypeConverter
    fun fromStringList(strings: List<String>?): String? {
        return strings?.let { gson.toJson(it) }
    }
    
    @TypeConverter
    fun toStringList(stringsJson: String?): List<String>? {
        return stringsJson?.let {
            val type = object : TypeToken<List<String>>() {}.type
            gson.fromJson(it, type)
        }
    }
}
