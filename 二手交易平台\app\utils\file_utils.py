#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理工具模块
"""

import os
import uuid
from flask import current_app
from werkzeug.utils import secure_filename


def allowed_file(filename):
    """检查文件扩展名是否允许"""
    if not filename or '.' not in filename:
        return False

    ext = filename.rsplit('.', 1)[1].lower()
    allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', {'jpg', 'jpeg', 'png', 'gif'})
    return ext in allowed_extensions


def get_file_extension(filename):
    """获取文件扩展名"""
    if not filename or '.' not in filename:
        return ''
    return filename.rsplit('.', 1)[1].lower()


def generate_unique_filename(original_filename):
    """生成唯一的文件名"""
    if not original_filename:
        return str(uuid.uuid4())
    
    # 获取文件扩展名
    ext = get_file_extension(original_filename)
    
    # 生成唯一文件名
    unique_name = str(uuid.uuid4())
    
    if ext:
        return f"{unique_name}.{ext}"
    return unique_name


def save_uploaded_file(file, upload_dir, filename=None):
    """保存上传的文件
    
    Args:
        file: 上传的文件对象
        upload_dir: 上传目录
        filename: 指定的文件名（可选）
    
    Returns:
        tuple: (success, filename_or_error_message)
    """
    if not file or not file.filename:
        return False, "没有选择文件"
    
    if not allowed_file(file.filename):
        return False, "不支持的文件类型"
    
    # 确保上传目录存在
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
    
    # 生成安全的文件名
    if filename:
        safe_filename = secure_filename(filename)
    else:
        safe_filename = generate_unique_filename(file.filename)
    
    # 保存文件
    try:
        file_path = os.path.join(upload_dir, safe_filename)
        file.save(file_path)
        return True, safe_filename
    except Exception as e:
        return False, f"文件保存失败: {str(e)}"


def delete_file(file_path):
    """删除文件
    
    Args:
        file_path: 文件路径
    
    Returns:
        bool: 删除是否成功
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception:
        return False


def get_file_size(file_path):
    """获取文件大小（字节）"""
    try:
        if os.path.exists(file_path):
            return os.path.getsize(file_path)
        return 0
    except Exception:
        return 0


def format_file_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def is_image_file(filename):
    """检查是否为图片文件"""
    if not filename:
        return False
    
    image_extensions = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'}
    ext = get_file_extension(filename)
    return ext in image_extensions


def create_upload_directories():
    """创建所有必要的上传目录"""
    upload_dirs = [
        'app/static/uploads',
        'app/static/uploads/products',
        'app/static/uploads/avatars'
    ]
    
    for upload_dir in upload_dirs:
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)
            print(f"✓ 创建目录: {upload_dir}")
