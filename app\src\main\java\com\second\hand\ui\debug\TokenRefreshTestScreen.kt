package com.second.hand.ui.debug

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.second.hand.ui.auth.AuthViewModel
import com.second.hand.ui.components.AutoDismissTokenRefreshIndicator
import com.second.hand.ui.components.InfoMessageCard
import com.second.hand.ui.components.SuccessMessageCard
import kotlinx.coroutines.delay

/**
 * Token刷新测试界面
 * 用于测试和演示自动Token刷新机制
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TokenRefreshTestScreen(
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val authUiState by authViewModel.uiState.collectAsState()
    val tokenState by authViewModel.getTokenState().collectAsState()
    val refreshStatus by authViewModel.getRefreshStatus().collectAsState()
    val refreshMessage by authViewModel.getRefreshUserMessage().collectAsState()
    val tokenHealthStatus = authViewModel.getTokenHealthStatus()
    val healthDescription = authViewModel.getTokenHealthDescription()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "🔄 Token刷新机制测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        // Token刷新状态指示器
        AutoDismissTokenRefreshIndicator(
            refreshStatus = refreshStatus,
            userMessage = refreshMessage,
            tokenHealthStatus = tokenHealthStatus,
            onDismiss = { authViewModel.clearRefreshMessage() }
        )
        
        // 当前状态概览
        CurrentStatusOverview(authUiState, tokenState, tokenHealthStatus, healthDescription)
        
        // 测试操作按钮
        TestActionsCard(authViewModel)
        
        // 刷新机制说明
        RefreshMechanismInfo()
        
        // 测试场景
        TestScenariosCard(authViewModel)
    }
}

@Composable
private fun CurrentStatusOverview(
    authUiState: com.second.hand.ui.auth.AuthUiState,
    tokenState: com.second.hand.data.auth.TokenState,
    tokenHealthStatus: com.second.hand.data.auth.TokenHealthStatus,
    healthDescription: String
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "当前状态概览",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            StatusRow("登录状态", if (authUiState.isLoggedIn) "✅ 已登录" else "❌ 未登录")
            StatusRow("Token有效性", if (tokenState.isValid) "✅ 有效" else "❌ 无效")
            StatusRow("需要刷新", if (tokenState.shouldRefresh) "⚠️ 是" else "✅ 否")
            StatusRow("Token健康", healthDescription)
            
            if (tokenState.expiryTime > 0) {
                val remainingMinutes = tokenState.remainingTime / (1000 * 60)
                StatusRow("剩余时间", "${remainingMinutes}分钟")
            }
        }
    }
}

@Composable
private fun StatusRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun TestActionsCard(authViewModel: AuthViewModel) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "测试操作",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { authViewModel.manualRefreshToken() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("手动刷新")
                }
                
                Button(
                    onClick = { authViewModel.refreshTokenState() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("刷新状态")
                }
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { authViewModel.checkLoginStatus() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("检查登录")
                }
                
                Button(
                    onClick = { authViewModel.clearRefreshMessage() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("清除消息")
                }
            }
        }
    }
}

@Composable
private fun RefreshMechanismInfo() {
    InfoMessageCard(
        message = """
            🔄 自动Token刷新机制说明：
            
            1. 主动刷新：Token过期前5分钟自动刷新
            2. 被动刷新：收到401响应时尝试刷新
            3. 并发控制：同时只允许一个刷新请求
            4. 重试机制：最多重试3次，间隔30秒
            5. 降级处理：刷新失败时自动登出
            6. 用户反馈：显示刷新状态和错误信息
        """.trimIndent()
    )
}

@Composable
private fun TestScenariosCard(authViewModel: AuthViewModel) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "测试场景",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "以下是可以测试的场景：",
                style = MaterialTheme.typography.bodyMedium
            )
            
            val scenarios = listOf(
                "✅ 正常刷新：Token即将过期时的自动刷新",
                "⚠️ 网络异常：网络不可用时的刷新处理",
                "❌ 刷新失败：Refresh Token无效时的降级处理",
                "🔄 并发请求：多个请求同时触发刷新的处理",
                "⏰ 重试机制：刷新失败后的重试逻辑",
                "🔐 自动登出：刷新失败达到上限时的自动登出"
            )
            
            scenarios.forEach { scenario ->
                Text(
                    text = scenario,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            SuccessMessageCard(
                message = "💡 提示：登录后可以通过手动刷新按钮测试刷新机制，或等待Token自然过期观察自动刷新行为。"
            )
        }
    }
}
