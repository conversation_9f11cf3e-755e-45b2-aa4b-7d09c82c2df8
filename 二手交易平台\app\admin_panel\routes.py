#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员后台路由
"""

from flask import render_template, redirect, url_for, flash, request, current_app, abort, jsonify
from flask_login import login_required, current_user
from functools import wraps
import sys
import flask
from app import db
from app.admin_panel import bp
from app.admin_panel.forms import (UserManageForm, ProductAuditForm, CategoryManageForm,
                                  SystemConfigForm, FeedbackReplyForm)
from app.models import (User, Product, Category, Order, Review, Feedback, SystemConfig,
                       UserRole, ProductStatus, OrderStatus, Notification)
from sqlalchemy import desc, func
from datetime import datetime, timedelta
from app.utils.datetime_utils import local_now, get_online_users_count
import os

def update_env_file(updated_keys, form_data):
    """更新.env文件中的邮件配置"""
    env_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')

    # 邮件配置映射
    env_key_mapping = {
        'mail_server': 'MAIL_SERVER',
        'mail_port': 'MAIL_PORT',
        'mail_username': 'MAIL_USERNAME',
        'mail_password': 'MAIL_PASSWORD',
        'mail_use_tls': 'MAIL_USE_TLS'
    }

    if not os.path.exists(env_file_path):
        print(f".env file not found at {env_file_path}")
        return

    # 读取现有的.env文件
    with open(env_file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 更新配置
    updated_lines = []
    updated_env_keys = set()

    for line in lines:
        line = line.strip()
        if '=' in line and not line.startswith('#'):
            key = line.split('=')[0].strip()
            # 检查是否需要更新这个配置
            for config_key in updated_keys:
                if config_key in env_key_mapping and env_key_mapping[config_key] == key:
                    new_value = form_data.get(config_key, '')
                    # 处理布尔值
                    if config_key == 'mail_use_tls':
                        new_value = 'true' if new_value.lower() == 'true' else 'false'
                    # 处理密码字段（如果为空则保持原值）
                    elif config_key == 'mail_password' and not new_value:
                        updated_lines.append(line + '\n')
                        updated_env_keys.add(key)
                        break
                    updated_lines.append(f"{key}={new_value}\n")
                    updated_env_keys.add(key)
                    print(f"Updated .env: {key}={new_value}")
                    break
            else:
                updated_lines.append(line + '\n')
        else:
            updated_lines.append(line + '\n')

    # 添加缺失的配置项
    for config_key in updated_keys:
        if config_key in env_key_mapping:
            env_key = env_key_mapping[config_key]
            if env_key not in updated_env_keys:
                new_value = form_data.get(config_key, '')
                if config_key == 'mail_use_tls':
                    new_value = 'true' if new_value.lower() == 'true' else 'false'
                elif config_key == 'mail_password' and not new_value:
                    continue  # 跳过空密码
                updated_lines.append(f"{env_key}={new_value}\n")
                print(f"Added to .env: {env_key}={new_value}")

    # 写回文件
    with open(env_file_path, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)

    print(f"Successfully updated .env file at {env_file_path}")

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """管理员仪表板"""
    # 统计数据
    total_users = User.query.count()
    total_products = Product.query.count()
    total_orders = Order.query.count()
    pending_products = Product.query.filter_by(status=ProductStatus.PENDING).count()

    # 在线用户数（使用统一的计算方法）
    online_users = get_online_users_count()
    
    # 最近7天的数据
    seven_days_ago = local_now() - timedelta(days=7)
    new_users_week = User.query.filter(User.created_at >= seven_days_ago).count()
    new_products_week = Product.query.filter(Product.created_at >= seven_days_ago).count()
    new_orders_week = Order.query.filter(Order.created_at >= seven_days_ago).count()
    
    # 最近的活动
    recent_users = User.query.order_by(desc(User.created_at)).limit(5).all()
    recent_products = Product.query.order_by(desc(Product.created_at)).limit(5).all()
    recent_orders = Order.query.order_by(desc(Order.created_at)).limit(5).all()
    
    # 待处理事项
    pending_feedbacks = Feedback.query.filter_by(status='pending').count()

    # 最近的系统日志
    from app.models import SystemLog
    recent_logs = SystemLog.query.order_by(SystemLog.created_at.desc()).limit(8).all()

    return render_template('admin/dashboard.html',
                         title='管理员仪表板',
                         total_users=total_users,
                         total_products=total_products,
                         total_orders=total_orders,
                         pending_products=pending_products,
                         online_users=online_users,
                         new_users_week=new_users_week,
                         new_products_week=new_products_week,
                         new_orders_week=new_orders_week,
                         recent_users=recent_users,
                         recent_products=recent_products,
                         recent_orders=recent_orders,
                         pending_feedbacks=pending_feedbacks,
                         recent_logs=recent_logs)

@bp.route('/users')
@login_required
@admin_required
def users():
    """用户管理"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    role_filter = request.args.get('role', 'all')
    status_filter = request.args.get('status', 'all')
    
    query = User.query
    
    # 搜索
    if search:
        query = query.filter(
            User.username.contains(search) |
            User.email.contains(search) |
            User.nickname.contains(search)
        )
    
    # 角色筛选
    if role_filter != 'all':
        query = query.filter_by(role=UserRole(role_filter))
    
    # 状态筛选
    if status_filter == 'active':
        query = query.filter_by(is_active=True)
    elif status_filter == 'inactive':
        query = query.filter_by(is_active=False)
    
    users = query.order_by(desc(User.created_at)).paginate(
        page=page,
        per_page=20,
        error_out=False
    )
    
    return render_template('admin/users.html',
                         title='用户管理',
                         users=users,
                         search=search,
                         role_filter=role_filter,
                         status_filter=status_filter)

@bp.route('/users/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(id):
    """编辑用户"""
    user = User.query.get_or_404(id)
    form = UserManageForm()
    
    if form.validate_on_submit():
        try:
            # 检查用户名和邮箱是否被其他用户使用
            existing_user = User.query.filter(
                User.username == form.username.data,
                User.id != id
            ).first()
            if existing_user:
                flash('用户名已被其他用户使用', 'error')
                return render_template('admin/edit_user.html',
                                     title='编辑用户',
                                     form=form,
                                     user=user)

            existing_email = User.query.filter(
                User.email == form.email.data,
                User.id != id
            ).first()
            if existing_email:
                flash('邮箱已被其他用户使用', 'error')
                return render_template('admin/edit_user.html',
                                     title='编辑用户',
                                     form=form,
                                     user=user)

            # 更新用户信息
            user.username = form.username.data
            user.email = form.email.data
            user.nickname = form.nickname.data
            user.phone = form.phone.data
            user.bio = form.bio.data
            user.role = UserRole(form.role.data)

            # 明确处理布尔字段
            user.is_active = bool(form.is_active.data)
            user.email_verified = bool(form.email_verified.data)

            # 更新时间戳
            user.updated_at = local_now()

            db.session.commit()

            # 记录管理员操作
            try:
                from app.utils.logger import log_admin_action
                log_admin_action(f'编辑用户信息: {user.username}')
            except:
                pass

            flash('用户信息已更新', 'success')
            return redirect(url_for('admin_panel.users'))
        except Exception as e:
            db.session.rollback()
            flash(f'更新失败: {str(e)}', 'error')

    elif request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.nickname.data = user.nickname
        form.phone.data = user.phone
        form.bio.data = user.bio
        form.role.data = user.role.value
        form.is_active.data = user.is_active
        form.email_verified.data = user.email_verified
    
    return render_template('admin/edit_user.html',
                         title='编辑用户',
                         form=form,
                         user=user)

@bp.route('/products')
@login_required
@admin_required
def products():
    """商品管理"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status', 'all')
    category_filter = request.args.get('category', type=int)
    
    query = Product.query
    
    # 搜索
    if search:
        query = query.filter(
            Product.title.contains(search) |
            Product.description.contains(search)
        )
    
    # 状态筛选
    if status_filter != 'all':
        query = query.filter_by(status=ProductStatus(status_filter))
    
    # 分类筛选
    if category_filter:
        query = query.filter_by(category_id=category_filter)
    
    products = query.order_by(desc(Product.created_at)).paginate(
        page=page,
        per_page=20,
        error_out=False
    )
    
    categories = Category.query.all()
    
    return render_template('admin/products.html',
                         title='商品管理',
                         products=products,
                         categories=categories,
                         search=search,
                         status_filter=status_filter,
                         category_filter=category_filter)

@bp.route('/products/audit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def audit_product(id):
    """审核商品"""
    product = Product.query.get_or_404(id)
    form = ProductAuditForm()

    if form.validate_on_submit():
        try:
            old_status = product.status.value
            new_status = form.status.data
            product.status = ProductStatus(new_status)

            # 更新时间戳
            product.updated_at = local_now()

            # 创建通知
            from app.models import Notification
            status_messages = {
                'active': '审核通过，商品已上架',
                'inactive': '商品已下架',
                'pending': '商品重新进入待审核状态',
                'sold': '商品已标记为售出'
            }

            status_message = status_messages.get(new_status, f'状态已更新为{new_status}')
            notification_content = f'您的商品"{product.title}"状态已更新：{status_message}'

            # 如果有审核备注，添加到通知中
            if form.audit_note.data:
                notification_content += f'\n管理员备注：{form.audit_note.data}'

            notification = Notification(
                user_id=product.seller_id,
                title='商品状态更新',
                content=notification_content,
                type='audit',
                related_id=product.id
            )
            db.session.add(notification)

            db.session.commit()

            # 记录管理员操作
            try:
                from app.utils.logger import log_admin_action
                log_admin_action(f'更新商品状态: {product.title} ({old_status} -> {new_status})')
            except:
                pass

            flash(f'商品状态已更新为：{status_messages.get(new_status, new_status)}', 'success')
            return redirect(url_for('admin_panel.products'))

        except Exception as e:
            db.session.rollback()
            flash(f'更新失败: {str(e)}', 'error')

    elif request.method == 'GET':
        form.status.data = product.status.value
        # 如果有之前的审核备注，可以在这里加载

    return render_template('admin/audit_product.html',
                         title='商品状态管理',
                         form=form,
                         product=product)

@bp.route('/categories')
@login_required
@admin_required
def categories():
    """分类管理"""
    categories = Category.query.order_by(Category.name).all()
    return render_template('admin/categories.html',
                         title='分类管理',
                         categories=categories)

@bp.route('/categories/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_category():
    """添加分类"""
    form = CategoryManageForm()

    if form.validate_on_submit():
        # 使用新的验证工具
        from app.utils.validators import validate_category_name, sanitize_input

        # 验证分类名称
        is_valid, error_msg = validate_category_name(form.name.data)
        if not is_valid:
            flash(error_msg, 'error')
            return render_template('admin/add_category.html',
                                 title='添加分类',
                                 form=form)

        # 清理输入数据
        name = sanitize_input(form.name.data)
        description = sanitize_input(form.description.data) if form.description.data else None
        icon = sanitize_input(form.icon.data) if form.icon.data else None

        # 检查分类名称是否已存在
        existing_category = Category.query.filter_by(name=name).first()
        if existing_category:
            flash('分类名称已存在，请使用其他名称', 'error')
            return render_template('admin/add_category.html',
                                 title='添加分类',
                                 form=form)

        try:
            category = Category(
                name=name,
                description=description,
                icon=icon,
                is_active=form.is_active.data
            )

            db.session.add(category)
            db.session.commit()

            # 记录管理员操作
            from app.utils.logger import log_admin_action
            log_admin_action(f'添加分类: {name}')

            flash('分类添加成功', 'success')
            return redirect(url_for('admin_panel.categories'))
        except Exception as e:
            db.session.rollback()
            from app.utils.logger import log_error
            log_error(f'添加分类失败: {str(e)}', 'admin')
            flash(f'添加分类失败: {str(e)}', 'error')

    return render_template('admin/add_category.html',
                         title='添加分类',
                         form=form)

@bp.route('/categories/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_category(id):
    """编辑分类"""
    category = Category.query.get_or_404(id)
    form = CategoryManageForm()

    if form.validate_on_submit():
        # 使用新的验证工具
        from app.utils.validators import validate_category_name, sanitize_input

        # 验证分类名称
        is_valid, error_msg = validate_category_name(form.name.data)
        if not is_valid:
            flash(error_msg, 'error')
            return render_template('admin/edit_category.html',
                                 title='编辑分类',
                                 form=form,
                                 category=category)

        # 清理输入数据
        name = sanitize_input(form.name.data)
        description = sanitize_input(form.description.data) if form.description.data else None
        icon = sanitize_input(form.icon.data) if form.icon.data else None

        # 检查分类名称是否已被其他分类使用
        existing_category = Category.query.filter(
            Category.name == name,
            Category.id != id
        ).first()
        if existing_category:
            flash('分类名称已存在，请使用其他名称', 'error')
            return render_template('admin/edit_category.html',
                                 title='编辑分类',
                                 form=form,
                                 category=category)

        try:
            old_name = category.name
            category.name = name
            category.description = description
            category.icon = icon
            category.is_active = form.is_active.data

            db.session.commit()

            # 记录管理员操作
            from app.utils.logger import log_admin_action
            log_admin_action(f'编辑分类: {old_name} -> {name}')

            flash('分类信息已更新', 'success')
            return redirect(url_for('admin_panel.categories'))
        except Exception as e:
            db.session.rollback()
            from app.utils.logger import log_error
            log_error(f'编辑分类失败: {str(e)}', 'admin')
            flash(f'更新分类失败: {str(e)}', 'error')

    elif request.method == 'GET':
        form.name.data = category.name
        form.description.data = category.description
        form.icon.data = category.icon
        form.is_active.data = category.is_active

    return render_template('admin/edit_category.html',
                         title='编辑分类',
                         form=form,
                         category=category)

@bp.route('/orders')
@login_required
@admin_required
def orders():
    """订单管理"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    time_filter = request.args.get('time_range', 'all')
    search_query = request.args.get('search', '')

    query = Order.query

    # 搜索
    if search_query:
        query = query.filter(Order.order_no.contains(search_query))

    # 状态筛选
    if status_filter != 'all':
        query = query.filter_by(status=OrderStatus(status_filter))

    # 时间筛选
    if time_filter != 'all':
        now = local_now()
        if time_filter == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            query = query.filter(Order.created_at >= start_date)
        elif time_filter == 'week':
            start_date = now - timedelta(days=now.weekday())
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            query = query.filter(Order.created_at >= start_date)
        elif time_filter == 'month':
            start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            query = query.filter(Order.created_at >= start_date)

    orders = query.order_by(desc(Order.created_at)).paginate(
        page=page,
        per_page=20,
        error_out=False
    )

    return render_template('admin/orders.html',
                         title='订单管理',
                         orders=orders,
                         status_filter=status_filter,
                         time_filter=time_filter,
                         search_query=search_query)

@bp.route('/orders/detail/<int:id>')
@login_required
@admin_required
def order_detail(id):
    """订单详情"""
    order = Order.query.get_or_404(id)

    return render_template('orders/detail.html',
                         title=f'订单详情 - {order.order_no}',
                         order=order)

@bp.route('/orders/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_order(id):
    """编辑订单"""
    order = Order.query.get_or_404(id)

    if request.method == 'POST':
        status = request.form.get('status')
        if status and status in [s.value for s in OrderStatus]:
            order.status = OrderStatus(status)
            order.updated_at = local_now()

            try:
                db.session.commit()
                flash('订单状态更新成功！', 'success')
                return redirect(url_for('admin_panel.orders'))
            except Exception as e:
                db.session.rollback()
                flash(f'更新失败：{str(e)}', 'error')

    return render_template('orders/detail.html',
                         title=f'编辑订单 - {order.order_no}',
                         order=order,
                         edit_mode=True)

@bp.route('/feedbacks')
@login_required
@admin_required
def feedbacks():
    """用户反馈管理"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    type_filter = request.args.get('type', 'all')

    query = Feedback.query

    # 状态筛选
    if status_filter != 'all':
        query = query.filter_by(status=status_filter)

    # 类型筛选
    if type_filter != 'all':
        query = query.filter_by(type=type_filter)

    feedbacks = query.order_by(desc(Feedback.created_at)).paginate(
        page=page,
        per_page=20,
        error_out=False
    )

    return render_template('admin/feedbacks.html',
                         title='用户反馈',
                         feedbacks=feedbacks,
                         status_filter=status_filter,
                         type_filter=type_filter)

@bp.route('/feedbacks/<int:id>')
@login_required
@admin_required
def feedback_detail(id):
    """查看反馈详情"""
    feedback = Feedback.query.get_or_404(id)
    return render_template('admin/feedback_detail.html',
                         title='反馈详情',
                         feedback=feedback)

@bp.route('/feedbacks/reply/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def reply_feedback(id):
    """回复用户反馈"""
    feedback = Feedback.query.get_or_404(id)
    form = FeedbackReplyForm()

    if form.validate_on_submit():
        feedback.status = form.status.data
        feedback.admin_reply = form.admin_reply.data
        feedback.admin_id = current_user.id

        # 创建通知（仅对注册用户）
        if feedback.user_id:
            from app.models import Notification
            notification = Notification(
                user_id=feedback.user_id,
                title='反馈回复通知',
                content=f'您的反馈"{feedback.title}"已收到管理员回复',
                type='feedback',
                related_id=feedback.id
            )
            db.session.add(notification)

        db.session.commit()
        flash('反馈回复成功', 'success')
        return redirect(url_for('admin_panel.feedbacks'))

    elif request.method == 'GET':
        form.status.data = feedback.status
        form.admin_reply.data = feedback.admin_reply

    return render_template('admin/reply_feedback.html',
                         title='回复反馈',
                         form=form,
                         feedback=feedback)

@bp.route('/feedbacks/<int:id>/update_status', methods=['GET', 'POST'])
@login_required
@admin_required
def update_feedback_status(id):
    """管理员更新反馈状态"""
    from app.admin_panel.forms import UserFeedbackStatusForm

    feedback = Feedback.query.get_or_404(id)
    form = UserFeedbackStatusForm()

    if form.validate_on_submit():
        old_status = feedback.status
        new_status = form.status.data

        feedback.status = new_status
        if form.user_note.data:
            # 将管理员备注添加到反馈内容中
            feedback.content += f"\n\n[管理员备注 - {local_now().strftime('%Y-%m-%d %H:%M')}]\n{form.user_note.data}"

        feedback.updated_at = local_now()

        # 如果状态发生变化，创建通知给用户
        if old_status != new_status and feedback.user_id:
            notification = Notification(
                user_id=feedback.user_id,
                title='反馈状态更新',
                content=f'您的反馈"{feedback.title}"状态已更新为：{dict(form.status.choices)[new_status]}',
                type='feedback',
                related_id=feedback.id
            )
            db.session.add(notification)

        db.session.commit()
        flash('反馈状态已更新', 'success')
        return redirect(url_for('admin_panel.feedback_detail', id=feedback.id))

    elif request.method == 'GET':
        form.status.data = feedback.status

    return render_template('admin/update_feedback_status.html',
                         title='更新反馈状态',
                         form=form,
                         feedback=feedback)

@bp.route('/system_config', methods=['GET', 'POST'])
@login_required
@admin_required
def system_config():
    """系统配置"""
    import sys
    import flask
    import platform
    from datetime import datetime

    if request.method == 'POST':
        try:
            # 记录请求信息用于调试
            print(f"POST request received - Content-Type: {request.content_type}")
            print(f"Form data: {dict(request.form)}")
            print(f"Is JSON: {request.is_json}")
            print(f"Headers: {dict(request.headers)}")

            # 处理AJAX请求保存设置
            if request.is_json:
                data = request.get_json()
                print(f"JSON data: {data}")
                return jsonify({'success': True, 'message': '设置保存成功'})

            # 处理表单提交 - 支持更多配置项
            if request.form:
                # 定义允许的配置项及其验证规则
                allowed_configs = {
                    'site_name': {'type': str, 'max_length': 100},
                    'site_description': {'type': str, 'max_length': 500},
                    'contact_email': {'type': str, 'max_length': 100},
                    'contact_phone': {'type': str, 'max_length': 20},
                    'announcement': {'type': str, 'max_length': 1000},
                    'products_per_page': {'type': int, 'min': 6, 'max': 50},
                    'orders_per_page': {'type': int, 'min': 5, 'max': 50},
                    'max_file_size': {'type': int, 'min': 1, 'max': 100},
                    'allowed_extensions': {'type': str, 'max_length': 200},
                    'mail_server': {'type': str, 'max_length': 100},
                    'mail_port': {'type': int, 'min': 1, 'max': 65535},
                    'mail_username': {'type': str, 'max_length': 100},
                    'mail_password': {'type': str, 'max_length': 100},
                    'mail_use_tls': {'type': str, 'max_length': 10}
                }

                updated_configs = []
                # 验证并更新配置
                for key, value in request.form.items():
                    # 跳过CSRF token
                    if key == 'csrf_token':
                        continue

                    if key in allowed_configs:
                        config_rule = allowed_configs[key]
                        original_value = value

                        # 类型验证
                        if config_rule['type'] == int:
                            try:
                                value = int(value)
                                if 'min' in config_rule and value < config_rule['min']:
                                    print(f"Skipping {key}: value {value} below minimum {config_rule['min']}")
                                    continue
                                if 'max' in config_rule and value > config_rule['max']:
                                    print(f"Skipping {key}: value {value} above maximum {config_rule['max']}")
                                    continue
                            except ValueError:
                                print(f"Skipping {key}: invalid integer value '{original_value}'")
                                continue
                        elif config_rule['type'] == str:
                            if 'max_length' in config_rule and len(value) > config_rule['max_length']:
                                print(f"Skipping {key}: value length {len(value)} exceeds maximum {config_rule['max_length']}")
                                continue

                        # 更新或创建配置项
                        config = SystemConfig.query.filter_by(key=key).first()
                        if config:
                            old_value = config.value
                            config.value = str(value)
                            print(f"Updated config {key}: '{old_value}' -> '{value}'")
                        else:
                            config = SystemConfig(key=key, value=str(value))
                            db.session.add(config)
                            print(f"Created new config {key}: '{value}'")

                        updated_configs.append(key)
                    else:
                        print(f"Ignoring unknown config key: {key}")

                # 提交数据库更改
                db.session.commit()

                # 如果更新了邮件配置，同时更新.env文件
                mail_configs = ['mail_server', 'mail_port', 'mail_username', 'mail_password', 'mail_use_tls']
                updated_mail_configs = [key for key in updated_configs if key in mail_configs]

                if updated_mail_configs:
                    try:
                        update_env_file(updated_mail_configs, request.form)
                    except Exception as e:
                        print(f"Failed to update .env file: {e}")

                message = f'系统配置已更新 ({len(updated_configs)} 项配置)'
                print(f"Success: {message}")
                print(f"Updated configs: {updated_configs}")

                flash(message, 'success')

                # 记录管理员操作
                try:
                    from app.utils.logger import log_admin_action
                    log_admin_action(f'更新系统配置: {", ".join(updated_configs)}')
                except Exception as log_error:
                    print(f"Failed to log admin action: {log_error}")

                return jsonify({'success': True, 'message': message})

            # 处理WTF表单提交
            form = SystemConfigForm()
            if form.validate_on_submit():
                # 更新或创建配置项
                configs = {
                    'site_name': form.site_name.data,
                    'site_description': form.site_description.data,
                    'contact_email': form.contact_email.data,
                    'contact_phone': form.contact_phone.data,
                    'announcement': form.announcement.data
                }

                for key, value in configs.items():
                    config = SystemConfig.query.filter_by(key=key).first()
                    if config:
                        config.value = value
                    else:
                        config = SystemConfig(key=key, value=value)
                        db.session.add(config)

                db.session.commit()
                flash('系统配置已更新', 'success')
                return redirect(url_for('admin_panel.system_config'))
        except Exception as e:
            db.session.rollback()
            flash(f'配置更新失败: {str(e)}', 'error')
            return jsonify({'success': False, 'message': f'配置更新失败: {str(e)}'})

    # GET请求 - 显示配置页面
    configs = {config.key: config.value for config in SystemConfig.query.all()}

    # 系统信息
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    flask_version = flask.__version__
    db_info = "SQLite"
    uptime = "运行中"
    current_time = local_now().strftime('%Y-%m-%d %H:%M:%S')

    # 统计信息（使用统一的在线用户计算逻辑）
    online_users = get_online_users_count()
    total_users = User.query.filter(User.is_active == True).count()
    total_products = Product.query.count()
    total_orders = Order.query.count()

    # 获取今日新增数据
    today_start = local_now().replace(hour=0, minute=0, second=0, microsecond=0)
    new_users_today = User.query.filter(
        User.created_at >= today_start,
        User.is_active == True
    ).count()
    new_products_today = Product.query.filter(Product.created_at >= today_start).count()
    new_orders_today = Order.query.filter(Order.created_at >= today_start).count()

    # 添加邮件配置信息到configs中，以便模板正确显示邮件配置状态
    configs['MAIL_USERNAME'] = current_app.config.get('MAIL_USERNAME')
    configs['MAIL_PASSWORD'] = current_app.config.get('MAIL_PASSWORD')
    configs['MAIL_SERVER'] = current_app.config.get('MAIL_SERVER')
    configs['MAIL_PORT'] = current_app.config.get('MAIL_PORT')
    configs['MAIL_USE_TLS'] = current_app.config.get('MAIL_USE_TLS')

    return render_template('admin/system_config.html',
                         title='系统配置',
                         config=configs,
                         python_version=python_version,
                         flask_version=flask_version,
                         db_info=db_info,
                         uptime=uptime,
                         current_time=current_time,
                         online_users=online_users,
                         total_users=total_users,
                         total_products=total_products,
                         total_orders=total_orders,
                         new_users_today=new_users_today,
                         new_products_today=new_products_today,
                         new_orders_today=new_orders_today)

@bp.route('/system_monitor')
@login_required
@admin_required
def system_monitor():
    """系统监控"""
    return render_template('admin/system_monitor.html', title='系统监控')

@bp.route('/debug_status')
@login_required
@admin_required
def debug_status():
    """调试商品状态切换"""
    return render_template('admin/debug_status.html', title='状态切换调试')

@bp.route('/system_diagnostics')
@login_required
@admin_required
def system_diagnostics():
    """系统诊断页面"""
    return render_template('admin/system_diagnostics.html', title='系统诊断')

@bp.route('/statistics')
@login_required
@admin_required
def statistics():
    """数据统计"""
    # 用户统计
    total_users = User.query.count()
    # 活跃用户定义为最近一天在线的用户
    one_day_ago = local_now() - timedelta(days=1)
    active_users = User.query.filter(
        User.last_seen >= one_day_ago,
        User.is_active == True
    ).count()
    inactive_users = total_users - active_users
    admin_users = User.query.filter_by(role=UserRole.ADMIN).count()

    # 商品统计
    total_products = Product.query.count()
    active_products = Product.query.filter_by(status=ProductStatus.ACTIVE).count()
    # 已售出商品：已售数量等于总数量的商品
    sold_out_products = Product.query.filter(Product.sold_quantity >= Product.quantity).count()
    # 已下架商品
    inactive_products = Product.query.filter_by(status=ProductStatus.INACTIVE).count()
    pending_products = Product.query.filter_by(status=ProductStatus.PENDING).count()

    # 订单统计
    total_orders = Order.query.count()
    completed_orders = Order.query.filter_by(status=OrderStatus.COMPLETED).count()
    pending_orders = Order.query.filter_by(status=OrderStatus.PENDING).count()
    cancelled_orders = Order.query.filter_by(status=OrderStatus.CANCELLED).count()

    # 交易金额统计
    total_amount = db.session.query(func.sum(Order.total_amount)).filter_by(status=OrderStatus.COMPLETED).scalar() or 0

    # 分类统计
    category_stats = db.session.query(
        Category.name,
        func.count(Product.id).label('product_count')
    ).join(Product).group_by(Category.id, Category.name).all()

    # 计算分类占比
    category_stats_with_percentage = []
    for category in category_stats:
        percentage = (category.product_count / total_products * 100) if total_products > 0 else 0
        category_stats_with_percentage.append({
            'name': category.name,
            'product_count': category.product_count,
            'percentage': percentage
        })

    # 活跃用户排行 - 获取用户的商品数和交易次数
    active_users_ranking = db.session.query(
        User,
        func.count(Product.id).label('product_count'),
        func.count(Order.id).label('order_count')
    ).outerjoin(Product, User.id == Product.seller_id)\
     .outerjoin(Order, User.id == Order.seller_id)\
     .group_by(User.id)\
     .order_by(func.count(Product.id).desc())\
     .limit(10).all()

    # 为每个用户对象添加统计属性
    active_users_list = []
    for user, product_count, order_count in active_users_ranking:
        user.product_count = product_count
        user.order_count = order_count
        active_users_list.append(user)

    # 最近30天的数据趋势
    thirty_days_ago = local_now() - timedelta(days=30)

    # 每日新用户数
    daily_users = db.session.query(
        func.date(User.created_at).label('date'),
        func.count(User.id).label('count')
    ).filter(User.created_at >= thirty_days_ago).group_by(func.date(User.created_at)).all()

    # 每日新商品数
    daily_products = db.session.query(
        func.date(Product.created_at).label('date'),
        func.count(Product.id).label('count')
    ).filter(Product.created_at >= thirty_days_ago).group_by(func.date(Product.created_at)).all()

    # 每日新订单数
    daily_orders = db.session.query(
        func.date(Order.created_at).label('date'),
        func.count(Order.id).label('count')
    ).filter(Order.created_at >= thirty_days_ago).group_by(func.date(Order.created_at)).all()

    # 准备图表数据
    stats = {
        'active_users': active_users,
        'inactive_users': inactive_users,
        'active_products': active_products,
        'sold_products': sold_out_products,
        'inactive_products': inactive_products
    }

    # 订单趋势数据
    order_trend_labels = [str(order.date) for order in daily_orders]
    order_trend_data = [order.count for order in daily_orders]

    return render_template('admin/statistics.html',
                         title='数据统计',
                         stats=stats,
                         category_stats=category_stats_with_percentage,
                         active_users=active_users_list,
                         order_trend_labels=order_trend_labels,
                         order_trend_data=order_trend_data)

@bp.route('/api/system_status')
@login_required
@admin_required
def api_system_status():
    """获取系统状态API - 使用统一的数据源和性能优化"""
    try:
        from app.utils.performance_monitor import optimize_system_status_cache, optimize_online_users_cache

        # 使用缓存优化的系统状态获取
        get_cached_status = optimize_system_status_cache()
        get_cached_users = optimize_online_users_cache()

        # 获取缓存的系统状态
        status = get_cached_status()

        # 添加缓存的在线用户数
        status['online_users'] = get_cached_users()

        return jsonify(status)
    except Exception as e:
        print(f"System status API error: {str(e)}")
        import traceback
        traceback.print_exc()

        # 返回默认错误状态
        from app.utils.system_monitor import get_simplified_system_status
        try:
            error_status = get_simplified_system_status()
            error_status['error'] = str(e)
            error_status['online_users'] = 0
            return jsonify(error_status)
        except:
            # 如果连默认状态都获取失败，返回最基本的错误信息
            return jsonify({
                'error': str(e),
                'server_status': '错误',
                'db_status': '未知',
                'cpu_usage': '0.0%',
                'memory_usage': '0.0%',
                'disk_usage': '0.0%',
                'online_users': 0,
                'timestamp': local_now().strftime('%Y-%m-%d %H:%M:%S')
            })

@bp.route('/api/online_users')
@login_required
@admin_required
def api_online_users():
    """在线用户数API"""
    try:
        # 使用统一的在线用户数计算方法
        online_users = get_online_users_count()
        total_users = User.query.filter(User.is_active == True).count()

        # 获取今日新增用户数
        today_start = local_now().replace(hour=0, minute=0, second=0, microsecond=0)
        new_users_today = User.query.filter(
            User.created_at >= today_start,
            User.is_active == True
        ).count()

        return jsonify({
            'online_users': online_users,
            'total_users': total_users,
            'new_users_today': new_users_today,
            'timestamp': local_now().strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        return jsonify({
            'online_users': 0,
            'total_users': 0,
            'new_users_today': 0,
            'timestamp': local_now().strftime('%Y-%m-%d %H:%M:%S'),
            'error': str(e)
        })

@bp.route('/api/system_details')
@login_required
@admin_required
def api_system_details():
    """获取详细系统信息"""
    try:
        from app.utils.system_monitor import get_complete_system_status
        return jsonify(get_complete_system_status())
    except Exception as e:
        return jsonify({'error': str(e)})

@bp.route('/api/data_consistency_check')
@login_required
@admin_required
def api_data_consistency_check():
    """数据一致性检查API"""
    try:
        from app.utils.data_consistency import run_consistency_check
        result = run_consistency_check()
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': local_now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_checks': 0,
                'passed': 0,
                'failed': 1,
                'warnings': 0
            }
        })

@bp.route('/api/performance_report')
@login_required
@admin_required
def api_performance_report():
    """性能监控报告API"""
    try:
        from app.utils.performance_monitor import get_performance_report
        return jsonify(get_performance_report())
    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': local_now().strftime('%Y-%m-%d %H:%M:%S'),
            'performance_metrics': {},
            'performance_summary': {
                'total_calls': 0,
                'total_errors': 1,
                'overall_error_rate': 1.0
            },
            'cache_stats': {
                'total_items': 0,
                'active_items': 0,
                'expired_items': 0
            }
        })

@bp.route('/users/toggle_status/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """切换用户状态"""
    try:
        user = User.query.get_or_404(user_id)

        if user.id == current_user.id:
            return jsonify({'success': False, 'message': '不能禁用自己的账户'})

        # 记录原状态
        old_status = user.is_active
        user.is_active = not user.is_active

        # 更新时间戳
        user.updated_at = local_now()

        db.session.commit()

        # 记录管理员操作
        try:
            from app.utils.logger import log_admin_action
            action = '启用' if user.is_active else '禁用'
            log_admin_action(f'{action}用户: {user.username}')
        except:
            pass  # 如果日志记录失败，不影响主要功能

        status = '启用' if user.is_active else '禁用'
        return jsonify({'success': True, 'message': f'用户已{status}'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

# 旧的在线用户API已被实时版本替代

@bp.route('/api/clear_cache', methods=['POST'])
@login_required
@admin_required
def api_clear_cache():
    """清理缓存"""
    try:
        import os
        import tempfile
        import shutil

        cleared_items = []

        # 清理临时文件
        temp_dir = tempfile.gettempdir()
        temp_files_count = 0
        try:
            for filename in os.listdir(temp_dir):
                if filename.startswith('tmp') or filename.startswith('temp'):
                    temp_files_count += 1
            cleared_items.append(f'临时文件: {temp_files_count} 个')
        except Exception as temp_error:
            print(f"清理临时文件时出错: {temp_error}")

        # 清理Python缓存
        import sys
        if hasattr(sys, 'modules'):
            cleared_items.append(f'Python模块缓存: {len(sys.modules)} 个')

        # 记录管理员操作
        try:
            from app.utils.logger import log_admin_action
            log_admin_action('清理系统缓存')
        except Exception as log_error:
            print(f"记录日志时出错: {log_error}")

        message = '缓存清理成功'
        if cleared_items:
            message += f' ({", ".join(cleared_items)})'

        return jsonify({'success': True, 'message': message})
    except Exception as e:
        print(f"清理缓存失败: {str(e)}")
        try:
            from app.utils.logger import log_error
            log_error(f'缓存清理失败: {str(e)}', 'admin')
        except:
            pass
        return jsonify({'success': False, 'message': f'缓存清理失败: {str(e)}'})

@bp.route('/api/backup_database', methods=['POST'])
@login_required
@admin_required
def api_backup_database():
    """备份数据库"""
    try:
        import shutil
        import os
        from datetime import datetime

        # 创建备份目录
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # 生成备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'app_backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, backup_filename)

        # 检查多个可能的数据库文件位置
        db_paths = ['app.db', 'app/app.db']
        source_db = None

        for db_path in db_paths:
            if os.path.exists(db_path) and os.path.getsize(db_path) > 0:
                source_db = db_path
                break

        if source_db:
            # 复制数据库文件
            shutil.copy2(source_db, backup_path)

            # 获取文件大小信息
            file_size = os.path.getsize(backup_path)
            size_mb = file_size / (1024 * 1024)

            # 记录管理员操作
            try:
                from app.utils.logger import log_admin_action
                log_admin_action(f'备份数据库: {backup_filename} (源文件: {source_db}, 大小: {size_mb:.2f}MB)')
            except Exception as log_error:
                print(f"记录日志时出错: {log_error}")

            return jsonify({
                'success': True,
                'message': f'数据库备份成功: {backup_filename} (大小: {size_mb:.2f}MB)'
            })
        else:
            # 列出检查的路径用于调试
            checked_paths = []
            for db_path in db_paths:
                exists = os.path.exists(db_path)
                size = os.path.getsize(db_path) if exists else 0
                checked_paths.append(f"{db_path} (存在: {exists}, 大小: {size})")

            error_msg = f'数据库文件不存在。检查的路径: {"; ".join(checked_paths)}'
            print(error_msg)
            return jsonify({'success': False, 'message': '数据库文件不存在或为空'})

    except Exception as e:
        print(f"数据库备份失败: {str(e)}")
        import traceback
        traceback.print_exc()

        try:
            from app.utils.logger import log_error
            log_error(f'数据库备份失败: {str(e)}', 'admin')
        except:
            pass

        return jsonify({'success': False, 'message': f'数据库备份失败: {str(e)}'})

@bp.route('/api/test_email', methods=['POST'])
@login_required
@admin_required
def api_test_email():
    """测试邮件发送"""
    try:
        data = request.get_json()
        email = data.get('email')

        if not email:
            return jsonify({'success': False, 'message': '请提供邮箱地址'})

        # 验证邮箱格式
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return jsonify({'success': False, 'message': '邮箱地址格式不正确'})

        # 检查邮件配置
        if not current_app.config.get('MAIL_USERNAME') or not current_app.config.get('MAIL_PASSWORD'):
            return jsonify({'success': False, 'message': '邮件服务未配置，请先在系统设置中配置邮件参数'})

        # 实际发送邮件
        try:
            from flask_mail import Message
            from app import mail
            from datetime import datetime

            msg = Message(
                subject='[二手交易平台] 邮件系统测试',
                sender=current_app.config['MAIL_DEFAULT_SENDER'],
                recipients=[email]
            )

            # 邮件正文
            msg.body = f"""
这是一封测试邮件，用于验证二手交易平台的邮件系统是否正常工作。

如果您收到这封邮件，说明邮件配置成功！

测试信息：
- 发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- SMTP服务器: {current_app.config.get('MAIL_SERVER', 'N/A')}
- 端口: {current_app.config.get('MAIL_PORT', 'N/A')}
- 加密方式: {'TLS' if current_app.config.get('MAIL_USE_TLS') else 'None'}

此邮件由系统自动发送，请勿回复。
二手交易平台管理团队
            """

            # HTML邮件内容
            msg.html = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #007bff;">📧 邮件系统测试</h2>

                    <p>这是一封测试邮件，用于验证<strong>二手交易平台</strong>的邮件系统是否正常工作。</p>

                    <div style="background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 20px 0;">
                        <strong>✅ 测试成功！</strong><br>
                        如果您收到这封邮件，说明邮件配置成功！
                    </div>

                    <p><strong>📊 测试信息：</strong></p>
                    <ul>
                        <li>发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                        <li>SMTP服务器: {current_app.config.get('MAIL_SERVER', 'N/A')}</li>
                        <li>端口: {current_app.config.get('MAIL_PORT', 'N/A')}</li>
                        <li>加密方式: {'TLS' if current_app.config.get('MAIL_USE_TLS') else 'None'}</li>
                        <li>测试人员: {current_user.username}</li>
                    </ul>

                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">

                    <p style="color: #666; font-size: 14px;">
                        此邮件由系统自动发送，请勿回复。<br>
                        二手交易平台管理团队
                    </p>
                </div>
            </body>
            </html>
            """

            # 发送邮件
            mail.send(msg)

            # 记录管理员操作
            try:
                from app.utils.logger import log_admin_action
                log_admin_action(f'测试邮件发送到: {email} (发送成功)')
            except Exception as log_error:
                print(f"记录日志时出错: {log_error}")

            return jsonify({'success': True, 'message': f'测试邮件已成功发送到 {email}，请检查收件箱（包括垃圾邮件文件夹）'})

        except Exception as mail_error:
            print(f"邮件发送失败: {str(mail_error)}")

            # 记录失败日志
            try:
                from app.utils.logger import log_admin_action
                log_admin_action(f'测试邮件发送到: {email} (发送失败: {str(mail_error)})')
            except:
                pass

            # 根据错误类型提供具体的错误信息
            error_message = str(mail_error)
            if 'Authentication failed' in error_message or '535' in error_message:
                return jsonify({'success': False, 'message': '邮件认证失败，请检查邮箱用户名和授权码是否正确'})
            elif 'Connection refused' in error_message or 'timeout' in error_message:
                return jsonify({'success': False, 'message': '无法连接到邮件服务器，请检查网络连接和SMTP设置'})
            elif 'Invalid recipient' in error_message or '550' in error_message:
                return jsonify({'success': False, 'message': '收件人邮箱地址无效或被拒绝'})
            else:
                return jsonify({'success': False, 'message': f'邮件发送失败: {error_message}'})

    except Exception as e:
        print(f"测试邮件API错误: {str(e)}")
        try:
            from app.utils.logger import log_error
            log_error(f'测试邮件发送失败: {str(e)}', 'admin')
        except:
            pass
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

@bp.route('/products/toggle_status/<int:product_id>', methods=['POST'])
@login_required
@admin_required
def toggle_product_status(product_id):
    """切换商品状态"""
    try:
        product = Product.query.get_or_404(product_id)

        # 支持JSON和表单数据
        if request.is_json:
            data = request.get_json()
            new_status = data.get('status') if data else None
        else:
            new_status = request.form.get('status')

        # 调试信息
        print(f"Toggle status request - Product ID: {product_id}, New Status: {new_status}")
        print(f"Request data: {request.form.to_dict()}")
        print(f"Request JSON: {request.get_json()}")

        # 验证状态值
        valid_statuses = ['active', 'inactive', 'pending', 'sold']
        if not new_status:
            return jsonify({'success': False, 'message': '缺少状态参数'})

        if new_status not in valid_statuses:
            return jsonify({'success': False, 'message': f'无效的状态: {new_status}，有效状态: {valid_statuses}'})

        old_status = product.status.value
        product.status = ProductStatus(new_status)
        db.session.commit()

        # 记录管理员操作
        try:
            from app.utils.logger import log_admin_action
            log_admin_action(f'修改商品状态: {product.title} ({old_status} -> {new_status})')
        except:
            pass  # 如果日志记录失败，不影响主要功能

        status_names = {
            'active': '在售',
            'inactive': '已下架',
            'pending': '待审核',
            'sold': '已售出'
        }

        return jsonify({
            'success': True,
            'message': f'商品状态已更新为: {status_names.get(new_status, new_status)}',
            'new_status': new_status,
            'old_status': old_status
        })
    except Exception as e:
        db.session.rollback()
        print(f"Toggle status error: {str(e)}")
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

@bp.route('/products/delete/<int:product_id>', methods=['POST'])
@login_required
@admin_required
def delete_product(product_id):
    """删除商品"""
    try:
        product = Product.query.get_or_404(product_id)
        product_title = product.title

        # 检查是否有相关订单
        from app.models import Order
        orders = Order.query.filter_by(product_id=product_id).count()
        if orders > 0:
            return jsonify({'success': False, 'message': '该商品有相关订单，无法删除'})

        # 删除相关的收藏记录
        from app.models import Favorite, ProductImage
        Favorite.query.filter_by(product_id=product_id).delete()

        # 删除相关的商品图片记录
        ProductImage.query.filter_by(product_id=product_id).delete()

        # 删除商品
        db.session.delete(product)
        db.session.commit()

        # 记录管理员操作
        try:
            from app.utils.logger import log_admin_action
            log_admin_action(f'删除商品: {product_title}')
        except:
            pass

        return jsonify({'success': True, 'message': '商品已删除'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@bp.route('/products/toggle_featured/<int:product_id>', methods=['POST'])
@login_required
@admin_required
def toggle_featured(product_id):
    """切换商品重点观察状态"""
    try:
        product = Product.query.get_or_404(product_id)

        # 切换重点观察状态
        product.is_featured = not product.is_featured
        db.session.commit()

        # 记录管理员操作
        try:
            from app.utils.logger import log_admin_action
            action = '设为重点观察' if product.is_featured else '取消重点观察'
            log_admin_action(f'{action}: {product.title}')
        except:
            pass

        message = '已设为重点观察' if product.is_featured else '已取消重点观察'
        return jsonify({'success': True, 'message': message, 'is_featured': product.is_featured})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

@bp.route('/logs')
@login_required
@admin_required
def logs():
    """系统日志"""
    from app.models import SystemLog

    page = request.args.get('page', 1, type=int)
    level_filter = request.args.get('level', '')
    module_filter = request.args.get('module', '')
    search_query = request.args.get('search', '')
    date_filter = request.args.get('date', '')

    query = SystemLog.query

    # 级别过滤
    if level_filter:
        query = query.filter(SystemLog.level == level_filter)

    # 模块过滤
    if module_filter:
        query = query.filter(SystemLog.module == module_filter)

    # 搜索过滤
    if search_query:
        query = query.filter(SystemLog.message.contains(search_query))

    # 日期过滤
    if date_filter:
        from datetime import datetime
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter(func.date(SystemLog.created_at) == filter_date)
        except ValueError:
            pass

    logs = query.order_by(SystemLog.created_at.desc()).paginate(
        page=page, per_page=50, error_out=False
    )

    return render_template('admin/logs.html',
                         title='系统日志',
                         logs=logs,
                         level_filter=level_filter,
                         module_filter=module_filter,
                         search_query=search_query,
                         date_filter=date_filter)

@bp.route('/api/delete_category/<int:category_id>', methods=['POST'])
@login_required
@admin_required
def api_delete_category(category_id):
    """删除分类"""
    try:
        category = Category.query.get_or_404(category_id)
        category_name = category.name

        # 检查是否有商品使用此分类
        if category.products.count() > 0:
            return jsonify({'success': False, 'message': '该分类下还有商品，无法删除'})

        # 删除分类
        db.session.delete(category)
        db.session.commit()

        # 记录管理员操作
        from app.utils.logger import log_admin_action
        log_admin_action(f'删除分类: {category_name}')

        return jsonify({'success': True, 'message': '分类已删除'})
    except Exception as e:
        db.session.rollback()
        from app.utils.logger import log_error
        log_error(f'删除分类失败: {str(e)}', 'admin')
        print(f"Delete category error: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@bp.route('/api/clear_logs', methods=['POST'])
@login_required
@admin_required
def api_clear_logs():
    """清空日志"""
    from app.models import SystemLog
    from app.utils.logger import log_admin_action

    try:
        # 记录清空前的日志数量
        log_count = SystemLog.query.count()

        # 清空日志
        SystemLog.query.delete()
        db.session.commit()

        # 记录管理员操作
        log_admin_action(f'清空了 {log_count} 条系统日志')

        return jsonify({
            'success': True,
            'message': f'成功清空 {log_count} 条日志'
        })
    except Exception as e:
        db.session.rollback()
        # 记录错误
        try:
            from app.utils.logger import log_error
            log_error(f'清空日志失败: {str(e)}', 'admin')
        except:
            pass
        return jsonify({
            'success': False,
            'message': f'清空失败: {str(e)}'
        })

# WebSocket相关功能已移除，改为传统HTTP模式
