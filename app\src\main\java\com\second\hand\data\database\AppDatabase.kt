package com.second.hand.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.second.hand.data.model.*

/**
 * Room数据库定义
 * 本地数据缓存和离线支持
 */
@Database(
    entities = [
        User::class,
        Category::class,
        Product::class,
        ProductImage::class,
        Order::class,
        Message::class,
        Review::class,
        Notification::class,
        Favorite::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "second_hand_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
