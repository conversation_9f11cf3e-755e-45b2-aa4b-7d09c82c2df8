#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证表单
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, PasswordField, BooleanField, SubmitField, TextAreaField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Regexp
from app.models import User

class LoginForm(FlaskForm):
    """登录表单"""
    username = StringField('用户名/邮箱', validators=[DataRequired(message='请输入用户名或邮箱')])
    password = PasswordField('密码', validators=[DataRequired(message='请输入密码')])
    remember_me = BooleanField('记住我')
    submit = SubmitField('登录')

class RegistrationForm(FlaskForm):
    """注册表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='请输入用户名'),
        Length(min=3, max=20, message='用户名长度应在3-20个字符之间'),
        Regexp('^[a-zA-Z0-9_]+$', message='用户名只能包含字母、数字和下划线')
    ])
    email = StringField('邮箱', validators=[
        DataRequired(message='请输入邮箱'),
        Email(message='请输入有效的邮箱地址')
    ])
    phone = StringField('手机号', validators=[
        Length(min=11, max=11, message='请输入11位手机号'),
        Regexp('^1[3-9]\d{9}$', message='请输入有效的手机号')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='请输入密码'),
        Length(min=6, max=20, message='密码长度应在6-20个字符之间')
    ])
    password2 = PasswordField('确认密码', validators=[
        DataRequired(message='请确认密码'),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    email_code = StringField('邮箱验证码', validators=[
        DataRequired(message='请输入邮箱验证码'),
        Length(min=6, max=6, message='邮箱验证码为6位数字'),
        Regexp('^[0-9]{6}$', message='邮箱验证码必须为6位数字')
    ])
    captcha = StringField('验证码', validators=[
        DataRequired(message='请输入验证码'),
        Length(min=4, max=4, message='验证码为4位')
    ])
    submit = SubmitField('注册')
    
    def validate_username(self, username):
        """验证用户名唯一性"""
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('该用户名已被使用，请选择其他用户名')
    
    def validate_email(self, email):
        """验证邮箱唯一性"""
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('该邮箱已被注册，请使用其他邮箱')
    
    def validate_phone(self, phone):
        """验证手机号唯一性"""
        if phone.data:
            user = User.query.filter_by(phone=phone.data).first()
            if user:
                raise ValidationError('该手机号已被注册，请使用其他手机号')

    def validate_email_code(self, email_code):
        """验证邮箱验证码"""
        from app.models import EmailVerificationCode

        if not self.email.data:
            raise ValidationError('请先输入邮箱地址')

        # 获取最新的有效验证码
        verification_code = EmailVerificationCode.get_latest_valid_code(self.email.data)

        if not verification_code:
            raise ValidationError('验证码不存在或已过期，请重新获取')

        if verification_code.code != email_code.data:
            raise ValidationError('验证码错误，请重新输入')

class EditProfileForm(FlaskForm):
    """编辑个人资料表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='请输入用户名'),
        Length(min=3, max=20, message='用户名长度应在3-20个字符之间'),
        Regexp('^[a-zA-Z0-9_]+$', message='用户名只能包含字母、数字和下划线')
    ])
    
    email = StringField('邮箱', validators=[
        DataRequired(message='请输入邮箱'),
        Email(message='请输入有效的邮箱地址')
    ])
    
    phone = StringField('手机号', validators=[
        Length(min=11, max=11, message='请输入11位手机号'),
        Regexp('^1[3-9]\d{9}$', message='请输入有效的手机号')
    ])
    
    nickname = StringField('昵称', validators=[
        Length(max=50, message='昵称长度不能超过50个字符')
    ])
    
    bio = TextAreaField('个人简介', validators=[
        Length(max=500, message='个人简介长度不能超过500个字符')
    ])
    
    avatar = FileField('头像', validators=[
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], message='只支持jpg、jpeg、png、gif格式的图片')
    ])
    
    submit = SubmitField('保存')
    
    def __init__(self, original_username=None, original_email=None, original_phone=None, *args, **kwargs):
        super(EditProfileForm, self).__init__(*args, **kwargs)
        self.original_username = original_username
        self.original_email = original_email
        self.original_phone = original_phone
    
    def validate_username(self, username):
        """验证用户名唯一性"""
        if username.data != self.original_username:
            user = User.query.filter_by(username=username.data).first()
            if user:
                raise ValidationError('该用户名已被使用，请选择其他用户名')
    
    def validate_email(self, email):
        """验证邮箱唯一性"""
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('该邮箱已被注册，请使用其他邮箱')
    
    def validate_phone(self, phone):
        """验证手机号唯一性"""
        if phone.data and phone.data != self.original_phone:
            user = User.query.filter_by(phone=phone.data).first()
            if user:
                raise ValidationError('该手机号已被注册，请使用其他手机号')

class ChangePasswordForm(FlaskForm):
    """修改密码表单"""
    old_password = PasswordField('当前密码', validators=[
        DataRequired(message='请输入当前密码')
    ])
    email_code = StringField('邮箱验证码', validators=[
        DataRequired(message='请输入邮箱验证码'),
        Length(min=6, max=6, message='邮箱验证码为6位数字'),
        Regexp('^[0-9]{6}$', message='邮箱验证码必须为6位数字')
    ])
    password = PasswordField('新密码', validators=[
        DataRequired(message='请输入新密码'),
        Length(min=6, max=20, message='密码长度应在6-20个字符之间')
    ])
    password2 = PasswordField('确认新密码', validators=[
        DataRequired(message='请确认新密码'),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('修改密码')

    def validate_email_code(self, email_code):
        """验证邮箱验证码"""
        from flask_login import current_user
        from app.models import EmailVerificationCode

        if not current_user.is_authenticated:
            raise ValidationError('用户未登录')

        # 获取最新的有效验证码
        verification_code = EmailVerificationCode.get_latest_valid_code(current_user.email)

        if not verification_code:
            raise ValidationError('验证码不存在或已过期，请重新获取')

        if verification_code.code != email_code.data:
            raise ValidationError('验证码错误，请重新输入')

class ResetPasswordRequestForm(FlaskForm):
    """重置密码请求表单"""
    email = StringField('邮箱', validators=[
        DataRequired(message='请输入邮箱'),
        Email(message='请输入有效的邮箱地址')
    ])
    submit = SubmitField('发送重置邮件')

class ResetPasswordForm(FlaskForm):
    """重置密码表单"""
    password = PasswordField('新密码', validators=[
        DataRequired(message='请输入新密码'),
        Length(min=6, max=20, message='密码长度应在6-20个字符之间')
    ])
    password2 = PasswordField('确认新密码', validators=[
        DataRequired(message='请确认新密码'),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('重置密码')
