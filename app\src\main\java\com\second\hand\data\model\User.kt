package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import com.second.hand.data.model.enums.UserRole

/**
 * 用户数据模型
 * 对应Flask后端的User模型
 */
@Entity(tableName = "users")
data class User(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    @SerializedName("username")
    var username: String = "",

    @SerializedName("email")
    var email: String = "",

    @SerializedName("phone")
    var phone: String? = null,

    // 个人信息
    @SerializedName("nickname")
    var nickname: String? = null,

    @SerializedName("avatar")
    var avatar: String? = null,

    @SerializedName("bio")
    var bio: String? = null,

    // 用户状态
    @SerializedName("role")
    var role: UserRole = UserRole.USER,

    @SerializedName("is_active")
    var isActive: Boolean = true,

    @SerializedName("email_verified")
    var emailVerified: Boolean = false,

    // 时间戳
    @SerializedName("created_at")
    var createdAt: String = "",

    @SerializedName("updated_at")
    var updatedAt: String? = null,

    @SerializedName("last_seen")
    var lastSeen: String? = null
) {
    /**
     * 获取显示名称
     */
    fun getDisplayName(): String {
        return nickname?.takeIf { it.isNotBlank() } ?: username
    }
    
    /**
     * 获取头像URL
     */
    fun getAvatarUrl(baseUrl: String): String {
        return if (!avatar.isNullOrBlank()) {
            "$baseUrl/static/uploads/avatars/$avatar"
        } else {
            "$baseUrl/avatar/$id"
        }
    }
    
    /**
     * 是否为管理员
     */
    fun isAdmin(): Boolean {
        return role == UserRole.ADMIN
    }
    
    /**
     * 获取头像显示字符
     */
    fun getAvatarChar(): String {
        val displayName = getDisplayName()
        return if (displayName.isNotEmpty()) {
            displayName.first().toString().uppercase()
        } else {
            "U"
        }
    }
}
