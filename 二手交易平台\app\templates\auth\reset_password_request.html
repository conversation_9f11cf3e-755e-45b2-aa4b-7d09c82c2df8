{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header text-center">
                <h3><i class="fas fa-key"></i> 重置密码</h3>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">请输入您的邮箱地址，我们将向您发送重置密码的链接。</p>
                
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">
                    想起密码了？ <a href="{{ url_for('auth.login') }}">返回登录</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}