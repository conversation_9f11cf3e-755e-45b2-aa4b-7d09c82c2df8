package com.second.hand.data.model

import com.google.gson.annotations.SerializedName

/**
 * 错误响应数据模型
 * 对应Flask后端的error对象结构
 */
data class ErrorResponse(
    @SerializedName("code")
    val code: String = "",

    @SerializedName("message")
    val message: String = "",

    @SerializedName("details")
    val details: Any? = null
)

/**
 * API响应基础数据模型
 * 统一的API响应格式，对应Flask后端的响应结构
 */
data class BaseResponse<T>(
    @SerializedName("success")
    val success: Boolean = false,

    @SerializedName("message")
    val message: String = "",

    @SerializedName("data")
    val data: T? = null,

    @SerializedName("error")
    val error: ErrorResponse? = null,

    @SerializedName("code")
    val code: Int? = null,

    @SerializedName("timestamp")
    val timestamp: String? = null
) {
    /**
     * 检查响应是否成功
     */
    fun isSuccess(): Boolean = success

    /**
     * 获取错误信息
     */
    fun getErrorMessage(): String {
        return when {
            error != null -> error.message.ifEmpty { "请求失败" }
            !success && message.isNotEmpty() -> message
            else -> "未知错误"
        }
    }

    /**
     * 获取错误代码
     */
    fun getErrorCode(): String? {
        return error?.code
    }
}
