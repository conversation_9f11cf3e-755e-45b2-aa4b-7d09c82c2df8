{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>邮件管理</h2>
                <a href="{{ url_for('admin_panel.send_email') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>发送邮件
                </a>
            </div>

            <!-- 邮件统计卡片 -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">总邮件数</h6>
                                    <h3>{{ total_emails }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">已发送</h6>
                                    <h3>{{ sent_emails }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-paper-plane fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">草稿</h6>
                                    <h3>{{ draft_emails }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-edit fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">发送失败</h6>
                                    <h3>{{ failed_emails }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">快捷操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('admin_panel.send_email') }}" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus me-1"></i>发送新邮件
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('admin_panel.email_list') }}" class="btn btn-info btn-block">
                                        <i class="fas fa-list me-1"></i>查看所有邮件
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('admin_panel.email_list', status='sent') }}" class="btn btn-success btn-block">
                                        <i class="fas fa-check me-1"></i>已发送邮件
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('admin_panel.email_list', status='failed') }}" class="btn btn-danger btn-block">
                                        <i class="fas fa-times me-1"></i>发送失败
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近邮件 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">最近邮件</h5>
                    <a href="{{ url_for('admin_panel.email_list') }}" class="btn btn-sm btn-outline-primary">查看全部</a>
                </div>
                <div class="card-body">
                    {% if recent_emails %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>主题</th>
                                    <th>收件人数量</th>
                                    <th>状态</th>
                                    <th>发送时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for email in recent_emails %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('admin_panel.email_detail', email_id=email.id) }}" class="text-decoration-none">
                                            {{ email.subject }}
                                        </a>
                                    </td>
                                    <td>{{ email.recipients|length }}</td>
                                    <td>
                                        {% if email.status == 'sent' %}
                                        <span class="badge bg-success">已发送</span>
                                        {% elif email.status == 'draft' %}
                                        <span class="badge bg-warning">草稿</span>
                                        {% elif email.status == 'failed' %}
                                        <span class="badge bg-danger">发送失败</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if email.sent_at %}
                                        {{ email.sent_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        {{ email.created_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('admin_panel.email_detail', email_id=email.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if email.status == 'sent' %}
                                        <a href="{{ url_for('admin_panel.reply_email', email_id=email.id) }}" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-reply"></i>
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无邮件记录</p>
                        <a href="{{ url_for('admin_panel.send_email') }}" class="btn btn-primary">发送第一封邮件</a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.btn-block {
    width: 100%;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
