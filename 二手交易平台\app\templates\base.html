<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>
        {% if title %}{{ title }} - 二手交易平台{% else %}二手交易平台{% endif %}
    </title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛒</text></svg>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <!-- 头像样式 -->
    <style>
    /* 调试信息样式 */
    .debug-info {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 9999;
        display: none;
    }

    .debug-info.show {
        display: block;
    }
    .user-avatar {
        object-fit: cover;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .user-avatar-sm {
        width: 24px;
        height: 24px;
    }

    .user-avatar-md {
        width: 48px;
        height: 48px;
    }

    .user-avatar-lg {
        width: 120px;
        height: 120px;
    }

    .user-avatar-xl {
        width: 200px;
        height: 200px;
    }

    /* 确保头像始终是圆形 */
    .rounded-circle {
        border-radius: 50% !important;
    }

    /* 头像加载失败时的样式 */
    .avatar-fallback {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.8em;
    }
    </style>
</head>
<body class="{% if current_user.is_authenticated %}authenticated{% else %}anonymous{% endif %}">
    <!-- 现代化导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-store"></i>
                <span>二手交易平台</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="切换导航">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'main.index' }}"
                           href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'main.search' }}"
                           href="{{ url_for('main.search') }}">
                            <i class="fas fa-search me-1"></i>商品搜索
                        </a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'products.publish' }}"
                           href="{{ url_for('products.publish') }}">
                            <i class="fas fa-plus-circle me-1"></i>发布商品
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- 搜索框 -->
                <form class="d-flex me-3 search-form" method="GET" action="{{ url_for('main.search') }}">
                    <div class="input-group">
                        <input class="form-control" type="search" name="q" placeholder="搜索商品..."
                               value="{{ request.args.get('q', '') }}" aria-label="搜索商品">
                        <button class="btn btn-outline-light" type="submit" aria-label="搜索">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                
                <ul class="navbar-nav align-items-center">
                    {% if current_user.is_authenticated %}
                    <!-- 通知图标 -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link position-relative d-flex align-items-center" href="#"
                           id="notificationDropdown" role="button" data-bs-toggle="dropdown"
                           aria-expanded="false" aria-label="通知">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge notification-badge"
                                  style="display: none;">
                                0
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown"
                            aria-labelledby="notificationDropdown">
                            <li><h6 class="dropdown-header">
                                <i class="fas fa-bell me-1"></i>通知中心
                            </h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li class="notification-list">
                                <div class="text-center p-3 text-muted">
                                    <i class="fas fa-bell-slash fa-2x mb-2"></i>
                                    <p class="mb-0">暂无新通知</p>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="{{ url_for('notifications.notifications') }}">
                                    <i class="fas fa-list me-1"></i>查看所有通知
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 消息图标 -->
                    <li class="nav-item me-2">
                        <a class="nav-link position-relative d-flex align-items-center"
                           href="{{ url_for('messages.index') }}" aria-label="消息">
                            <i class="fas fa-comments"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge message-badge"
                                  style="display: none;">
                                0
                            </span>
                        </a>
                    </li>

                    <!-- 用户菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#"
                           id="navbarDropdown" role="button" data-bs-toggle="dropdown"
                           aria-expanded="false" aria-label="用户菜单">
                            <img src="{{ current_user.get_avatar_url() }}" alt="{{ current_user.nickname or current_user.username }}的头像"
                                 class="rounded-circle user-avatar user-avatar-sm me-2">
                            <span class="d-none d-md-inline">{{ current_user.nickname or current_user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                    <i class="fas fa-user me-2"></i>个人资料
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('products.my_products') }}">
                                    <i class="fas fa-box me-2"></i>我的商品
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('orders.my_orders') }}">
                                    <i class="fas fa-shopping-cart me-2"></i>我的订单
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('products.favorites') }}">
                                    <i class="fas fa-heart me-2"></i>我的收藏
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            {% if current_user.is_admin() %}
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin_panel.dashboard') }}">
                                    <i class="fas fa-cog me-2"></i>管理后台
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>退出登录
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <!-- 未登录用户按钮 -->
                    <li class="nav-item me-2">
                        <a class="btn btn-outline-light btn-sm nav-auth-btn" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <span>登录</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-light btn-sm nav-auth-btn" href="{{ url_for('auth.register') }}">
                            <i class="fas fa-user-plus me-1"></i>
                            <span>注册</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 调试信息面板 -->
    <div id="debugInfo" class="debug-info">
        <div>窗口宽度: <span id="windowWidth"></span>px</div>
        <div>窗口高度: <span id="windowHeight"></span>px</div>
        <div>设备像素比: <span id="devicePixelRatio"></span></div>
        <div>用户代理: <span id="userAgent"></span></div>
        <div>访问地址: <span id="currentUrl"></span></div>
        <button onclick="toggleDebugInfo()" style="margin-top: 5px; padding: 2px 5px;">隐藏</button>
    </div>

    <!-- 消息提示 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- 主要内容 -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>
    
    <!-- 现代化页脚 -->
    <footer class="py-5 mt-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6;">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="footer-brand mb-3">
                        <h5 class="d-flex align-items-center">
                            <i class="fas fa-store text-primary me-2"></i>
                            二手交易平台
                        </h5>
                        <p class="text-muted mb-3">安全、便捷的二手商品交易平台，让闲置物品重新焕发价值</p>
                        <div class="social-links">
                            <a href="#" class="text-muted me-3" aria-label="微信">
                                <i class="fab fa-weixin fa-lg"></i>
                            </a>
                            <a href="#" class="text-muted me-3" aria-label="微博">
                                <i class="fab fa-weibo fa-lg"></i>
                            </a>
                            <a href="#" class="text-muted me-3" aria-label="QQ">
                                <i class="fab fa-qq fa-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="footer-title">快速链接</h6>
                    <ul class="list-unstyled footer-links">
                        <li><a href="{{ url_for('main.index') }}" class="text-muted">
                            <i class="fas fa-home me-1"></i>首页
                        </a></li>
                        <li><a href="{{ url_for('main.search') }}" class="text-muted">
                            <i class="fas fa-search me-1"></i>商品搜索
                        </a></li>
                        <li><a href="{{ url_for('main.about') }}" class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>关于我们
                        </a></li>
                        <li><a href="{{ url_for('main.help') }}" class="text-muted">
                            <i class="fas fa-question-circle me-1"></i>帮助中心
                        </a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h6 class="footer-title">用户服务</h6>
                    <ul class="list-unstyled footer-links">
                        {% if current_user.is_authenticated %}
                        <li><a href="{{ url_for('products.publish') }}" class="text-muted">
                            <i class="fas fa-plus-circle me-1"></i>发布商品
                        </a></li>
                        <li><a href="{{ url_for('products.my_products') }}" class="text-muted">
                            <i class="fas fa-box me-1"></i>我的商品
                        </a></li>
                        <li><a href="{{ url_for('orders.my_orders') }}" class="text-muted">
                            <i class="fas fa-shopping-cart me-1"></i>我的订单
                        </a></li>
                        {% else %}
                        <li><a href="{{ url_for('auth.register') }}" class="text-muted">
                            <i class="fas fa-user-plus me-1"></i>注册账号
                        </a></li>
                        <li><a href="{{ url_for('auth.login') }}" class="text-muted">
                            <i class="fas fa-sign-in-alt me-1"></i>用户登录
                        </a></li>
                        {% endif %}
                        <li><a href="{{ url_for('main.contact') }}" class="text-muted">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h6 class="footer-title">联系方式</h6>
                    <div class="contact-info">
                        <p class="text-muted mb-2">
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <EMAIL>
                        </p>
                        <p class="text-muted mb-2">
                            <i class="fas fa-phone text-primary me-2"></i>
                            ************
                        </p>
                        <p class="text-muted mb-2">
                            <i class="fas fa-clock text-primary me-2"></i>
                            服务时间：9:00-18:00
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; 2024 二手交易平台. 保留所有权利.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">
                        <small>让闲置物品重新焕发价值</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- 调试工具 -->
    <script>
    function updateDebugInfo() {
        document.getElementById('windowWidth').textContent = window.innerWidth;
        document.getElementById('windowHeight').textContent = window.innerHeight;
        document.getElementById('devicePixelRatio').textContent = window.devicePixelRatio;
        document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 50) + '...';
        document.getElementById('currentUrl').textContent = window.location.href;
    }

    function toggleDebugInfo() {
        const debugInfo = document.getElementById('debugInfo');
        debugInfo.classList.toggle('show');
        if (debugInfo.classList.contains('show')) {
            updateDebugInfo();
        }
    }

    // 按Ctrl+D显示调试信息
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'd') {
            e.preventDefault();
            toggleDebugInfo();
        }
    });

    // 窗口大小改变时更新调试信息
    window.addEventListener('resize', function() {
        if (document.getElementById('debugInfo').classList.contains('show')) {
            updateDebugInfo();
        }
    });
    </script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/zh-cn.min.js"></script>



    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- 头像处理脚本 -->
    <script>
    // 头像加载失败时的处理
    function handleAvatarError(img) {
        // 如果已经是备用头像，不再处理
        if (img.classList.contains('avatar-fallback')) {
            return;
        }

        // 获取用户名首字符
        const alt = img.getAttribute('alt') || '';
        const char = alt.charAt(0).toUpperCase() || 'U';

        // 创建文字头像
        const canvas = document.createElement('canvas');
        const size = Math.max(img.width || 48, img.height || 48);
        canvas.width = size;
        canvas.height = size;

        const ctx = canvas.getContext('2d');

        // 生成背景色
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
        const colorIndex = char.charCodeAt(0) % colors.length;
        const bgColor = colors[colorIndex];

        // 绘制圆形背景
        ctx.fillStyle = bgColor;
        ctx.beginPath();
        ctx.arc(size/2, size/2, size/2, 0, 2 * Math.PI);
        ctx.fill();

        // 绘制文字（完美居中）
        ctx.fillStyle = 'white';
        ctx.font = `bold ${size * 0.4}px "Microsoft YaHei", "SimHei", Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 对于中文字符，向上调整位置以获得更好的视觉居中效果
        const isChineseChar = /[\u4e00-\u9fff]/.test(char);
        const yOffset = isChineseChar ? -(size * 0.04) : 0;  // 中文字符向上偏移4%的尺寸

        ctx.fillText(char, size/2, size/2 + yOffset);

        // 替换图片
        img.src = canvas.toDataURL();
        img.classList.add('avatar-fallback');
    }

    // 为所有头像图片添加错误处理
    $(document).ready(function() {
        $('img[src*="/avatar/"], img[src*="default-avatar"]').on('error', function() {
            handleAvatarError(this);
        });

        // 为动态添加的头像也添加错误处理
        $(document).on('error', 'img[src*="/avatar/"], img[src*="default-avatar"]', function() {
            handleAvatarError(this);
        });
    });
    </script>

    <!-- 实时通知系统 -->
    {% if current_user.is_authenticated %}
    <script>
    // 实时通知系统
    let notificationCheckInterval;
    let lastNotificationCount = 0;

    // 刷新通知
    function refreshNotifications() {
        $.ajax({
            url: '/notifications/api/unread_count',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    // 只更新通知徽章，不包含消息数量
                    updateNotificationBadge(response.unread_notifications);

                    // 如果有新通知，获取最新通知列表
                    if (response.unread_notifications > lastNotificationCount) {
                        loadLatestNotifications();
                        // 显示新通知提示
                        if (lastNotificationCount > 0) {
                            showNotificationToast('您有新的通知消息');
                        }
                    }
                    lastNotificationCount = response.unread_notifications;
                }
            },
            error: function() {
                console.log('获取通知数量失败');
            }
        });
    }

    // 更新通知徽章（base.html版本）
    function updateNotificationBadge(count) {
        // 直接更新徽章，不再检查全局函数避免递归
        const badge = $('.notification-badge');
        if (count > 0) {
            badge.text(count > 99 ? '99+' : count).show();
        } else {
            badge.hide();
        }
    }

    // 加载最新通知
    function loadLatestNotifications() {
        $.ajax({
            url: '/notifications/api/latest_notifications',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    updateNotificationDropdown(response.notifications);
                }
            },
            error: function() {
                console.log('获取通知列表失败');
            }
        });
    }

    // 更新通知下拉菜单
    function updateNotificationDropdown(notifications) {
        const notificationList = $('.notification-list');

        if (notifications.length === 0) {
            notificationList.html(`
                <div class="text-center p-3 text-muted">
                    <i class="fas fa-bell-slash fa-2x mb-2"></i>
                    <p class="mb-0">暂无新通知</p>
                </div>
            `);
            return;
        }

        let html = '';
        notifications.forEach(function(notification) {
            const isUnread = !notification.is_read;
            html += `
                <li>
                    <a class="dropdown-item ${isUnread ? 'bg-light' : ''}" href="/notifications/notifications">
                        <div class="d-flex align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1 ${isUnread ? 'fw-bold' : ''}">${notification.title}</h6>
                                <p class="mb-1 small text-muted">${notification.content.substring(0, 50)}${notification.content.length > 50 ? '...' : ''}</p>
                                <small class="text-muted">${notification.time_ago}</small>
                            </div>
                            ${isUnread ? '<span class="badge bg-danger ms-2">新</span>' : ''}
                        </div>
                    </a>
                </li>
            `;
        });

        notificationList.html(html);
    }

    // 显示通知提示
    function showNotificationToast(message) {
        // 创建toast通知
        const toastHtml = `
            <div class="toast align-items-center text-white bg-primary border-0" role="alert" aria-live="assertive" aria-atomic="true" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-bell me-2"></i>${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        $('body').append(toastHtml);
        const toast = new bootstrap.Toast($('.toast').last()[0]);
        toast.show();

        // 3秒后自动移除
        setTimeout(function() {
            $('.toast').last().remove();
        }, 3000);
    }

    // 页面加载时初始化
    $(document).ready(function() {
        // 立即检查一次通知
        refreshNotifications();

        // 每0.5秒检查一次新通知（实时更新）
        notificationCheckInterval = setInterval(refreshNotifications, 500);

        // 页面获得焦点时检查通知
        $(window).focus(function() {
            refreshNotifications();
        });

        // 点击通知图标时刷新
        $('#notificationDropdown').click(function() {
            refreshNotifications();
        });
    });

    // 页面卸载时清除定时器
    $(window).on('beforeunload', function() {
        if (notificationCheckInterval) {
            clearInterval(notificationCheckInterval);
        }
    });
    </script>
    {% endif %}

    <!-- 全局实时刷新系统配置 -->
    <script src="{{ url_for('static', filename='js/refresh-config.js') }}"></script>

    <!-- 全局实时刷新系统 -->
    <script>
    // 全局实时刷新系统
    class GlobalRefreshSystem {
        constructor() {
            // 从配置文件加载设置
            const config = window.RefreshConfig || {};
            const pageConfig = config.getCurrentPageConfig ? config.getCurrentPageConfig() : {};

            this.refreshInterval = null;
            this.refreshRate = pageConfig.refreshRate || config.defaultRefreshRate || 500;
            this.isEnabled = config.enableByDefault !== false;
            this.isPaused = config.shouldPauseRefresh ? config.shouldPauseRefresh() : false;
            this.lastUserActivity = Date.now();
            this.refreshMode = pageConfig.refreshMode || config.defaultRefreshMode || 'smart';
            this.userActivityTimeout = config.userActivityTimeout || 2000;
            this.useCustomRefresh = pageConfig.useCustomRefresh || false;
            this.config = config;

            // 性能监控
            this.performanceData = [];
            this.refreshCount = 0;
            this.startTime = Date.now();

            if (this.isEnabled) {
                this.init();
            }
        }

        init() {
            this.createControlPanel();
            this.bindEvents();
            this.startRefresh();
        }

        createControlPanel() {
            // 从配置获取样式
            const panelStyle = this.config.controlPanelStyle || {};
            const hintStyle = this.config.hintStyle || {};
            const availableRates = this.config.availableRefreshRates || [
                { value: 500, label: '0.5秒' },
                { value: 1000, label: '1秒' },
                { value: 2000, label: '2秒' },
                { value: 5000, label: '5秒' }
            ];

            // 生成刷新间隔选项
            const rateOptions = availableRates.map(rate =>
                `<option value="${rate.value}">${rate.label}</option>`
            ).join('');

            // 生成样式字符串
            const panelStyleStr = Object.entries(panelStyle).map(([key, value]) =>
                `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`
            ).join('; ');

            // 创建刷新控制面板
            const controlPanel = $(`
                <div id="refreshControlPanel" style="${panelStyleStr}; display: none;">
                    <div style="margin-bottom: 8px; border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 5px;">
                        <strong>🔄 实时刷新控制</strong>
                        <button id="toggleRefreshPanel" style="float: right; background: none; border: none; color: white; cursor: pointer; font-size: 16px;">×</button>
                    </div>
                    <div style="margin-bottom: 6px;">
                        <span style="display: inline-block; width: 50px;">状态:</span>
                        <span id="refreshStatus" style="font-weight: bold; color: #4CAF50;">运行中</span>
                    </div>
                    <div style="margin-bottom: 6px;">
                        <span style="display: inline-block; width: 50px;">模式:</span>
                        <select id="refreshModeSelect" style="background: #333; color: white; border: 1px solid #555; padding: 2px; border-radius: 3px;">
                            <option value="smart">智能刷新</option>
                            <option value="full">完全刷新</option>
                            <option value="off">关闭刷新</option>
                        </select>
                    </div>
                    <div style="margin-bottom: 6px;">
                        <span style="display: inline-block; width: 50px;">间隔:</span>
                        <select id="refreshRateSelect" style="background: #333; color: white; border: 1px solid #555; padding: 2px; border-radius: 3px;">
                            ${rateOptions}
                        </select>
                    </div>
                    <div style="margin-bottom: 6px;">
                        <span style="display: inline-block; width: 50px;">统计:</span>
                        <span id="refreshStats" style="font-size: 10px; color: #ccc;">刷新次数: 0</span>
                    </div>
                    <div style="text-align: center;">
                        <button id="pauseRefreshBtn" style="background: #007bff; color: white; border: none; padding: 4px 10px; margin-right: 5px; cursor: pointer; border-radius: 3px; font-size: 11px;">暂停</button>
                        <button id="manualRefreshBtn" style="background: #28a745; color: white; border: none; padding: 4px 10px; cursor: pointer; border-radius: 3px; font-size: 11px;">立即刷新</button>
                    </div>
                    <div style="margin-top: 8px; padding-top: 5px; border-top: 1px solid rgba(255,255,255,0.2); font-size: 10px; color: #aaa;">
                        快捷键: Ctrl+R 显示/隐藏 | Ctrl+P 暂停/恢复
                    </div>
                </div>
            `);

            $('body').append(controlPanel);

            // 添加显示控制面板的快捷键提示
            if (this.config.showControlPanelHint !== false) {
                const hintStyleStr = Object.entries(hintStyle).map(([key, value]) =>
                    `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`
                ).join('; ');

                const shortcutHint = $(`
                    <div id="refreshShortcutHint" style="${hintStyleStr}">
                        💡 按 Ctrl+R 显示刷新控制面板
                    </div>
                `);

                $('body').append(shortcutHint);

                // 配置的时间后隐藏提示
                const duration = this.config.controlPanelHintDuration || 3000;
                setTimeout(() => {
                    shortcutHint.fadeOut();
                }, duration);
            }
        }

        bindEvents() {
            const self = this;

            // 监听用户活动
            $(document).on('mousemove keypress click scroll', function() {
                self.lastUserActivity = Date.now();
            });

            // 监听表单输入
            $(document).on('focus', 'input, textarea, select', function() {
                self.pauseRefresh();
            });

            $(document).on('blur', 'input, textarea, select', function() {
                setTimeout(() => {
                    if (!$('input:focus, textarea:focus, select:focus').length) {
                        self.resumeRefresh();
                    }
                }, 1000);
            });

            // 快捷键控制
            $(document).on('keydown', function(e) {
                if (e.ctrlKey && e.key === 'r') {
                    e.preventDefault();
                    $('#refreshControlPanel').toggle();
                }

                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    self.togglePause();
                }
            });

            // 控制面板事件
            $('#toggleRefreshPanel').on('click', function() {
                $('#refreshControlPanel').hide();
            });

            $('#refreshModeSelect').on('change', function() {
                self.setRefreshMode($(this).val());
            });

            $('#refreshRateSelect').on('change', function() {
                self.setRefreshRate(parseInt($(this).val()));
            });

            $('#pauseRefreshBtn').on('click', function() {
                self.togglePause();
            });

            $('#manualRefreshBtn').on('click', function() {
                self.performRefresh();
            });

            // 页面可见性变化
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    self.pauseRefresh();
                } else {
                    self.resumeRefresh();
                }
            });
        }

        startRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }

            if (this.refreshMode === 'off') {
                return;
            }

            this.refreshInterval = setInterval(() => {
                if (!this.isPaused && this.shouldRefresh()) {
                    this.performRefresh();
                }
            }, this.refreshRate);

            this.updateStatus();
        }

        shouldRefresh() {
            if (this.refreshMode === 'full') {
                return true;
            }

            if (this.refreshMode === 'smart') {
                // 智能刷新：检查用户活动
                const timeSinceActivity = Date.now() - this.lastUserActivity;
                return timeSinceActivity > this.userActivityTimeout;
            }

            return false;
        }

        performRefresh() {
            const startTime = performance.now();
            this.refreshCount++;

            // 记录调试信息
            if (this.config.log) {
                this.config.log(`执行刷新 #${this.refreshCount}`, {
                    mode: this.refreshMode,
                    rate: this.refreshRate,
                    timeSinceActivity: Date.now() - this.lastUserActivity
                });
            }

            try {
                if (this.refreshMode === 'full') {
                    // 完全刷新页面
                    window.location.reload();
                } else if (this.refreshMode === 'smart') {
                    // 智能刷新：只刷新特定内容
                    this.smartRefresh();
                }

                // 记录性能数据
                const endTime = performance.now();
                const duration = endTime - startTime;

                if (this.config.performanceMonitoring && this.config.performanceMonitoring.enabled) {
                    this.recordPerformance(duration);
                }

                // 更新统计信息
                this.updateStats();

            } catch (error) {
                console.error('[GlobalRefresh] 刷新时发生错误:', error);
                if (this.config.log) {
                    this.config.log('刷新错误', error);
                }
            }
        }

        recordPerformance(duration) {
            const perfData = {
                timestamp: Date.now(),
                duration: duration,
                refreshCount: this.refreshCount,
                mode: this.refreshMode,
                rate: this.refreshRate
            };

            this.performanceData.push(perfData);

            // 限制性能数据数量
            const maxEntries = this.config.performanceMonitoring.maxLogEntries || 100;
            if (this.performanceData.length > maxEntries) {
                this.performanceData.shift();
            }

            // 定期输出性能报告
            const logInterval = this.config.performanceMonitoring.logInterval || 10000;
            if (this.refreshCount % Math.floor(logInterval / this.refreshRate) === 0) {
                this.logPerformanceReport();
            }
        }

        logPerformanceReport() {
            if (this.performanceData.length === 0) return;

            const avgDuration = this.performanceData.reduce((sum, data) => sum + data.duration, 0) / this.performanceData.length;
            const maxDuration = Math.max(...this.performanceData.map(data => data.duration));
            const minDuration = Math.min(...this.performanceData.map(data => data.duration));

            console.log('[GlobalRefresh] 性能报告:', {
                总刷新次数: this.refreshCount,
                平均耗时: `${avgDuration.toFixed(2)}ms`,
                最大耗时: `${maxDuration.toFixed(2)}ms`,
                最小耗时: `${minDuration.toFixed(2)}ms`,
                运行时间: `${((Date.now() - this.startTime) / 1000).toFixed(1)}s`
            });
        }

        updateStats() {
            const runtime = ((Date.now() - this.startTime) / 1000).toFixed(1);
            $('#refreshStats').text(`刷新: ${this.refreshCount}次 | 运行: ${runtime}s`);
        }

        smartRefresh() {
            // 智能刷新逻辑：根据页面类型执行不同的刷新策略
            const currentPath = window.location.pathname;

            if (currentPath.includes('/messages')) {
                // 消息页面：使用现有的实时更新机制
                if (typeof updateConversations === 'function') {
                    updateConversations();
                }
            } else if (currentPath.includes('/admin')) {
                // 管理页面：刷新统计数据
                this.refreshAdminData();
            } else if (currentPath.includes('/products')) {
                // 商品页面：刷新商品列表
                this.refreshProductData();
            } else {
                // 其他页面：刷新通知和基础数据
                if (typeof refreshNotifications === 'function') {
                    refreshNotifications();
                }
            }
        }

        refreshAdminData() {
            // 刷新管理页面数据
            if (typeof updateOnlineUsers === 'function') {
                updateOnlineUsers();
            }
        }

        refreshProductData() {
            // 刷新商品页面数据
            // 可以在这里添加商品列表的刷新逻辑
        }

        setRefreshMode(mode) {
            this.refreshMode = mode;
            this.startRefresh();
            this.updateStatus();
        }

        setRefreshRate(rate) {
            this.refreshRate = rate;
            this.startRefresh();
            this.updateStatus();
        }

        pauseRefresh() {
            this.isPaused = true;
            this.updateStatus();
        }

        resumeRefresh() {
            this.isPaused = false;
            this.updateStatus();
        }

        togglePause() {
            if (this.isPaused) {
                this.resumeRefresh();
            } else {
                this.pauseRefresh();
            }
        }

        updateStatus() {
            const statusText = this.refreshMode === 'off' ? '已关闭' :
                              this.isPaused ? '已暂停' : '运行中';
            $('#refreshStatus').text(statusText);
            $('#pauseRefreshBtn').text(this.isPaused ? '恢复' : '暂停');
            $('#refreshModeSelect').val(this.refreshMode);
            $('#refreshRateSelect').val(this.refreshRate);
        }

        destroy() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
            $('#refreshControlPanel').remove();
            $('#refreshShortcutHint').remove();
        }
    }

    // 初始化全局刷新系统
    let globalRefreshSystem;

    $(document).ready(function() {
        globalRefreshSystem = new GlobalRefreshSystem();

        // 检查是否需要刷新通知和消息（登录成功后）
        {% if session.get('refresh_notifications') %}
        if (typeof refreshNotifications === 'function') {
            setTimeout(refreshNotifications, 100);
        }
        {% endif %}

        {% if session.get('refresh_messages') %}
        if (typeof loadUnreadCounts === 'function') {
            setTimeout(loadUnreadCounts, 200);
        }
        {% endif %}

        // 页面卸载时清理
        $(window).on('beforeunload', function() {
            if (globalRefreshSystem) {
                globalRefreshSystem.destroy();
            }
        });
    });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
