#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息路由
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, abort, current_app
from flask_login import login_required, current_user
from app import db
from app.messages import bp
from app.models import Message, User, Product, Notification
from sqlalchemy import or_, and_, desc
from datetime import datetime

def local_now():
    """获取当前本地时间"""
    return datetime.now()

@bp.route('/')
@login_required
def index():
    """消息列表"""
    # 获取所有对话 - 按商品分组，避免重复显示
    # 使用子查询来获取每个商品的对话信息
    conversations = db.session.query(
        Message.product_id,
        db.func.max(Message.created_at).label('last_message_time'),
        db.func.count(Message.id).label('message_count'),
        # 获取对话中的另一个用户ID（非当前用户）
        db.case(
            (Message.sender_id == current_user.id, Message.receiver_id),
            else_=Message.sender_id
        ).label('other_user_id')
    ).filter(
        or_(Message.sender_id == current_user.id, Message.receiver_id == current_user.id),
        # 排除当前用户已删除的对话
        or_(
            and_(Message.sender_id == current_user.id, Message.deleted_by_sender == False),
            and_(Message.receiver_id == current_user.id, Message.deleted_by_receiver == False)
        ),
        Message.product_id.isnot(None)  # 只显示有关联商品的对话
    ).group_by(
        Message.product_id,
        db.case(
            (Message.sender_id == current_user.id, Message.receiver_id),
            else_=Message.sender_id
        )
    ).order_by(desc('last_message_time')).all()
    
    # 处理对话数据
    conversation_list = []
    for conv in conversations:
        # 获取对方用户信息
        other_user = User.query.get(conv.other_user_id)
        if not other_user:
            continue
        
        # 获取最后一条消息 - 排除当前用户已删除的消息
        last_message = Message.query.filter(
            or_(
                and_(Message.sender_id == current_user.id, Message.receiver_id == conv.other_user_id),
                and_(Message.sender_id == conv.other_user_id, Message.receiver_id == current_user.id)
            ),
            Message.product_id == conv.product_id,
            # 排除当前用户已删除的消息
            or_(
                and_(Message.sender_id == current_user.id, Message.deleted_by_sender == False),
                and_(Message.receiver_id == current_user.id, Message.deleted_by_receiver == False)
            )
        ).order_by(desc(Message.created_at)).first()

        # 获取未读消息数
        unread_count = Message.query.filter(
            Message.sender_id == conv.other_user_id,
            Message.receiver_id == current_user.id,
            Message.product_id == conv.product_id,
            Message.is_read == False
        ).count()
        
        # 获取相关商品
        product = Product.query.get(conv.product_id) if conv.product_id else None
        
        conversation_list.append({
            'other_user': other_user,
            'product': product,
            'last_message': last_message,
            'unread_count': unread_count,
            'last_time': conv.last_message_time
        })
    
    return render_template('messages/index.html',
                         title='消息中心',
                         conversations=conversation_list)

@bp.route('/chat/<int:user_id>')
@bp.route('/chat/<int:user_id>/<int:product_id>')
@login_required
def chat(user_id, product_id=None):
    """聊天页面"""
    other_user = User.query.get_or_404(user_id)
    product = None
    if product_id:
        product = Product.query.filter_by(id=product_id).first()
        # 如果商品不存在或已删除，设为None但不影响聊天功能
    
    # 获取聊天记录 - 只显示当前用户未删除的消息
    messages = Message.query.filter(
        or_(
            and_(Message.sender_id == current_user.id, Message.receiver_id == user_id),
            and_(Message.sender_id == user_id, Message.receiver_id == current_user.id)
        ),
        Message.product_id == product_id,
        # 只显示当前用户未删除的消息
        or_(
            and_(Message.sender_id == current_user.id, Message.deleted_by_sender == False),
            and_(Message.receiver_id == current_user.id, Message.deleted_by_receiver == False)
        )
    ).order_by(Message.created_at).all()

    # 标记消息为已读
    unread_messages = Message.query.filter(
        Message.sender_id == user_id,
        Message.receiver_id == current_user.id,
        Message.product_id == product_id,
        Message.is_read == False
    ).all()

    for msg in unread_messages:
        msg.is_read = True

    if unread_messages:
        # 同时标记相关的消息通知为已读
        message_ids = [msg.id for msg in unread_messages]
        if message_ids:
            Notification.query.filter(
                Notification.user_id == current_user.id,
                Notification.type == 'message',
                Notification.related_id.in_(message_ids),
                Notification.is_read == False
            ).update({'is_read': True}, synchronize_session=False)

        db.session.commit()
    
    return render_template('messages/chat.html',
                         title=f'与 {other_user.nickname or other_user.username} 的对话',
                         other_user=other_user,
                         product=product,
                         messages=messages)

@bp.route('/send', methods=['POST'])
@login_required
def send_message():
    """发送消息"""
    try:
        # 检查请求数据
        if not request.is_json:
            return jsonify({'success': False, 'message': '请求格式错误'}), 400

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请求数据为空'}), 400

        receiver_id = data.get('receiver_id')
        content = data.get('content', '').strip()
        product_id = data.get('product_id')

        # 验证参数
        if not receiver_id:
            return jsonify({'success': False, 'message': '接收者ID不能为空'}), 400
        if not content:
            return jsonify({'success': False, 'message': '消息内容不能为空'}), 400
        if len(content) > 1000:
            return jsonify({'success': False, 'message': '消息内容过长'}), 400

        # 验证接收者
        receiver = User.query.get(receiver_id)
        if not receiver:
            return jsonify({'success': False, 'message': '接收者不存在'}), 404

        # 不能给自己发消息
        if receiver_id == current_user.id:
            return jsonify({'success': False, 'message': '不能给自己发消息'}), 400

        # 验证商品（如果提供了product_id）
        if product_id:
            product = Product.query.get(product_id)
            if not product:
                product_id = None  # 商品不存在，设为None但不影响发送

        # 检查是否在短时间内发送了相同内容的消息（防重复提交）
        from datetime import timedelta
        recent_message = Message.query.filter(
            Message.sender_id == current_user.id,
            Message.receiver_id == receiver_id,
            Message.product_id == product_id,
            Message.content == content,
            Message.created_at >= local_now() - timedelta(seconds=10)  # 10秒内
        ).first()

        if recent_message:
            return jsonify({'success': False, 'message': '请不要重复发送相同的消息'}), 400

        # 创建消息
        message = Message(
            sender_id=current_user.id,
            receiver_id=receiver_id,
            product_id=product_id if product_id else None,
            content=content,
            created_at=local_now(),
            # 新消息默认对双方都可见
            deleted_by_sender=False,
            deleted_by_receiver=False
        )

        db.session.add(message)

        # 如果接收方之前删除了与发送方的对话，新消息会让对话重新出现
        # 但接收方只能看到删除时间点之后的消息
        # 这里不需要特殊处理，因为新消息的deleted_by_receiver=False会自动让对话重新出现

        # 创建通知
        notification = Notification(
            user_id=receiver_id,
            title='新消息通知',
            content=f'您收到来自 {current_user.nickname or current_user.username} 的新消息',
            type='message',
            related_id=message.id
        )
        db.session.add(notification)

        db.session.commit()

        # WebSocket实时通知已移除，改为传统模式

        return jsonify({
            'success': True,
            'message': {
                'id': message.id,
                'content': message.content,
                'created_at': message.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'sender_id': message.sender_id
            }
        })

    except Exception as e:
        db.session.rollback()
        # 记录错误日志
        current_app.logger.error(f'发送消息失败: {str(e)}')
        return jsonify({'success': False, 'message': f'发送失败: {str(e)}'}), 500

@bp.route('/api/messages/<int:user_id>')
@bp.route('/api/messages/<int:user_id>/<int:product_id>')
@login_required
def get_messages(user_id, product_id=None):
    """获取聊天记录API"""
    try:
        messages = Message.query.filter(
            or_(
                and_(Message.sender_id == current_user.id, Message.receiver_id == user_id),
                and_(Message.sender_id == user_id, Message.receiver_id == current_user.id)
            ),
            Message.product_id == product_id,
            # 只显示当前用户未删除的消息
            or_(
                and_(Message.sender_id == current_user.id, Message.deleted_by_sender == False),
                and_(Message.receiver_id == current_user.id, Message.deleted_by_receiver == False)
            )
        ).order_by(Message.created_at).all()
        
        return jsonify({
            'success': True,
            'messages': [{
                'id': msg.id,
                'content': msg.content,
                'sender_id': msg.sender_id,
                'receiver_id': msg.receiver_id,
                'created_at': msg.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'is_read': msg.is_read
            } for msg in messages]
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': '获取消息失败'}), 500

@bp.route('/api/message_info/<int:message_id>')
@login_required
def api_message_info(message_id):
    """获取消息信息API"""
    try:
        message = Message.query.filter(
            Message.id == message_id,
            or_(
                Message.sender_id == current_user.id,
                Message.receiver_id == current_user.id
            )
        ).first()

        if not message:
            return jsonify({'success': False, 'message': '消息不存在或无权限访问'}), 404

        # 返回消息的发送者ID和商品ID，用于构建聊天页面URL
        sender_id = message.sender_id if message.sender_id != current_user.id else message.receiver_id

        return jsonify({
            'success': True,
            'sender_id': sender_id,
            'product_id': message.product_id,
            'message_id': message.id
        })

    except Exception as e:
        return jsonify({'success': False, 'message': '获取消息信息失败'}), 500

@bp.route('/start_chat/<int:product_id>')
@login_required
def start_chat(product_id):
    """开始与商品卖家聊天"""
    product = Product.query.get_or_404(product_id)

    # 不能与自己聊天
    if product.seller_id == current_user.id:
        flash('不能与自己聊天', 'warning')
        return redirect(url_for('products.detail', id=product_id))

    return redirect(url_for('messages.chat', user_id=product.seller_id, product_id=product_id))

@bp.route('/api/conversations')
@login_required
def api_conversations():
    """获取对话列表API - 用于实时更新"""
    try:
        # 获取所有对话 - 按商品分组，避免重复显示
        conversations = db.session.query(
            Message.product_id,
            db.func.max(Message.created_at).label('last_message_time'),
            db.func.count(Message.id).label('message_count'),
            # 获取对话中的另一个用户ID（非当前用户）
            db.case(
                (Message.sender_id == current_user.id, Message.receiver_id),
                else_=Message.sender_id
            ).label('other_user_id')
        ).filter(
            or_(Message.sender_id == current_user.id, Message.receiver_id == current_user.id),
            # 排除当前用户已删除的对话
            or_(
                and_(Message.sender_id == current_user.id, Message.deleted_by_sender == False),
                and_(Message.receiver_id == current_user.id, Message.deleted_by_receiver == False)
            ),
            Message.product_id.isnot(None)  # 只显示有关联商品的对话
        ).group_by(
            Message.product_id,
            db.case(
                (Message.sender_id == current_user.id, Message.receiver_id),
                else_=Message.sender_id
            )
        ).order_by(desc('last_message_time')).all()

        # 构建对话列表
        conversation_list = []
        total_unread = 0

        for conv in conversations:
            # 获取对方用户信息
            other_user = User.query.get(conv.other_user_id)
            if not other_user:
                continue

            # 获取最后一条消息
            last_message = Message.query.filter(
                or_(
                    and_(Message.sender_id == current_user.id, Message.receiver_id == conv.other_user_id),
                    and_(Message.sender_id == conv.other_user_id, Message.receiver_id == current_user.id)
                ),
                Message.product_id == conv.product_id,
                # 排除当前用户已删除的消息
                or_(
                    and_(Message.sender_id == current_user.id, Message.deleted_by_sender == False),
                    and_(Message.receiver_id == current_user.id, Message.deleted_by_receiver == False)
                )
            ).order_by(desc(Message.created_at)).first()

            # 获取未读消息数
            unread_count = Message.query.filter(
                Message.sender_id == conv.other_user_id,
                Message.receiver_id == current_user.id,
                Message.product_id == conv.product_id,
                Message.is_read == False
            ).count()

            total_unread += unread_count

            # 获取相关商品
            product = Product.query.get(conv.product_id) if conv.product_id else None

            conversation_list.append({
                'other_user_id': conv.other_user_id,
                'other_user_name': other_user.nickname or other_user.username,
                'other_user_avatar': other_user.avatar,
                'product_id': conv.product_id,
                'product_title': product.title if product else '商品已删除',
                'product_image': product.get_main_image_url() if product else None,
                'last_message_content': last_message.content if last_message else '',
                'last_message_time': conv.last_message_time.strftime('%Y-%m-%d %H:%M:%S'),
                'unread_count': unread_count,
                'is_sender': last_message.sender_id == current_user.id if last_message else False
            })

        return jsonify({
            'success': True,
            'conversations': conversation_list,
            'total_count': len(conversation_list),
            'total_unread': total_unread
        })

    except Exception as e:
        current_app.logger.error(f'获取对话列表失败: {str(e)}')
        return jsonify({'success': False, 'message': '获取对话列表失败'}), 500

@bp.route('/api/mark_messages_read', methods=['POST'])
@login_required
def api_mark_messages_read():
    """标记消息为已读API"""
    try:
        user_id = request.json.get('user_id', type=int)
        product_id = request.json.get('product_id', type=int)

        if not user_id:
            return jsonify({'success': False, 'message': '用户ID不能为空'}), 400

        # 标记来自指定用户关于指定商品的所有未读消息为已读
        messages_to_mark = Message.query.filter(
            Message.sender_id == user_id,
            Message.receiver_id == current_user.id,
            Message.product_id == product_id,
            Message.is_read == False
        ).all()

        for message in messages_to_mark:
            message.is_read = True

        # 同时标记相关的消息通知为已读
        if messages_to_mark:
            message_ids = [msg.id for msg in messages_to_mark]
            Notification.query.filter(
                Notification.user_id == current_user.id,
                Notification.type == 'message',
                Notification.related_id.in_(message_ids),
                Notification.is_read == False
            ).update({'is_read': True}, synchronize_session=False)

        db.session.commit()

        return jsonify({
            'success': True,
            'marked_count': len(messages_to_mark)
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'标记消息已读失败: {str(e)}')
        return jsonify({'success': False, 'message': '标记消息已读失败'}), 500



@bp.route('/realtime_demo')
@login_required
def realtime_demo():
    """实时更新演示页面"""
    return render_template('messages/realtime_demo.html', title='消息实时更新演示')

@bp.route('/delete_test')
@login_required
def delete_test():
    """删除功能测试页面"""
    return render_template('messages/delete_test.html', title='删除对话测试')

@bp.route('/logical_delete_test')
@login_required
def logical_delete_test():
    """逻辑删除功能测试页面"""
    return render_template('messages/logical_delete_test.html', title='逻辑删除测试')

@bp.route('/api/delete_conversation', methods=['POST'])
@login_required
def api_delete_conversation():
    """删除对话API"""
    try:
        user_id = request.form.get('user_id', type=int)
        product_id = request.form.get('product_id')

        # 添加调试信息
        print(f"删除对话请求: user_id={user_id}, product_id={product_id}, current_user={current_user.id}")

        if not user_id:
            return jsonify({'success': False, 'message': '用户ID不能为空'}), 400

        # 转换product_id
        if product_id == '' or product_id == 'None':
            product_id = None
        else:
            product_id = int(product_id) if product_id else None

        print(f"处理后的参数: user_id={user_id}, product_id={product_id}")

        # 验证用户是否存在
        other_user = User.query.get(user_id)
        if not other_user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        # 查找相关的所有消息进行逻辑删除
        messages_to_update = Message.query.filter(
            or_(
                and_(Message.sender_id == current_user.id, Message.receiver_id == user_id),
                and_(Message.sender_id == user_id, Message.receiver_id == current_user.id)
            ),
            Message.product_id == product_id
        ).all()

        print(f"找到 {len(messages_to_update)} 条消息需要逻辑删除")

        if not messages_to_update:
            return jsonify({'success': False, 'message': '没有找到相关对话'}), 404

        # 对每条消息进行逻辑删除（只对当前用户删除）
        for message in messages_to_update:
            message.delete_for_user(current_user.id)

        # 删除当前用户的相关通知
        message_ids = [msg.id for msg in messages_to_update]
        if message_ids:
            notifications_to_delete = Notification.query.filter(
                Notification.user_id == current_user.id,
                Notification.type == 'message',
                Notification.related_id.in_(message_ids)
            ).all()

            for notification in notifications_to_delete:
                db.session.delete(notification)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'已删除与 {other_user.nickname or other_user.username} 的对话',
            'deleted_count': len(messages_to_update)
        })

    except Exception as e:
        db.session.rollback()
        # 记录详细错误信息
        import traceback
        error_details = traceback.format_exc()
        print(f"删除对话错误: {str(e)}")
        print(f"错误详情: {error_details}")

        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}',
            'error': str(e)
        }), 500
