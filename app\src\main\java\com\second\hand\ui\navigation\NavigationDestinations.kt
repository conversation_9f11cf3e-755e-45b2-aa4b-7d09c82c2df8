package com.second.hand.ui.navigation

/**
 * 导航目的地常量定义
 * 定义应用中所有页面的路由常量
 */
object NavigationDestinations {
    
    // 认证相关页面
    const val WELCOME = "welcome"
    const val LOGIN = "login"
    const val REGISTER = "register"
    
    // 主要页面（底部导航）
    const val HOME = "home"
    const val CATEGORY = "category"
    const val PUBLISH = "publish"
    const val MESSAGE = "message"
    const val PROFILE = "profile"
    
    // 详情页面
    const val PRODUCT_DETAIL = "product_detail"
    const val USER_PROFILE = "user_profile"
    const val ORDER_DETAIL = "order_detail"
    const val CHAT = "chat"
    
    // 功能页面
    const val SEARCH = "search"
    const val PRODUCT_LIST = "product_list"
    const val FAVORITES = "favorites"
    const val MY_PRODUCTS = "my_products"
    const val ORDERS = "orders"
    const val SETTINGS = "settings"
    const val EDIT_PROFILE = "edit_profile"

    // 调试页面
    const val AUTH_TEST = "auth_test"
    const val TOKEN_DEBUG = "token_debug"
    const val APP_STATUS = "app_status"
    const val TOKEN_REFRESH_TEST = "token_refresh_test"
    
    // 带参数的路由
    const val PRODUCT_DETAIL_WITH_ID = "product_detail/{productId}"
    const val USER_PROFILE_WITH_ID = "user_profile/{userId}"
    const val ORDER_DETAIL_WITH_ID = "order_detail/{orderId}"
    const val CHAT_WITH_USER = "chat/{userId}"
    
    // 参数键
    const val PRODUCT_ID_KEY = "productId"
    const val USER_ID_KEY = "userId"
    const val ORDER_ID_KEY = "orderId"
    
    /**
     * 构建带参数的路由
     */
    fun productDetail(productId: String) = "product_detail/$productId"
    fun userProfile(userId: String) = "user_profile/$userId"
    fun orderDetail(orderId: String) = "order_detail/$orderId"
    fun chatWithUser(userId: String) = "chat/$userId"
}
