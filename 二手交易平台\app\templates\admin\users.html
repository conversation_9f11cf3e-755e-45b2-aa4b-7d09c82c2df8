{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users me-2"></i>用户管理</h2>
        <div>
            <span class="badge bg-primary fs-6">总用户数: {{ users.total }}</span>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('admin_panel.users') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="search" 
                               value="{{ search_query or '' }}" placeholder="搜索用户名或邮箱">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="role">
                            <option value="">全部角色</option>
                            <option value="user" {{ 'selected' if role_filter == 'user' else '' }}>普通用户</option>
                            <option value="admin" {{ 'selected' if role_filter == 'admin' else '' }}>管理员</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">全部状态</option>
                            <option value="active" {{ 'selected' if status_filter == 'active' else '' }}>活跃</option>
                            <option value="inactive" {{ 'selected' if status_filter == 'inactive' else '' }}>非活跃</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>用户</th>
                            <th>邮箱</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th>注册时间</th>
                            <th>最后活跃</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users.items %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ user.get_avatar_url() }}"
                                         class="rounded-circle user-avatar user-avatar-md me-2"
                                         alt="头像">
                                    <div>
                                        <div class="fw-bold">{{ user.username }}</div>
                                        <small class="text-muted">@{{ user.username }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.role.value == 'admin' %}
                                <span class="badge bg-danger">管理员</span>
                                {% else %}
                                <span class="badge bg-primary">用户</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_active %}
                                <span class="badge bg-success">正常</span>
                                {% else %}
                                <span class="badge bg-secondary">禁用</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ user.created_at.strftime('%Y-%m-%d') }}</small>
                            </td>
                            <td>
                                <small>{{ user.last_seen.strftime('%Y-%m-%d %H:%M') if user.last_seen else '从未' }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('admin_panel.edit_user', id=user.id) }}"
                                       class="btn btn-outline-primary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if user.id != current_user.id %}
                                    <button class="btn btn-outline-warning" 
                                            onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})"
                                            title="{{ '禁用' if user.is_active else '启用' }}">
                                        <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if users.pages > 1 %}
            <nav aria-label="用户分页" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if users.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_panel.users', page=users.prev_num, search=search_query, role=role_filter, status=status_filter) }}">上一页</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in users.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != users.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_panel.users', page=page_num, search=search_query, role=role_filter, status=status_filter) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_panel.users', page=users.next_num, search=search_query, role=role_filter, status=status_filter) }}">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

{% block scripts %}
<script>
function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus ? '禁用' : '启用';
    if (confirm(`确定要${action}该用户吗？`)) {
        $.ajax({
            url: `/admin_panel/users/toggle_status/${userId}`,
            method: 'POST',
            headers: {
                'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('操作失败：' + response.message);
                }
            },
            error: function() {
                alert('操作失败，请重试');
            }
        });
    }
}
</script>
{% endblock %}
{% endblock %}
