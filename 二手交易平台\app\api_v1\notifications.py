#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端通知API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import Notification, Message
from app import db
from app.utils.jwt_utils import jwt_required
from app.utils.api_response import (
    success_response, error_response, validation_error_response,
    paginated_response, not_found_response
)
from app.utils.datetime_utils import local_now, format_relative_time
from sqlalchemy import desc


def serialize_notification(notification):
    """序列化通知数据"""
    return {
        'id': notification.id,
        'title': notification.title,
        'content': notification.content,
        'type': notification.type,
        'isRead': notification.is_read,
        'relatedId': notification.related_id,
        'createdAt': notification.created_at.isoformat() + 'Z',
        'timeAgo': format_relative_time(notification.created_at)
    }


@bp.route('/notifications', methods=['GET'])
@jwt_required
def get_notifications():
    """获取通知列表"""
    try:
        user = request.current_user
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        notification_type = request.args.get('type', 'all')
        status = request.args.get('status', 'all')
        
        # 构建查询（排除聊天消息通知）
        query = Notification.query.filter(
            Notification.user_id == user.id,
            Notification.type != 'message'
        )
        
        # 类型筛选
        if notification_type != 'all':
            query = query.filter(Notification.type == notification_type)
        
        # 状态筛选
        if status == 'unread':
            query = query.filter(Notification.is_read == False)
        elif status == 'read':
            query = query.filter(Notification.is_read == True)
        
        # 按创建时间倒序排列
        query = query.order_by(desc(Notification.created_at))
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )
        
        # 序列化数据
        notifications = [serialize_notification(notification) for notification in pagination.items]
        
        return paginated_response(notifications, pagination)
        
    except Exception as e:
        current_app.logger.error(f"获取通知列表失败: {str(e)}")
        return error_response('SYS_001', '获取通知列表失败', http_code=500)


@bp.route('/notifications/unread-count', methods=['GET'])
@jwt_required
def get_unread_count():
    """获取未读通知数量"""
    try:
        user = request.current_user
        
        # 获取未读通知数量（排除聊天消息通知）
        unread_notifications = Notification.query.filter(
            Notification.user_id == user.id,
            Notification.is_read == False,
            Notification.type != 'message'
        ).count()
        
        # 获取未读消息数量
        unread_messages = Message.query.filter(
            Message.receiver_id == user.id,
            Message.is_read == False,
            Message.deleted_by_receiver == False
        ).count()
        
        return success_response({
            'unreadNotifications': unread_notifications,
            'unreadMessages': unread_messages,
            'totalUnread': unread_notifications + unread_messages
        })
        
    except Exception as e:
        current_app.logger.error(f"获取未读通知数量失败: {str(e)}")
        return error_response('SYS_001', '获取未读通知数量失败', http_code=500)


@bp.route('/notifications/<int:notification_id>/read', methods=['PUT'])
@jwt_required
def mark_notification_read(notification_id):
    """标记通知已读"""
    try:
        user = request.current_user
        notification = Notification.query.get(notification_id)
        
        if not notification:
            return not_found_response('通知不存在')
        
        # 权限检查
        if notification.user_id != user.id:
            return error_response('AUTH_004', '无权限操作此通知', http_code=403)
        
        notification.is_read = True
        db.session.commit()
        
        return success_response(None, '通知已标记为已读')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"标记通知已读失败: {str(e)}")
        return error_response('SYS_001', '标记通知已读失败', http_code=500)


@bp.route('/notifications/read-all', methods=['PUT'])
@jwt_required
def mark_all_read():
    """标记所有通知为已读"""
    try:
        user = request.current_user
        
        # 更新所有未读通知（排除聊天消息通知）
        updated_count = Notification.query.filter(
            Notification.user_id == user.id,
            Notification.is_read == False,
            Notification.type != 'message'
        ).update({'is_read': True})
        
        db.session.commit()
        
        return success_response({
            'updatedCount': updated_count
        }, '所有通知已标记为已读')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"标记所有通知已读失败: {str(e)}")
        return error_response('SYS_001', '标记所有通知已读失败', http_code=500)


@bp.route('/notifications/latest', methods=['GET'])
@jwt_required
def get_latest_notifications():
    """获取最新通知"""
    try:
        user = request.current_user
        
        # 获取最新5条通知（排除聊天消息通知）
        notifications = Notification.query.filter(
            Notification.user_id == user.id,
            Notification.type != 'message'
        ).order_by(desc(Notification.created_at)).limit(5).all()
        
        notifications_data = [serialize_notification(notification) for notification in notifications]
        
        return success_response(notifications_data)
        
    except Exception as e:
        current_app.logger.error(f"获取最新通知失败: {str(e)}")
        return error_response('SYS_001', '获取最新通知失败', http_code=500)


@bp.route('/notifications/<int:notification_id>', methods=['DELETE'])
@jwt_required
def delete_notification(notification_id):
    """删除通知"""
    try:
        user = request.current_user
        notification = Notification.query.get(notification_id)
        
        if not notification:
            return not_found_response('通知不存在')
        
        # 权限检查
        if notification.user_id != user.id:
            return error_response('AUTH_004', '无权限删除此通知', http_code=403)
        
        db.session.delete(notification)
        db.session.commit()
        
        return success_response(None, '通知删除成功')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除通知失败: {str(e)}")
        return error_response('SYS_001', '删除通知失败', http_code=500)


@bp.route('/notifications/clear-read', methods=['DELETE'])
@jwt_required
def clear_read_notifications():
    """清空已读通知"""
    try:
        user = request.current_user
        
        # 删除所有已读通知（排除聊天消息通知）
        deleted_count = Notification.query.filter(
            Notification.user_id == user.id,
            Notification.is_read == True,
            Notification.type != 'message'
        ).delete()
        
        db.session.commit()
        
        return success_response({
            'deletedCount': deleted_count
        }, '已读通知清空成功')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"清空已读通知失败: {str(e)}")
        return error_response('SYS_001', '清空已读通知失败', http_code=500)
