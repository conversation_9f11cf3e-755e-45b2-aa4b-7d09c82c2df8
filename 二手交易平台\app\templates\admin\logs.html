{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-file-alt me-2"></i>系统日志</h2>
        <div>
            <button class="btn btn-outline-primary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button class="btn btn-outline-danger" onclick="clearLogs()">
                <i class="fas fa-trash"></i> 清空日志
            </button>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('admin_panel.logs') }}">
                <div class="row g-3">
                    <div class="col-md-2">
                        <select class="form-select" name="level">
                            <option value="">全部级别</option>
                            <option value="INFO" {{ 'selected' if level_filter == 'INFO' else '' }}>信息</option>
                            <option value="WARNING" {{ 'selected' if level_filter == 'WARNING' else '' }}>警告</option>
                            <option value="ERROR" {{ 'selected' if level_filter == 'ERROR' else '' }}>错误</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="module">
                            <option value="">全部模块</option>
                            <option value="auth" {{ 'selected' if module_filter == 'auth' else '' }}>用户认证</option>
                            <option value="products" {{ 'selected' if module_filter == 'products' else '' }}>商品管理</option>
                            <option value="orders" {{ 'selected' if module_filter == 'orders' else '' }}>订单管理</option>
                            <option value="admin" {{ 'selected' if module_filter == 'admin' else '' }}>管理后台</option>
                            <option value="system" {{ 'selected' if module_filter == 'system' else '' }}>系统</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="search" 
                               value="{{ search_query or '' }}" placeholder="搜索日志内容">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" name="date" 
                               value="{{ date_filter or '' }}">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 筛选
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 日志列表 -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th width="120">时间</th>
                            <th width="80">级别</th>
                            <th width="100">模块</th>
                            <th>消息</th>
                            <th width="100">用户</th>
                            <th width="120">IP地址</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs.items %}
                        <tr class="log-row log-{{ log.level.lower() }}">
                            <td>
                                <small>{{ log.created_at.strftime('%m-%d %H:%M:%S') }}</small>
                            </td>
                            <td>
                                {% if log.level == 'INFO' %}
                                <span class="badge bg-info">信息</span>
                                {% elif log.level == 'WARNING' %}
                                <span class="badge bg-warning">警告</span>
                                {% elif log.level == 'ERROR' %}
                                <span class="badge bg-danger">错误</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ log.module or '-' }}</small>
                            </td>
                            <td>
                                <div class="log-message" title="{{ log.message }}">
                                    {{ log.message[:100] }}{% if log.message|length > 100 %}...{% endif %}
                                </div>
                            </td>
                            <td>
                                {% if log.user_id %}
                                <small>{{ log.user_id }}</small>
                                {% else %}
                                <small class="text-muted">系统</small>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ log.ip_address or '-' }}</small>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                暂无日志记录
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if logs.pages > 1 %}
            <nav aria-label="日志分页" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if logs.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_panel.logs', page=logs.prev_num, level=level_filter, module=module_filter, search=search_query, date=date_filter) }}">上一页</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in logs.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != logs.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_panel.logs', page=page_num, level=level_filter, module=module_filter, search=search_query, date=date_filter) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if logs.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_panel.logs', page=logs.next_num, level=level_filter, module=module_filter, search=search_query, date=date_filter) }}">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<style>
.log-row.log-error {
    background-color: rgba(220, 53, 69, 0.1);
}
.log-row.log-warning {
    background-color: rgba(255, 193, 7, 0.1);
}
.log-row.log-info {
    background-color: rgba(13, 202, 240, 0.05);
}
.log-message {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}
</style>

{% block scripts %}
<script>
function refreshLogs() {
    location.reload();
}

function clearLogs() {
    if (confirm('确定要清空所有日志吗？此操作不可恢复！')) {
        // 获取CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        $.ajax({
            url: '/admin_panel/api/clear_logs',
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            },
            success: function(response) {
                if (response.success) {
                    showAlert('日志清空成功', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('清空失败：' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Clear logs error:', error);
                let errorMsg = '清空失败，请重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                showAlert(errorMsg, 'error');
            }
        });
    }
}

// 显示提示信息
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="margin-bottom: 20px; position: relative; z-index: 1050;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 优先在页面内容区域的最顶部显示提示
    const pageTransition = document.querySelector('.page-transition');
    const targetContainer = pageTransition || document.querySelector('.container-fluid') || document.querySelector('.container') || document.body;

    // 移除现有的提示（只移除我们创建的提示，不影响其他alert）
    const existingAlerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
    existingAlerts.forEach(alert => {
        if (alert.style.zIndex === '1050') {
            alert.remove();
        }
    });

    // 在目标容器最顶部插入新提示
    if (pageTransition) {
        // 如果有page-transition容器，在其第一个子元素之前插入
        const firstChild = pageTransition.firstElementChild;
        if (firstChild) {
            firstChild.insertAdjacentHTML('beforebegin', alertHtml);
        } else {
            pageTransition.insertAdjacentHTML('afterbegin', alertHtml);
        }
    } else {
        targetContainer.insertAdjacentHTML('afterbegin', alertHtml);
    }

    // 滚动到页面顶部以确保用户看到提示
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 3秒后自动消失
    setTimeout(() => {
        const alerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
        alerts.forEach(alert => {
            if (alert.style.zIndex === '1050') {
                alert.remove();
            }
        });
    }, 3000);
}

// 自动刷新（每60秒）
setInterval(function() {
    if (document.visibilityState === 'visible') {
        refreshLogs();
    }
}, 60000);
</script>
{% endblock %}
{% endblock %}
