{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-comment-dots"></i> 我的反馈</h2>
    <a href="{{ url_for('notifications.feedback') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 新建反馈
    </a>
</div>

<!-- 筛选器 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">状态筛选</label>
                <select name="status" class="form-select" onchange="this.form.submit()">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>全部</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>待处理</option>
                    <option value="processing" {% if status_filter == 'processing' %}selected{% endif %}>处理中</option>
                    <option value="resolved" {% if status_filter == 'resolved' %}selected{% endif %}>已解决</option>
                </select>
            </div>
        </form>
    </div>
</div>

<!-- 反馈列表 -->
{% if feedbacks.items %}
    {% for feedback in feedbacks.items %}
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h5 class="card-title">
                        <a href="{{ url_for('notifications.feedback_detail', id=feedback.id) }}" 
                           class="text-decoration-none">
                            {{ feedback.title }}
                        </a>
                    </h5>
                    <p class="card-text text-muted">{{ feedback.content[:100] }}{% if feedback.content|length > 100 %}...{% endif %}</p>
                    <div class="d-flex align-items-center text-muted small">
                        <span class="me-3">
                            <i class="fas fa-calendar"></i> {{ feedback.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </span>
                        <span class="me-3">
                            <i class="fas fa-tag"></i> 
                            {% if feedback.type == 'bug' %}问题反馈
                            {% elif feedback.type == 'suggestion' %}功能建议
                            {% elif feedback.type == 'complaint' %}投诉举报
                            {% elif feedback.type == 'contact' %}联系我们
                            {% else %}其他{% endif %}
                        </span>
                    </div>
                </div>
                <div class="text-end">
                    {% if feedback.status == 'pending' %}
                        <span class="badge bg-warning">待处理</span>
                    {% elif feedback.status == 'processing' %}
                        <span class="badge bg-info">处理中</span>
                    {% elif feedback.status == 'waiting_user' %}
                        <span class="badge bg-primary">等待我回复</span>
                    {% elif feedback.status == 'resolved' %}
                        <span class="badge bg-success">已解决</span>
                    {% elif feedback.status == 'closed' %}
                        <span class="badge bg-secondary">已关闭</span>
                    {% endif %}
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="mt-3 d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('notifications.feedback_detail', id=feedback.id) }}" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-eye"></i> 查看详情
                    </a>
                    {% if feedback.status not in ['resolved', 'closed'] %}
                    <a href="{{ url_for('notifications.update_feedback_status', id=feedback.id) }}" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="fas fa-edit"></i> 更新状态
                    </a>
                    {% endif %}
                </div>
                <small class="text-muted">
                    {% if feedback.updated_at != feedback.created_at %}
                        最后更新：{{ feedback.updated_at.strftime('%m-%d %H:%M') }}
                    {% endif %}
                </small>
            </div>

            {% if feedback.admin_reply %}
            <div class="mt-3 p-3 bg-light rounded">
                <h6 class="text-primary"><i class="fas fa-reply"></i> 管理员回复：</h6>
                <p class="mb-0">{{ feedback.admin_reply }}</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}

    <!-- 分页 -->
    {% if feedbacks.pages > 1 %}
    <nav aria-label="反馈分页">
        <ul class="pagination justify-content-center">
            {% if feedbacks.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('notifications.my_feedbacks', page=feedbacks.prev_num, status=status_filter) }}">上一页</a>
                </li>
            {% endif %}
            
            {% for page_num in feedbacks.iter_pages() %}
                {% if page_num %}
                    {% if page_num != feedbacks.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('notifications.my_feedbacks', page=page_num, status=status_filter) }}">{{ page_num }}</a>
                        </li>
                    {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                    {% endif %}
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if feedbacks.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('notifications.my_feedbacks', page=feedbacks.next_num, status=status_filter) }}">下一页</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-comment-slash fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">暂无反馈记录</h4>
        <p class="text-muted">您还没有提交过任何反馈</p>
        <a href="{{ url_for('notifications.feedback') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 提交反馈
        </a>
    </div>
{% endif %}
{% endblock %}