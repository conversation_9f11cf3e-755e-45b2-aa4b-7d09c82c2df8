#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知工具模块
"""

from app.models import Notification
from app import db
from app.utils.datetime_utils import local_now
from flask import current_app


def create_notification(user_id, title, content, notification_type='system', related_id=None):
    """创建通知"""
    try:
        notification = Notification(
            user_id=user_id,
            title=title,
            content=content,
            type=notification_type,
            related_id=related_id,
            is_read=False,
            created_at=local_now()
        )
        
        db.session.add(notification)
        # 注意：这里不提交事务，由调用方决定何时提交
        
        return notification
        
    except Exception as e:
        current_app.logger.error(f"创建通知失败: {str(e)}")
        return None


def create_system_notification(user_id, title, content, related_id=None):
    """创建系统通知"""
    return create_notification(user_id, title, content, 'system', related_id)


def create_order_notification(user_id, title, content, order_id):
    """创建订单通知"""
    return create_notification(user_id, title, content, 'order', order_id)


def create_product_notification(user_id, title, content, product_id):
    """创建商品通知"""
    return create_notification(user_id, title, content, 'product', product_id)


def create_message_notification(user_id, title, content, message_id):
    """创建消息通知"""
    return create_notification(user_id, title, content, 'message', message_id)


def batch_create_notifications(notifications_data):
    """批量创建通知"""
    try:
        notifications = []
        for data in notifications_data:
            notification = Notification(
                user_id=data['user_id'],
                title=data['title'],
                content=data['content'],
                type=data.get('type', 'system'),
                related_id=data.get('related_id'),
                is_read=False,
                created_at=local_now()
            )
            notifications.append(notification)
        
        db.session.add_all(notifications)
        # 注意：这里不提交事务，由调用方决定何时提交
        
        return notifications
        
    except Exception as e:
        current_app.logger.error(f"批量创建通知失败: {str(e)}")
        return []


def mark_notifications_read(user_id, notification_ids=None):
    """标记通知为已读"""
    try:
        query = Notification.query.filter(
            Notification.user_id == user_id,
            Notification.is_read == False
        )
        
        if notification_ids:
            query = query.filter(Notification.id.in_(notification_ids))
        
        updated_count = query.update({'is_read': True})
        return updated_count
        
    except Exception as e:
        current_app.logger.error(f"标记通知已读失败: {str(e)}")
        return 0


def delete_old_notifications(days=30):
    """删除旧通知"""
    try:
        from datetime import timedelta
        cutoff_date = local_now() - timedelta(days=days)
        
        deleted_count = Notification.query.filter(
            Notification.created_at < cutoff_date,
            Notification.is_read == True
        ).delete()
        
        return deleted_count
        
    except Exception as e:
        current_app.logger.error(f"删除旧通知失败: {str(e)}")
        return 0


def get_unread_count(user_id, notification_type=None):
    """获取未读通知数量"""
    try:
        query = Notification.query.filter(
            Notification.user_id == user_id,
            Notification.is_read == False
        )
        
        if notification_type:
            query = query.filter(Notification.type == notification_type)
        
        return query.count()
        
    except Exception as e:
        current_app.logger.error(f"获取未读通知数量失败: {str(e)}")
        return 0
