#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的数据库初始化脚本 - 包含迁移和初始化
"""

import os
import sys
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>

def create_database_schema():
    """创建完整的数据库结构"""
    print("正在创建数据库结构...")
    
    # 删除旧数据库
    if os.path.exists('app.db'):
        os.remove('app.db')
        print("✓ 删除旧数据库")
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    # 创建用户表
    cursor.execute('''
    CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(80) UNIQUE NOT NULL,
        email VARCHAR(120) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        nickname VARCHAR(100),
        phone VARCHAR(20),
        avatar VARCHAR(255),
        bio TEXT,
        location VARCHAR(200),
        is_active BOOLEAN DEFAULT 1,
        role VARCHAR(20) DEFAULT 'user',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_seen DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # 创建分类表
    cursor.execute('''
    CREATE TABLE categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(100),
        is_active BOOLEAN DEFAULT 1,
        sort_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # 创建商品表（包含新的数量字段）
    cursor.execute('''
    CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(200) NOT NULL,
        description TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        original_price DECIMAL(10,2),
        status VARCHAR(20) DEFAULT 'pending',
        condition VARCHAR(50),
        location VARCHAR(200),
        quantity INTEGER DEFAULT 1,
        sold_quantity INTEGER DEFAULT 0,
        seller_id INTEGER NOT NULL,
        category_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        view_count INTEGER DEFAULT 0,
        favorite_count INTEGER DEFAULT 0,
        FOREIGN KEY (seller_id) REFERENCES users (id),
        FOREIGN KEY (category_id) REFERENCES categories (id)
    )
    ''')
    
    # 创建商品图片表
    cursor.execute('''
    CREATE TABLE product_images (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        filename VARCHAR(255) NOT NULL,
        is_main BOOLEAN DEFAULT 0,
        sort_order INTEGER DEFAULT 0,
        product_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id)
    )
    ''')
    
    # 创建订单表
    cursor.execute('''
    CREATE TABLE orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        buyer_id INTEGER NOT NULL,
        seller_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity INTEGER DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(20) DEFAULT 'pending',
        shipping_address TEXT,
        contact_phone VARCHAR(20),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        paid_at DATETIME,
        shipped_at DATETIME,
        completed_at DATETIME,
        FOREIGN KEY (buyer_id) REFERENCES users (id),
        FOREIGN KEY (seller_id) REFERENCES users (id),
        FOREIGN KEY (product_id) REFERENCES products (id)
    )
    ''')
    
    # 创建评价表
    cursor.execute('''
    CREATE TABLE reviews (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        reviewer_id INTEGER NOT NULL,
        reviewee_id INTEGER NOT NULL,
        rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
        content TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (reviewer_id) REFERENCES users (id),
        FOREIGN KEY (reviewee_id) REFERENCES users (id)
    )
    ''')
    
    # 创建收藏表
    cursor.execute('''
    CREATE TABLE favorites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (product_id) REFERENCES products (id),
        UNIQUE(user_id, product_id)
    )
    ''')
    
    # 创建通知表
    cursor.execute('''
    CREATE TABLE notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        title VARCHAR(200) NOT NULL,
        content TEXT,
        type VARCHAR(50) DEFAULT 'info',
        is_read BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')
    
    # 创建反馈表
    cursor.execute('''
    CREATE TABLE feedback (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        subject VARCHAR(200) NOT NULL,
        content TEXT NOT NULL,
        contact_email VARCHAR(120),
        status VARCHAR(20) DEFAULT 'pending',
        admin_reply TEXT,
        admin_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (admin_id) REFERENCES users (id)
    )
    ''')

    # 创建邮箱验证码表
    cursor.execute('''
    CREATE TABLE email_verification_codes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email VARCHAR(120) NOT NULL,
        code VARCHAR(6) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        is_used BOOLEAN DEFAULT 0
    )
    ''')
    
    # 创建系统配置表
    cursor.execute('''
    CREATE TABLE system_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key VARCHAR(100) UNIQUE NOT NULL,
        value TEXT,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # 创建系统日志表
    cursor.execute('''
    CREATE TABLE system_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level VARCHAR(20) NOT NULL,
        message TEXT NOT NULL,
        module VARCHAR(100),
        user_id INTEGER,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')
    
    # 创建索引
    cursor.execute('CREATE INDEX idx_products_status ON products(status)')
    cursor.execute('CREATE INDEX idx_products_seller ON products(seller_id)')
    cursor.execute('CREATE INDEX idx_products_category ON products(category_id)')
    cursor.execute('CREATE INDEX idx_products_created ON products(created_at)')
    cursor.execute('CREATE INDEX idx_orders_buyer ON orders(buyer_id)')
    cursor.execute('CREATE INDEX idx_orders_seller ON orders(seller_id)')
    cursor.execute('CREATE INDEX idx_orders_status ON orders(status)')
    cursor.execute('CREATE INDEX idx_users_username ON users(username)')
    cursor.execute('CREATE INDEX idx_users_email ON users(email)')
    cursor.execute('CREATE INDEX idx_users_last_seen ON users(last_seen)')
    cursor.execute('CREATE INDEX idx_notifications_user ON notifications(user_id)')
    cursor.execute('CREATE INDEX idx_favorites_user ON favorites(user_id)')
    cursor.execute('CREATE INDEX idx_favorites_product ON favorites(product_id)')
    
    conn.commit()
    conn.close()
    print("✓ 数据库结构创建完成")

def create_sample_data():
    """创建示例数据"""
    print("正在创建示例数据...")
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    # 创建用户
    from werkzeug.security import generate_password_hash
    
    users_data = [
        ('admin', '<EMAIL>', generate_password_hash('admin123'), '系统管理员', None, None, '系统管理员账户', '陕西', True, 'admin'),
        ('user1', '<EMAIL>', generate_password_hash('123456'), '张三', '13800138001', None, '我是一个普通用户', '北京', True, 'user'),
    ]
    
    for user_data in users_data:
        cursor.execute('''
        INSERT INTO users (username, email, password_hash, nickname, phone, avatar, bio, location, is_active, role)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', user_data)
    
    # 创建分类
    categories_data = [
        ('电子产品', '手机、电脑、数码相机等电子设备', 'fas fa-laptop', True, 1),
        ('服装鞋帽', '各类服装、鞋子、帽子等', 'fas fa-tshirt', True, 2),
        ('图书文具', '教材、小说、文具用品等', 'fas fa-book', True, 3),
        ('家居用品', '家具、装饰品、生活用品等', 'fas fa-home', True, 4),
        ('运动户外', '运动器材、户外用品等', 'fas fa-running', True, 5),
        ('美妆护肤', '化妆品、护肤品等', 'fas fa-heart', True, 6),
        ('母婴用品', '婴儿用品、玩具等', 'fas fa-baby', True, 7),
        ('其他', '其他类别商品', 'fas fa-ellipsis-h', True, 8),
    ]
    
    for category_data in categories_data:
        cursor.execute('''
        INSERT INTO categories (name, description, icon, is_active, sort_order)
        VALUES (?, ?, ?, ?, ?)
        ''', category_data)
    
    conn.commit()
    conn.close()
    print("✓ 示例数据创建完成")

def main():
    """主函数"""
    print("=" * 50)
    print("二手交易平台 - 完整数据库初始化")
    print("=" * 50)
    
    try:
        create_database_schema()
        create_sample_data()
        print("\n✓ 数据库初始化完成！")
        print("✓ 管理员账户: admin / admin123")
        print("✓ 示例用户: user1 / 123456")
        return True
    except Exception as e:
        print(f"\n✗ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    main()
