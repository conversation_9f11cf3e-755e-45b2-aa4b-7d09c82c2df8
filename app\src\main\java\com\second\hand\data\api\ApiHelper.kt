package com.second.hand.data.api

import com.second.hand.data.model.BaseResponse
import retrofit2.Response
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * API调用辅助类
 * 提供统一的API调用处理方法
 */
object ApiHelper {
    
    /**
     * 安全的API调用包装器
     * 统一处理网络请求异常和响应解析
     */
    suspend fun <T> safeApiCall(
        apiCall: suspend () -> Response<BaseResponse<T>>
    ): NetworkResult<T> {
        return try {
            val response = apiCall()
            handleApiResponse(response)
        } catch (e: Exception) {
            handleApiException(e)
        }
    }
    
    /**
     * 处理API响应
     */
    private fun <T> handleApiResponse(response: Response<BaseResponse<T>>): NetworkResult<T> {
        return try {
            // 记录响应信息用于调试
            println("API Response - Code: ${response.code()}, URL: ${response.raw().request.url}")

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    println("API Response Body - Success: ${body.success}, Message: ${body.message}")
                    if (body.isSuccess()) {
                        body.data?.let { data ->
                            NetworkResult.Success(data)
                        } ?: NetworkResult.Error("响应数据为空")
                    } else {
                        val errorMsg = body.getErrorMessage()
                        val errorCode = body.getErrorCode()
                        println("API Error - Code: $errorCode, Message: $errorMsg")
                        NetworkResult.Error(errorMsg, response.code())
                    }
                } else {
                    println("API Error - Response body is null")
                    NetworkResult.Error("响应体为空", response.code())
                }
            } else {
                // 尝试解析错误响应体
                val errorBody = response.errorBody()?.string()
                println("API Error Response - Code: ${response.code()}, Body: $errorBody")

                val errorMessage = when (response.code()) {
                    400 -> "请求参数错误"
                    401 -> "未授权，请重新登录"
                    403 -> "禁止访问"
                    404 -> "请求的资源不存在"
                    500 -> "服务器内部错误"
                    502 -> "网关错误"
                    503 -> "服务不可用"
                    else -> "网络请求失败 (${response.code()})"
                }
                NetworkResult.Error(errorMessage, response.code())
            }
        } catch (e: Exception) {
            println("API Response Parse Error: ${e.message}")
            e.printStackTrace()
            NetworkResult.Error("响应解析失败: ${e.message}")
        }
    }
    
    /**
     * 处理API异常
     */
    private fun <T> handleApiException(exception: Exception): NetworkResult<T> {
        val errorMessage = when (exception) {
            is UnknownHostException -> "网络连接失败，请检查网络设置"
            is ConnectException -> "连接服务器失败，请稍后重试"
            is SocketTimeoutException -> "请求超时，请稍后重试"
            else -> "网络请求异常: ${exception.message ?: "未知错误"}"
        }
        return NetworkResult.Error(errorMessage)
    }
    
    /**
     * 处理简单API响应（无数据返回）
     */
    suspend fun safeApiCallNoData(
        apiCall: suspend () -> Response<BaseResponse<Any>>
    ): NetworkResult<Boolean> {
        return try {
            val response = apiCall()
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.isSuccess()) {
                    NetworkResult.Success(true)
                } else {
                    NetworkResult.Error(body?.getErrorMessage() ?: "请求失败")
                }
            } else {
                NetworkResult.Error("请求失败 (${response.code()})", response.code())
            }
        } catch (e: Exception) {
            handleApiException(e)
        }
    }
    
    /**
     * 处理列表API响应
     */
    suspend fun <T> safeApiCallList(
        apiCall: suspend () -> Response<BaseResponse<List<T>>>
    ): NetworkResult<List<T>> {
        return try {
            val response = apiCall()
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.isSuccess()) {
                    NetworkResult.Success(body.data ?: emptyList())
                } else {
                    NetworkResult.Error(body?.getErrorMessage() ?: "请求失败")
                }
            } else {
                NetworkResult.Error("请求失败 (${response.code()})", response.code())
            }
        } catch (e: Exception) {
            handleApiException(e)
        }
    }
}
