{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-box me-2"></i>商品管理</h2>
        <div>
            <span class="badge bg-primary fs-6">总商品数: {{ products.total }}</span>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('admin_panel.products') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="search" 
                               value="{{ search_query or '' }}" placeholder="搜索商品标题">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">全部状态</option>
                            <option value="active" {{ 'selected' if status_filter == 'active' else '' }}>在售</option>
                            <option value="inactive" {{ 'selected' if status_filter == 'inactive' else '' }}>已下架</option>
                            <option value="pending" {{ 'selected' if status_filter == 'pending' else '' }}>待审核</option>
                            <option value="sold" {{ 'selected' if status_filter == 'sold' else '' }}>已售出</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="category">
                            <option value="">全部分类</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {{ 'selected' if category_filter == category.id|string else '' }}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 商品列表 -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>商品</th>
                            <th>卖家</th>
                            <th>分类</th>
                            <th>价格</th>
                            <th>状态</th>
                            <th>发布时间</th>
                            <th>浏览量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products.items %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ product.get_main_image_url() }}"
                                         class="rounded me-2"
                                         width="50" height="50" alt="商品图片" style="object-fit: cover;">
                                    <div>
                                        <div class="fw-bold">{{ product.title[:30] }}{% if product.title|length > 30 %}...{% endif %}</div>
                                        <small class="text-muted">ID: {{ product.id }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ product.seller.get_avatar_url() }}" 
                                         class="rounded-circle me-2" 
                                         width="24" height="24" alt="头像">
                                    {{ product.seller.username }}
                                </div>
                            </td>
                            <td>{{ product.category.name if product.category else '无分类' }}</td>
                            <td class="text-danger fw-bold">¥{{ "%.2f"|format(product.price) }}</td>
                            <td>
                                {% if product.status.value == 'pending' %}
                                <span class="badge bg-warning">待审核</span>
                                {% elif product.status.value == 'active' %}
                                <span class="badge bg-success">在售</span>
                                {% elif product.status.value == 'sold' %}
                                <span class="badge bg-primary">已售出</span>
                                {% elif product.status.value == 'inactive' %}
                                <span class="badge bg-secondary">已下架</span>
                                {% else %}
                                <span class="badge bg-danger">未知状态</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ product.created_at.strftime('%Y-%m-%d') }}</small>
                            </td>
                            <td>{{ product.view_count }}</td>
                            <td>
                                <div class="admin-action-buttons">
                                    <!-- 第一个按钮：查看 -->
                                    <a href="{{ url_for('products.detail', id=product.id) }}"
                                       class="btn btn-sm btn-outline-primary view-btn me-1"
                                       title="查看商品详情"
                                       target="_blank"
                                       style="z-index: 10; position: relative;">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <!-- 第二个按钮：状态编辑 -->
                                    <button type="button"
                                            class="btn btn-sm btn-outline-warning status-btn me-1"
                                            data-product-id="{{ product.id }}"
                                            data-current-status="{{ product.status.value if product.status else 'active' }}"
                                            title="编辑商品状态"
                                            style="z-index: 10; position: relative;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <!-- 第三个按钮：删除 -->
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger delete-btn"
                                            data-product-id="{{ product.id }}"
                                            title="删除商品"
                                            style="z-index: 10; position: relative;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if products.pages > 1 %}
            <nav aria-label="商品分页" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if products.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_panel.products', page=products.prev_num, search=search_query, status=status_filter, category=category_filter) }}">上一页</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in products.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != products.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_panel.products', page=page_num, search=search_query, status=status_filter, category=category_filter) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_panel.products', page=products.next_num, search=search_query, status=status_filter, category=category_filter) }}">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

{% block scripts %}
<script>
// 页面加载完成后的初始化
$(document).ready(function() {
    console.log('Admin products page loaded');
    console.log('jQuery version:', $.fn.jquery);
    console.log('CSRF token:', $('meta[name=csrf-token]').attr('content'));

    // 检查用户权限
    console.log('Current user is admin: true');

    // 简化的按钮事件绑定
    console.log('Binding button events...');

    // 状态编辑按钮
    $(document).on('click', '.status-btn', function(e) {
        e.preventDefault();
        console.log('Status button clicked!');
        const productId = $(this).data('product-id');
        const currentStatus = $(this).data('current-status') || 'active';
        console.log('Product ID:', productId, 'Current Status:', currentStatus);

        if (productId) {
            showStatusModal(productId, currentStatus);
        } else {
            alert('错误：无法获取商品ID');
        }
    });

    // 删除按钮
    $(document).on('click', '.delete-btn', function(e) {
        e.preventDefault();
        console.log('Delete button clicked!');
        const productId = $(this).data('product-id');
        console.log('Product ID:', productId);

        if (productId) {
            deleteProduct(productId);
        } else {
            alert('错误：无法获取商品ID');
        }
    });

    console.log('Button events bound successfully');
});

function toggleProductStatus(productId, currentStatus) {
    // 显示状态选择模态框
    showStatusModal(productId, currentStatus);
}

function showStatusModal(productId, currentStatus) {
    const statusOptions = {
        'active': '在售',
        'inactive': '下架',
        'pending': '待审核',
        'sold': '已售出'
    };

    let modalHtml = `
        <div class="modal fade" id="statusModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">更改商品状态</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>当前状态：<span class="badge bg-secondary">${statusOptions[currentStatus]}</span></p>
                        <div class="mb-3">
                            <label class="form-label">选择新状态：</label>
                            <select class="form-select" id="newStatus">
    `;

    for (const [value, text] of Object.entries(statusOptions)) {
        if (value !== currentStatus) {
            modalHtml += `<option value="${value}">${text}</option>`;
        }
    }

    modalHtml += `
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注（可选）：</label>
                            <textarea class="form-control" id="statusNote" rows="3" placeholder="请输入状态变更说明..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="updateProductStatus(${productId})">确认更新</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#statusModal').remove();

    // 添加新模态框
    $('body').append(modalHtml);

    // 显示模态框
    $('#statusModal').modal('show');
}

function updateProductStatus(productId) {
    const newStatus = document.getElementById('newStatus').value;
    const note = document.getElementById('statusNote').value;

    if (!newStatus) {
        alert('请选择新状态');
        return;
    }

    $.ajax({
        url: `/admin_panel/products/toggle_status/${productId}`,
        method: 'POST',
        data: {
            status: newStatus,
            note: note,
            csrf_token: $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // 隐藏模态框
                $('#statusModal').modal('hide');
                // 显示成功消息
                showAlert(response.message, 'success');
                // 延迟刷新页面以显示消息
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('操作失败：' + response.message, 'error');
            }
        },
        error: function(xhr) {
            console.error('Toggle status error:', xhr);
            let errorMsg = '操作失败，请重试';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                showAlert(errorMsg, 'error');
            }
        });
}

// 显示提示消息的函数
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="margin-bottom: 20px; position: relative; z-index: 1050;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 优先在页面内容区域的最顶部显示提示
    const pageTransition = document.querySelector('.page-transition');
    const targetContainer = pageTransition || document.querySelector('.container-fluid') || document.querySelector('.container') || document.body;

    // 移除现有的提示（只移除我们创建的提示，不影响其他alert）
    const existingAlerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
    existingAlerts.forEach(alert => {
        if (alert.style.zIndex === '1050') {
            alert.remove();
        }
    });

    // 在目标容器最顶部插入新提示
    if (pageTransition) {
        // 如果有page-transition容器，在其第一个子元素之前插入
        const firstChild = pageTransition.firstElementChild;
        if (firstChild) {
            firstChild.insertAdjacentHTML('beforebegin', alertHtml);
        } else {
            pageTransition.insertAdjacentHTML('afterbegin', alertHtml);
        }
    } else {
        targetContainer.insertAdjacentHTML('afterbegin', alertHtml);
    }

    // 滚动到页面顶部以确保用户看到提示
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 3秒后自动消失
    setTimeout(() => {
        const alerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
        alerts.forEach(alert => {
            if (alert.style.zIndex === '1050') {
                alert.remove();
            }
        });
    }, 3000);
}

function deleteProduct(productId) {
    console.log('deleteProduct called:', productId);
    if (confirm('确定要删除这个商品吗？此操作不可撤销！')) {
        console.log('User confirmed deletion');
        $.ajax({
            url: `/admin_panel/products/delete/${productId}`,
            method: 'POST',
            data: {
                csrf_token: $('meta[name=csrf-token]').attr('content')
            },
            beforeSend: function() {
                console.log('Delete AJAX request started');
            },
            success: function(response) {
                console.log('Delete AJAX success:', response);
                if (response.success) {
                    showAlert(response.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('删除失败：' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Delete product error:', xhr, status, error);
                console.error('Response text:', xhr.responseText);
                let errorMsg = '删除失败，请重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                showAlert(errorMsg, 'error');
            }
        });
    }
}

// toggleFeatured函数已移除，现在只使用三个按钮：查看、状态编辑、删除
</script>
{% endblock %}
{% endblock %}
