{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-shopping-cart"></i> 创建订单</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.quantity.label(class="form-label") }}
                        {{ form.quantity(class="form-control" + (" is-invalid" if form.quantity.errors else ""), style="width: 100px;") }}
                        {% if form.quantity.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.quantity.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.shipping_name.label(class="form-label") }}
                        {{ form.shipping_name(class="form-control" + (" is-invalid" if form.shipping_name.errors else "")) }}
                        {% if form.shipping_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.shipping_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.shipping_phone.label(class="form-label") }}
                        {{ form.shipping_phone(class="form-control" + (" is-invalid" if form.shipping_phone.errors else "")) }}
                        {% if form.shipping_phone.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.shipping_phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.shipping_address.label(class="form-label") }}
                        {{ form.shipping_address(class="form-control" + (" is-invalid" if form.shipping_address.errors else ""), rows="3") }}
                        {% if form.shipping_address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.shipping_address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.buyer_note.label(class="form-label") }}
                        {{ form.buyer_note(class="form-control" + (" is-invalid" if form.buyer_note.errors else ""), rows="3") }}
                        {% if form.buyer_note.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.buyer_note.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">可选，向卖家说明特殊要求</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products.detail', id=product.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回商品
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>订单信息</h5>
            </div>
            <div class="card-body">
                <div class="d-flex mb-3">
                    <img src="{{ product.get_main_image_url() }}"
                         class="img-thumbnail me-3" style="width: 80px; height: 80px; object-fit: cover;">
                    <div>
                        <h6 class="card-title">{{ product.title }}</h6>
                        <p class="text-muted mb-1">卖家：{{ product.seller.nickname or product.seller.username }}</p>
                        <p class="text-danger fw-bold mb-0">¥{{ "%.2f"|format(product.price) }}</p>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>单价：</span>
                    <span>¥{{ "%.2f"|format(product.price) }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>数量：</span>
                    <span id="quantity-display">1</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>运费：</span>
                    <span class="text-muted">免运费</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>总计：</strong>
                    <strong class="text-danger" id="total-amount">¥{{ "%.2f"|format(product.price) }}</strong>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('quantity');
    const quantityDisplay = document.getElementById('quantity-display');
    const totalAmount = document.getElementById('total-amount');
    const unitPrice = {{ product.price }};
    
    if (quantityInput) {
        quantityInput.addEventListener('input', function() {
            const quantity = parseInt(this.value) || 1;
            quantityDisplay.textContent = quantity;
            totalAmount.textContent = '¥' + (unitPrice * quantity).toFixed(2);
        });
    }
});
</script>
{% endblock %}