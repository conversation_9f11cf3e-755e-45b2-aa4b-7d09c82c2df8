# 二手交易平台

一个基于Flask的现代化二手商品交易平台，提供安全、便捷的交易体验。

## 功能特性

### 用户管理模块
- ✅ 用户注册与登录（支持邮箱、手机号）
- ✅ 个人信息管理（头像、昵称、联系方式）
- ✅ 密码修改功能
- ✅ 权限管理（普通用户、管理员）

### 商品管理模块
- ✅ 商品发布（多图上传、分类管理）
- ✅ 商品浏览与搜索（关键词、分类、价格筛选）
- ✅ 商品详情展示
- ✅ 商品收藏功能
- ✅ 商品状态管理

### 交易管理模块
- ✅ 订单创建与管理
- ✅ 订单状态跟踪
- ✅ 买卖双方沟通（消息系统）
- ✅ 交易评价系统

### 管理员后台模块
- ✅ 用户管理（查看、编辑、禁用）
- ✅ 商品审核管理
- ✅ 订单监控
- ✅ 分类管理
- ✅ 系统配置
- ✅ 数据统计分析

### 消息通知与反馈模块
- ✅ 系统通知
- ✅ 用户反馈系统
- ✅ 消息中心

### 数据统计与分析模块
- ✅ 交易数据统计
- ✅ 用户行为分析
- ✅ 数据可视化

## 技术栈

- **后端**: Python 3.8.10 + Flask 2.0.1
- **数据库**: SQLite
- **前端**: Bootstrap 5 + jQuery
- **图标**: Font Awesome 6
- **其他**: Flask-SQLAlchemy, Flask-Login, Flask-WTF, Flask-Mail等

## 安装与运行

### 1. 环境要求
- Python 3.8.10+
- pip

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境变量
复制 `.env` 文件并修改相关配置：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置以下配置：
- SECRET_KEY: 应用密钥
- DATABASE_URL: 数据库连接字符串
- MAIL_*: 邮件服务配置

### 4. 初始化数据库
```bash
python init_db.py init
```

### 5. 创建示例数据（可选）
```bash
python init_db.py sample
```

### 6. 启动应用
```bash
python run.py
```

应用将在 http://localhost:5000 启动

## 默认账户

### 管理员账户
- 用户名: `admin`
- 密码: `admin123`

### 示例用户账户
- 用户名: `user1`, `user2`, `user3`
- 密码: `123456`

## 项目结构

```
二手交易平台/
├── app/                    # 应用主目录
│   ├── admin_panel/        # 管理员后台模块
│   ├── analytics/          # 数据分析模块
│   ├── api/               # API接口模块
│   ├── auth/              # 用户认证模块
│   ├── main/              # 主页面模块
│   ├── notifications/     # 通知反馈模块
│   ├── orders/            # 订单管理模块
│   ├── products/          # 商品管理模块
│   ├── static/            # 静态文件
│   │   ├── css/           # 样式文件
│   │   ├── js/            # JavaScript文件
│   │   ├── images/        # 图片文件
│   │   └── uploads/       # 上传文件
│   ├── templates/         # 模板文件
│   └── models.py          # 数据模型
├── config.py              # 配置文件
├── app.py                 # 应用工厂
├── run.py                 # 启动脚本
├── init_db.py             # 数据库初始化脚本
├── requirements.txt       # 依赖包列表
└── README.md              # 项目说明
```

## 主要功能截图

### 首页
- 商品轮播展示
- 分类导航
- 热门商品推荐
- 最新商品列表

### 商品详情
- 商品图片轮播
- 详细信息展示
- 卖家信息
- 相关商品推荐

### 用户中心
- 个人资料管理
- 我的商品
- 我的订单
- 我的收藏

### 管理后台
- 数据统计仪表板
- 用户管理
- 商品审核
- 订单监控

## 开发说明

### 数据库迁移
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### 重置数据库
```bash
python init_db.py reset
```

### 运行测试
```bash
python -m pytest tests/
```

## 部署说明

### 生产环境配置
1. 设置环境变量 `FLASK_ENV=production`
2. 配置真实的数据库连接
3. 设置强密码的SECRET_KEY
4. 配置邮件服务
5. 设置反向代理（如Nginx）

### Docker部署
```bash
docker build -t secondhand-platform .
docker run -p 5000:5000 secondhand-platform
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的用户管理系统
- 商品发布与交易功能
- 管理员后台
- 数据统计分析

## 致谢

感谢所有为这个项目做出贡献的开发者和用户！
