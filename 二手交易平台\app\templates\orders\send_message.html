{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-comment"></i> 联系卖家</h3>
            </div>
            <div class="card-body">
                <!-- 商品信息 -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <img src="{{ product.get_main_image_url() }}"
                                     class="img-fluid rounded" alt="{{ product.title }}"
                                     style="height: 150px; object-fit: cover;">
                            </div>
                            <div class="col-md-9">
                                <h5 class="card-title">{{ product.title }}</h5>
                                <p class="text-muted">{{ product.description[:100] }}{% if product.description|length > 100 %}...{% endif %}</p>
                                <p class="text-danger fw-bold">¥{{ "%.2f"|format(product.price) }}</p>
                                <p class="text-muted">
                                    <i class="fas fa-user"></i> 卖家：{{ product.seller.nickname or product.seller.username }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发送消息表单 -->
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}
                        {{ form.content(class="form-control" + (" is-invalid" if form.content.errors else ""), rows="6", placeholder="请输入您想对卖家说的话...") }}
                        {% if form.content.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.content.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">您可以询问商品详情、交易方式等问题</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products.detail', id=product.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回商品
                        </a>
                        <div>
                            <a href="{{ url_for('orders.create', product_id=product.id) }}" class="btn btn-success me-2">
                                <i class="fas fa-shopping-cart"></i> 立即购买
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 历史消息 -->
        {% if messages %}
        <div class="card mt-4" id="messages">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> 聊天记录</h5>
            </div>
            <div class="card-body">
                <div class="message-list" style="max-height: 400px; overflow-y: auto;">
                    {% for message in messages %}
                    <div class="message-item mb-3 {% if message.sender_id == current_user.id %}sent{% else %}received{% endif %}">
                        <div class="d-flex {% if message.sender_id == current_user.id %}justify-content-end{% endif %}">
                            <div class="message-bubble p-3 rounded {% if message.sender_id == current_user.id %}bg-primary text-white{% else %}bg-light{% endif %}" style="max-width: 70%;">
                                <div class="message-content">{{ message.content|nl2br|safe }}</div>
                                <div class="message-time text-{% if message.sender_id == current_user.id %}light{% else %}muted{% endif %} small mt-1">
                                    <i class="fas fa-user"></i> {{ message.sender.nickname or message.sender.username }}
                                    <span class="ms-2">{{ message.created_at.strftime('%m-%d %H:%M') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
.message-item.sent .message-bubble {
    margin-left: auto;
}
.message-item.received .message-bubble {
    margin-right: auto;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 如果URL包含锚点，滚动到对应位置
    if (window.location.hash === '#messages') {
        const messagesElement = document.getElementById('messages');
        if (messagesElement) {
            messagesElement.scrollIntoView({ behavior: 'smooth' });

            // 滚动到消息列表底部
            const messageList = messagesElement.querySelector('.message-list');
            if (messageList) {
                setTimeout(() => {
                    messageList.scrollTop = messageList.scrollHeight;
                }, 500);
            }
        }
    }

    // 自动滚动消息列表到底部
    const messageList = document.querySelector('.message-list');
    if (messageList) {
        messageList.scrollTop = messageList.scrollHeight;
    }

    // 表单提交时防止重复提交
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('input[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.value = '发送中...';

                // 5秒后重新启用按钮（防止网络问题导致按钮永久禁用）
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.value = '发送消息';
                }, 5000);
            }
        });
    }
});
</script>
{% endblock %}