{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0"><i class="fas fa-envelope"></i> 联系我们</h3>
            </div>
            <div class="card-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.subject.label(class="form-label") }}
                        {{ form.subject(class="form-select" + (" is-invalid" if form.subject.errors else "")) }}
                        {% if form.subject.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.subject.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.message.label(class="form-label") }}
                        {{ form.message(class="form-control" + (" is-invalid" if form.message.errors else ""), rows="5", placeholder="请输入您的留言内容...") }}
                        {% if form.message.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.message.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="text-end">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> 联系信息</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6><i class="fas fa-envelope text-primary"></i> 邮箱</h6>
                    <p class="text-muted"><EMAIL></p>
                </div>
                
                <div class="mb-4">
                    <h6><i class="fas fa-phone text-success"></i> 电话</h6>
                    <p class="text-muted">************</p>
                </div>
                
                <div class="mb-4">
                    <h6><i class="fas fa-clock text-info"></i> 工作时间</h6>
                    <p class="text-muted">
                        周一至周五: 9:00 - 18:00<br>
                        周六至周日: 10:00 - 16:00
                    </p>
                </div>
                
                <div class="mb-4">
                    <h6><i class="fas fa-map-marker-alt text-danger"></i> 地址</h6>
                    <p class="text-muted">
                        北京市朝阳区<br>
                        科技园区123号<br>
                        创新大厦8层
                    </p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-question-circle"></i> 常见问题</h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                如何发布商品？
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                注册登录后，点击"发布商品"按钮，填写商品信息并上传图片即可。
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                交易安全吗？
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                我们提供平台担保交易，确保买卖双方的权益得到保障。
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                如何联系卖家？
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                在商品详情页点击"联系卖家"按钮，可以发送站内消息与卖家沟通。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
