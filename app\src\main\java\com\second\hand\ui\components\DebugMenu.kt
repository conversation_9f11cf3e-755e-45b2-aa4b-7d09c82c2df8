package com.second.hand.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.second.hand.ui.navigation.NavigationDestinations

/**
 * 调试菜单组件
 * 提供快速访问调试功能的入口
 */
@Composable
fun DebugMenu(
    navController: NavController,
    modifier: Modifier = Modifier,
    isExpanded: Boolean = false,
    onToggle: () -> Unit = {}
) {
    var expanded by remember { mutableStateOf(isExpanded) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        Icons.Default.Settings,
                        contentDescription = "调试",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Text(
                        text = "🔧 调试功能",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                TextButton(
                    onClick = { 
                        expanded = !expanded
                        onToggle()
                    }
                ) {
                    Text(
                        text = if (expanded) "收起" else "展开",
                        style = MaterialTheme.typography.labelSmall
                    )
                }
            }
            
            // 调试功能按钮
            if (expanded) {
                Spacer(modifier = Modifier.height(12.dp))
                
                // 第一行按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = {
                            navController.navigate(NavigationDestinations.APP_STATUS)
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            Icons.Default.Settings,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "应用状态",
                            style = MaterialTheme.typography.labelSmall
                        )
                    }

                    OutlinedButton(
                        onClick = {
                            navController.navigate(NavigationDestinations.AUTH_TEST)
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            Icons.Default.Settings,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "登录测试",
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 第二行按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = {
                            navController.navigate(NavigationDestinations.TOKEN_DEBUG)
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            Icons.Default.Settings,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "Token监控",
                            style = MaterialTheme.typography.labelSmall
                        )
                    }

                    // 占位按钮
                    Spacer(modifier = Modifier.weight(1f))
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "💡 提示：这些是开发调试工具，用于测试登录注册和Token管理功能",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 简化的调试按钮
 */
@Composable
fun DebugButton(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 调试按钮
        OutlinedButton(
            onClick = { showMenu = !showMenu },
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.primary
            )
        ) {
            Icon(
                Icons.Default.Settings,
                contentDescription = "调试功能",
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "调试功能",
                style = MaterialTheme.typography.labelSmall
            )
        }
        
        // 调试菜单
        if (showMenu) {
            Spacer(modifier = Modifier.height(8.dp))
            DebugMenu(
                navController = navController,
                isExpanded = true,
                onToggle = { showMenu = false }
            )
        }
    }
}

/**
 * 浮动调试按钮
 */
@Composable
fun FloatingDebugButton(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    var showOptions by remember { mutableStateOf(false) }
    
    Box(modifier = modifier) {
        FloatingActionButton(
            onClick = { showOptions = !showOptions },
            containerColor = MaterialTheme.colorScheme.secondaryContainer,
            contentColor = MaterialTheme.colorScheme.onSecondaryContainer
        ) {
            Icon(
                Icons.Default.Settings,
                contentDescription = "调试功能"
            )
        }
        
        if (showOptions) {
            Card(
                modifier = Modifier
                    .offset(x = (-120).dp, y = (-80).dp)
                    .width(200.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    TextButton(
                        onClick = { 
                            navController.navigate(NavigationDestinations.AUTH_TEST)
                            showOptions = false
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("登录注册测试")
                    }
                    
                    TextButton(
                        onClick = { 
                            navController.navigate(NavigationDestinations.TOKEN_DEBUG)
                            showOptions = false
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Token状态监控")
                    }
                }
            }
        }
    }
}
