{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3><i class="fas fa-comment-dots"></i> 反馈详情</h3>
                {% if feedback.status == 'pending' %}
                    <span class="badge bg-warning">待处理</span>
                {% elif feedback.status == 'processing' %}
                    <span class="badge bg-info">处理中</span>
                {% elif feedback.status == 'waiting_user' %}
                    <span class="badge bg-primary">等待我回复</span>
                {% elif feedback.status == 'resolved' %}
                    <span class="badge bg-success">已解决</span>
                {% elif feedback.status == 'closed' %}
                    <span class="badge bg-secondary">已关闭</span>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h4>{{ feedback.title }}</h4>
                    <div class="text-muted small mb-3">
                        <span class="me-3">
                            <i class="fas fa-calendar"></i> {{ feedback.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </span>
                        <span class="me-3">
                            <i class="fas fa-tag"></i> 
                            {% if feedback.type == 'bug' %}问题反馈
                            {% elif feedback.type == 'suggestion' %}功能建议
                            {% elif feedback.type == 'complaint' %}投诉举报
                            {% elif feedback.type == 'contact' %}联系我们
                            {% else %}其他{% endif %}
                        </span>
                        <span>
                            <i class="fas fa-user"></i> {{ feedback.user.nickname or feedback.user.username }}
                        </span>
                    </div>
                    <div class="border-start border-primary border-3 ps-3">
                        <p class="mb-0">{{ feedback.content|nl2br|safe }}</p>
                    </div>
                </div>

                {% if feedback.admin_reply %}
                <div class="mt-4 p-4 bg-light rounded">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-reply"></i> 管理员回复
                    </h5>
                    <p class="mb-2">{{ feedback.admin_reply|nl2br|safe }}</p>
                    {% if feedback.admin %}
                    <div class="text-muted small">
                        <i class="fas fa-user-shield"></i> {{ feedback.admin.nickname or feedback.admin.username }}
                        <span class="ms-2">
                            <i class="fas fa-clock"></i> {{ feedback.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </span>
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 管理员还未回复，请耐心等待
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('notifications.my_feedbacks') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    <div>
                        {% if feedback.status not in ['resolved', 'closed'] %}
                        <a href="{{ url_for('notifications.update_feedback_status', id=feedback.id) }}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-edit"></i> 更新状态
                        </a>
                        {% endif %}
                        {% if feedback.status != 'resolved' %}
                        <a href="{{ url_for('notifications.feedback') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新建反馈
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- 反馈状态信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> 反馈状态</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-5"><strong>当前状态:</strong></div>
                    <div class="col-7">
                        {% if feedback.status == 'pending' %}
                        <span class="badge bg-warning">待处理</span>
                        {% elif feedback.status == 'processing' %}
                        <span class="badge bg-info">处理中</span>
                        {% elif feedback.status == 'waiting_user' %}
                        <span class="badge bg-primary">等待我回复</span>
                        {% elif feedback.status == 'resolved' %}
                        <span class="badge bg-success">已解决</span>
                        {% elif feedback.status == 'closed' %}
                        <span class="badge bg-secondary">已关闭</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-5"><strong>反馈类型:</strong></div>
                    <div class="col-7">
                        {% if feedback.type == 'bug' %}
                        <span class="badge bg-danger">问题反馈</span>
                        {% elif feedback.type == 'suggestion' %}
                        <span class="badge bg-info">功能建议</span>
                        {% elif feedback.type == 'complaint' %}
                        <span class="badge bg-warning">投诉举报</span>
                        {% elif feedback.type == 'contact' %}
                        <span class="badge bg-primary">联系我们</span>
                        {% else %}
                        <span class="badge bg-secondary">其他</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-5"><strong>提交时间:</strong></div>
                    <div class="col-7">{{ feedback.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>

                {% if feedback.updated_at != feedback.created_at %}
                <div class="row mb-3">
                    <div class="col-5"><strong>更新时间:</strong></div>
                    <div class="col-7">{{ feedback.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                {% endif %}

                {% if feedback.admin_reply %}
                <div class="row">
                    <div class="col-5"><strong>回复时间:</strong></div>
                    <div class="col-7">{{ feedback.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 处理进度 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-tasks"></i> 处理进度</h6>
            </div>
            <div class="card-body">
                <div class="progress-steps">
                    <div class="step {{ 'completed' if feedback.status in ['pending', 'processing', 'waiting_user', 'resolved', 'closed'] else '' }}">
                        <div class="step-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="step-content">
                            <h6>已提交</h6>
                            <small class="text-muted">{{ feedback.created_at.strftime('%m-%d %H:%M') }}</small>
                        </div>
                    </div>

                    <div class="step {{ 'completed' if feedback.status in ['processing', 'waiting_user', 'resolved', 'closed'] else '' }}">
                        <div class="step-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="step-content">
                            <h6>处理中</h6>
                            {% if feedback.status in ['processing', 'waiting_user', 'resolved', 'closed'] %}
                            <small class="text-muted">{{ feedback.updated_at.strftime('%m-%d %H:%M') }}</small>
                            {% else %}
                            <small class="text-muted">等待处理</small>
                            {% endif %}
                        </div>
                    </div>

                    {% if feedback.status == 'waiting_user' %}
                    <div class="step completed">
                        <div class="step-icon">
                            <i class="fas fa-reply"></i>
                        </div>
                        <div class="step-content">
                            <h6>等待回复</h6>
                            <small class="text-muted">{{ feedback.updated_at.strftime('%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                    {% endif %}

                    <div class="step {{ 'completed' if feedback.status in ['resolved', 'closed'] else '' }}">
                        <div class="step-icon">
                            {% if feedback.status == 'closed' %}
                            <i class="fas fa-times"></i>
                            {% else %}
                            <i class="fas fa-check"></i>
                            {% endif %}
                        </div>
                        <div class="step-content">
                            {% if feedback.status == 'closed' %}
                            <h6>已关闭</h6>
                            {% else %}
                            <h6>已解决</h6>
                            {% endif %}
                            {% if feedback.status in ['resolved', 'closed'] %}
                            <small class="text-muted">{{ feedback.updated_at.strftime('%m-%d %H:%M') }}</small>
                            {% else %}
                            <small class="text-muted">等待完成</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.progress-steps {
    position: relative;
}

.step {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 35px;
    width: 2px;
    height: 20px;
    background-color: #dee2e6;
}

.step.completed:not(:last-child)::after {
    background-color: #28a745;
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #6c757d;
    font-size: 12px;
}

.step.completed .step-icon {
    background-color: #28a745;
    color: white;
}

.step-content h6 {
    margin: 0;
    font-size: 14px;
}

.step-content small {
    font-size: 12px;
}
</style>
{% endblock %}