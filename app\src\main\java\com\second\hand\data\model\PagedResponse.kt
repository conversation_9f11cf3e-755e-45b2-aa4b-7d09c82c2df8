package com.second.hand.data.model

import com.google.gson.annotations.SerializedName

/**
 * 分页响应数据模型
 * 用于处理分页数据的API响应
 */
data class PagedResponse<T>(
    @SerializedName("items")
    val items: List<T> = emptyList(),
    
    @SerializedName("total")
    val total: Int = 0,
    
    @SerializedName("page")
    val page: Int = 1,
    
    @SerializedName("per_page")
    val perPage: Int = 10,
    
    @SerializedName("pages")
    val pages: Int = 0,
    
    @SerializedName("has_prev")
    val hasPrev: Boolean = false,
    
    @SerializedName("has_next")
    val hasNext: Boolean = false,
    
    @SerializedName("prev_num")
    val prevNum: Int? = null,
    
    @SerializedName("next_num")
    val nextNum: Int? = null
) {
    /**
     * 检查是否有更多数据
     */
    fun hasMoreData(): Boolean = hasNext
    
    /**
     * 获取下一页页码
     */
    fun getNextPage(): Int? = nextNum
    
    /**
     * 检查是否为空数据
     */
    fun isEmpty(): Boolean = items.isEmpty()
    
    /**
     * 获取数据大小
     */
    fun size(): Int = items.size
}
