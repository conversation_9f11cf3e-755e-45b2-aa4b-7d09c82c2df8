 #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二手交易平台主应用文件
"""

import os
import sys
import signal
import atexit
import argparse
import threading
import time

# 确保上传目录存在
def create_directories():
    from app.utils.file_utils import create_upload_directories
    create_upload_directories()

# 初始化数据库
def init_database():
    # 检查数据库是否存在且结构完整
    need_init = False

    if not os.path.exists('app.db'):
        need_init = True
        print("数据库不存在，需要初始化...")
    else:
        # 检查数据库结构是否完整
        try:
            import sqlite3
            conn = sqlite3.connect('app.db')
            cursor = conn.cursor()

            # 检查关键表是否存在
            required_tables = ['users', 'products', 'categories', 'orders', 'system_logs']
            for table in required_tables:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if not cursor.fetchone():
                    need_init = True
                    break

            # 检查products表是否有新的字段
            if not need_init:
                cursor.execute("PRAGMA table_info(products)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'quantity' not in columns or 'sold_quantity' not in columns:
                    need_init = True

            conn.close()
        except Exception as e:
            need_init = True

    if need_init:
        print("正在初始化数据库...")
        try:
            from init_complete_db import main as init_complete_database
            if init_complete_database():
                print("✓ 数据库初始化完成")
                return True
            else:
                print("✗ 数据库初始化失败")
                return False
        except Exception as e:
            print(f"✗ 数据库初始化失败: {e}")
            return False
    else:
        print("✓ 数据库已存在且结构完整")
        return True

def signal_handler(signum, frame):
    sys.exit(0)

if __name__ == '__main__':
    # 解析命令行参数


    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    print("=" * 50)

    print("=" * 50)

    # 创建目录
    create_directories()

    # 初始化数据库
    if not init_database():
        print("数据库初始化失败，程序退出")
        sys.exit(1)

    # 启动应用
    try:
        from app import create_app
        app = create_app()

        # 获取本机IP地址
        import socket
        def get_local_ip():
            try:
                # 连接到一个远程地址来获取本机IP
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                ip = s.getsockname()[0]
                s.close()
                return ip
            except Exception:
                return "127.0.0.1"

        local_ip = get_local_ip()

        print("\n" + "=" * 60)
        print("二手交易平台 - 启动信息")
        print("=" * 60)
        print("本地访问地址:")
        print(f"  http://localhost:5000")
        print(f"  http://127.0.0.1:5000")
        print("\n局域网访问地址:")
        print(f"  http://{local_ip}:5000")
        print("\n账户信息:")
        print("  管理员账户: admin / admin123")
        print("  示例用户: user1 / 123456")
        print("=" * 60)
        print("\n正在启动应用...")

        # 使用Flask启动应用
        try:
            app.run(debug=True,
                   host='0.0.0.0',
                   port=5000,
                   use_reloader=True)
        except KeyboardInterrupt:
            print("\n" + "=" * 60)
            print("✓ 应用已安全关闭")
            print("=" * 60)
        except Exception as run_error:
            print(f"✗ 应用运行错误: {run_error}")
            raise

    except Exception as e:
        print(f"✗ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
