#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员后台表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Email, ValidationError, Regexp
from app.models import UserRole, Category

class UserManageForm(FlaskForm):
    """用户管理表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='请输入用户名'),
        Length(min=3, max=20, message='用户名长度应在3-20个字符之间')
    ])
    
    email = StringField('邮箱', validators=[
        DataRequired(message='请输入邮箱'),
        Email(message='请输入有效的邮箱地址')
    ])
    
    nickname = StringField('昵称', validators=[
        Length(max=50, message='昵称长度不能超过50个字符')
    ])
    
    phone = StringField('手机号', validators=[
        Length(max=20, message='手机号长度不能超过20个字符')
    ])
    
    bio = TextAreaField('个人简介', validators=[
        Length(max=500, message='个人简介长度不能超过500个字符')
    ])
    
    role = SelectField('用户角色', choices=[
        ('user', '普通用户'),
        ('admin', '管理员')
    ], validators=[DataRequired(message='请选择用户角色')])
    
    is_active = BooleanField('账户状态')
    email_verified = BooleanField('邮箱验证')
    
    submit = SubmitField('保存修改')

class ProductAuditForm(FlaskForm):
    """商品审核表单"""
    status = SelectField('商品状态', choices=[
        ('active', '审核通过 - 在售'),
        ('inactive', '下架'),
        ('pending', '待审核'),
        ('sold', '已售出')
    ], validators=[DataRequired(message='请选择商品状态')])

    audit_note = TextAreaField('审核备注', validators=[
        Length(max=500, message='备注长度不能超过500个字符')
    ])

    submit = SubmitField('更新状态')

class CategoryManageForm(FlaskForm):
    """分类管理表单"""
    name = StringField('分类名称', validators=[
        DataRequired(message='请输入分类名称'),
        Length(min=2, max=80, message='分类名称长度应在2-80个字符之间'),
        Regexp(r'^(?!\d+$).+', message='分类名称不能只包含数字')
    ])

    description = TextAreaField('分类描述', validators=[
        Length(max=500, message='分类描述长度不能超过500个字符')
    ])

    icon = StringField('图标', validators=[
        Length(max=100, message='图标长度不能超过100个字符')
    ])

    is_active = BooleanField('启用状态')

    submit = SubmitField('保存分类')

    def validate_name(self, name):
        """验证分类名称"""
        # 检查是否只包含数字
        if name.data and name.data.strip().isdigit():
            raise ValidationError('分类名称不能只包含数字')

        # 检查是否包含特殊字符
        if name.data and any(char in name.data for char in ['<', '>', '&', '"', "'"]):
            raise ValidationError('分类名称不能包含特殊字符')

class SystemConfigForm(FlaskForm):
    """系统配置表单"""
    site_name = StringField('网站名称', validators=[
        DataRequired(message='请输入网站名称'),
        Length(max=100, message='网站名称长度不能超过100个字符')
    ])
    
    site_description = TextAreaField('网站描述', validators=[
        Length(max=500, message='网站描述长度不能超过500个字符')
    ])
    
    contact_email = StringField('联系邮箱', validators=[
        Email(message='请输入有效的邮箱地址')
    ])
    
    contact_phone = StringField('联系电话', validators=[
        Length(max=20, message='联系电话长度不能超过20个字符')
    ])
    
    announcement = TextAreaField('公告内容', validators=[
        Length(max=1000, message='公告内容长度不能超过1000个字符')
    ])
    
    submit = SubmitField('保存配置')

class FeedbackReplyForm(FlaskForm):
    """反馈回复表单"""
    status = SelectField('处理状态', choices=[
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('waiting_user', '等待用户回复'),
        ('resolved', '已解决'),
        ('closed', '已关闭')
    ], validators=[DataRequired(message='请选择处理状态')])

    admin_reply = TextAreaField('管理员回复', validators=[
        DataRequired(message='请输入回复内容'),
        Length(min=1, max=1000, message='回复内容长度应在1-1000个字符之间')
    ])

    submit = SubmitField('提交回复')

class UserFeedbackStatusForm(FlaskForm):
    """用户反馈状态表单"""
    status = SelectField('反馈状态', choices=[
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('waiting_user', '等待我回复'),
        ('resolved', '已解决'),
        ('closed', '已关闭')
    ], validators=[DataRequired(message='请选择反馈状态')])

    user_note = TextAreaField('备注说明', validators=[
        Length(max=500, message='备注长度不能超过500个字符')
    ])

    submit = SubmitField('更新状态')

class EditUserForm(FlaskForm):
    """编辑用户表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='请输入用户名'),
        Length(min=3, max=20, message='用户名长度应在3-20个字符之间')
    ])

    email = StringField('邮箱', validators=[
        DataRequired(message='请输入邮箱'),
        Email(message='请输入有效的邮箱地址')
    ])

    nickname = StringField('昵称', validators=[
        Length(max=50, message='昵称长度不能超过50个字符')
    ])

    phone = StringField('手机号', validators=[
        Length(max=20, message='手机号长度不能超过20个字符')
    ])

    bio = TextAreaField('个人简介', validators=[
        Length(max=500, message='个人简介长度不能超过500个字符')
    ])

    role = SelectField('用户角色', coerce=lambda x: UserRole(x), choices=[
        (UserRole.USER, '普通用户'),
        (UserRole.ADMIN, '管理员')
    ], validators=[DataRequired(message='请选择用户角色')])

    is_active = BooleanField('账户激活')
    email_verified = BooleanField('邮箱已验证')

    submit = SubmitField('保存更改')
