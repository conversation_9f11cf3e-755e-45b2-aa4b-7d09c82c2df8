{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-reply"></i> 回复用户反馈</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.status.label(class="form-label") }}
                        {{ form.status(class="form-select") }}
                        {% for error in form.status.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.admin_reply.label(class="form-label") }}
                        {{ form.admin_reply(class="form-control", rows="5") }}
                        {% for error in form.admin_reply.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i>发送回复
                        </button>
                        <a href="{{ url_for('admin_panel.feedbacks') }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- 反馈信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">反馈信息</h6>
            </div>
            <div class="card-body">
                {% if feedback.user %}
                <div class="d-flex align-items-center mb-3">
                    <img src="{{ feedback.user.get_avatar_url() }}"
                         class="rounded-circle me-3"
                         width="48" height="48" alt="头像">
                    <div>
                        <h6 class="mb-0">{{ feedback.user.username }}</h6>
                        <small class="text-muted">{{ feedback.user.email }}</small>
                    </div>
                </div>
                {% else %}
                <div class="d-flex align-items-center mb-3">
                    <img src="/static/images/default-avatar.png"
                         class="rounded-circle me-3"
                         width="48" height="48" alt="头像">
                    <div>
                        <h6 class="mb-0">访客用户</h6>
                        <small class="text-muted">未注册用户</small>
                    </div>
                </div>
                {% endif %}
                
                <h6>{{ feedback.title }}</h6>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>类型:</strong></div>
                    <div class="col-7">
                        {% if feedback.type == 'bug' %}
                        <span class="badge bg-danger">Bug报告</span>
                        {% elif feedback.type == 'suggestion' %}
                        <span class="badge bg-info">建议</span>
                        {% elif feedback.type == 'complaint' %}
                        <span class="badge bg-warning">投诉</span>
                        {% elif feedback.type == 'contact' %}
                        <span class="badge bg-primary">联系我们</span>
                        {% else %}
                        <span class="badge bg-secondary">其他</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>状态:</strong></div>
                    <div class="col-7">
                        {% if feedback.status == 'pending' %}
                        <span class="badge bg-warning">待处理</span>
                        {% elif feedback.status == 'processing' %}
                        <span class="badge bg-info">处理中</span>
                        {% elif feedback.status == 'waiting_user' %}
                        <span class="badge bg-primary">等待用户回复</span>
                        {% elif feedback.status == 'resolved' %}
                        <span class="badge bg-success">已解决</span>
                        {% elif feedback.status == 'closed' %}
                        <span class="badge bg-secondary">已关闭</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-5"><strong>提交时间:</strong></div>
                    <div class="col-7">{{ feedback.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                
                <div class="mb-3">
                    <strong>反馈内容:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        {{ feedback.content }}
                    </div>
                </div>
                
                {% if feedback.admin_reply %}
                <div>
                    <strong>已有回复:</strong>
                    <div class="mt-2 p-3 bg-primary bg-opacity-10 rounded">
                        {{ feedback.admin_reply }}
                    </div>
                    {% if feedback.admin %}
                    <small class="text-muted">
                        回复人: {{ feedback.admin.username }} 
                        ({{ feedback.updated_at.strftime('%Y-%m-%d %H:%M') }})
                    </small>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
