{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3><i class="fas fa-receipt"></i> 订单详情</h3>
                {% if order.status.value == 'pending' %}
                    <span class="badge bg-warning fs-6">待付款</span>
                {% elif order.status.value == 'paid' %}
                    <span class="badge bg-info fs-6">已付款</span>
                {% elif order.status.value == 'shipped' %}
                    <span class="badge bg-primary fs-6">已发货</span>
                {% elif order.status.value == 'delivered' %}
                    <span class="badge bg-success fs-6">已送达</span>
                {% elif order.status.value == 'completed' %}
                    <span class="badge bg-success fs-6">已完成</span>
                {% elif order.status.value == 'cancelled' %}
                    <span class="badge bg-danger fs-6">已取消</span>
                {% endif %}
            </div>
            <div class="card-body">
                <!-- 订单基本信息 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>订单信息</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>订单号：</strong></td>
                                <td><code>{{ order.order_no }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>创建时间：</strong></td>
                                <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            </tr>
                            <tr>
                                <td><strong>订单金额：</strong></td>
                                <td><span class="text-danger fw-bold">¥{{ "%.2f"|format(order.total_amount) }}</span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>交易双方</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>买家：</strong></td>
                                <td>{{ order.buyer.nickname or order.buyer.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>卖家：</strong></td>
                                <td>{{ order.seller.nickname or order.seller.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>数量：</strong></td>
                                <td>{{ order.quantity }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">商品信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <img src="{{ order.product.get_main_image_url() }}"
                                     class="img-fluid rounded" alt="{{ order.product.title }}"
                                     style="height: 200px; object-fit: cover;">
                            </div>
                            <div class="col-md-9">
                                <h5>{{ order.product.title }}</h5>
                                <p class="text-muted">{{ order.product.description[:200] }}{% if order.product.description|length > 200 %}...{% endif %}</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>单价：</strong> ¥{{ "%.2f"|format(order.price) }}</p>
                                        <p><strong>数量：</strong> {{ order.quantity }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>成色：</strong> {{ order.product.condition or '未说明' }}</p>
                                        <p><strong>所在地：</strong> {{ order.product.location or '未说明' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收货信息 -->
                {% if order.shipping_name %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">收货信息</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>收货人：</strong> {{ order.shipping_name }}</p>
                        <p><strong>联系电话：</strong> {{ order.shipping_phone }}</p>
                        <p><strong>收货地址：</strong> {{ order.shipping_address }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- 备注信息 -->
                {% if order.buyer_note or order.seller_note %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">备注信息</h6>
                    </div>
                    <div class="card-body">
                        {% if order.buyer_note %}
                        <p><strong>买家留言：</strong> {{ order.buyer_note }}</p>
                        {% endif %}
                        {% if order.seller_note %}
                        <p><strong>卖家备注：</strong> {{ order.seller_note }}</p>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 操作按钮 -->
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('orders.my_orders') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回订单列表
                    </a>
                    
                    <div>
                        {% if current_user.id == order.buyer_id %}
                            <!-- 买家操作 -->
                            {% if order.status.value == 'pending' %}
                                <button class="btn btn-success btn-lg" onclick="showPaymentModal()">
                                    <i class="fas fa-credit-card"></i> 立即付款
                                </button>
                                <button class="btn btn-outline-danger" onclick="cancelOrder()">
                                    <i class="fas fa-times"></i> 取消订单
                                </button>
                            {% elif order.status.value == 'shipped' %}
                                <button class="btn btn-success" onclick="confirmOrder()">
                                    <i class="fas fa-check"></i> 确认收货
                                </button>
                            {% elif order.status.value == 'delivered' %}
                                <button class="btn btn-success" onclick="confirmOrder()">
                                    <i class="fas fa-check"></i> 确认收货
                                </button>
                            {% elif order.status.value == 'completed' and not order.review %}
                                <a href="{{ url_for('orders.review', id=order.id) }}" class="btn btn-warning">
                                    <i class="fas fa-star"></i> 评价
                                </a>
                            {% endif %}
                        {% elif current_user.id == order.seller_id %}
                            <!-- 卖家操作 -->
                            {% if order.status.value == 'paid' %}
                                <button class="btn btn-primary" onclick="shipOrder()">
                                    <i class="fas fa-truck"></i> 发货
                                </button>
                            {% endif %}
                        {% endif %}
                        
                        {% if current_user.id == order.buyer_id %}
                        <a href="{{ url_for('messages.chat', user_id=order.seller_id, product_id=order.product_id) }}" class="btn btn-outline-info">
                            <i class="fas fa-comment"></i> 联系卖家
                        </a>
                        {% elif current_user.id == order.seller_id %}
                        <a href="{{ url_for('messages.chat', user_id=order.buyer_id, product_id=order.product_id) }}" class="btn btn-outline-info">
                            <i class="fas fa-comment"></i> 联系买家
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- 订单状态时间线 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">订单状态</h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item {% if order.created_at %}completed{% endif %}">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>订单创建</h6>
                            {% if order.created_at %}
                            <small class="text-muted">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="timeline-item {% if order.paid_at %}completed{% endif %}">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>买家付款</h6>
                            {% if order.paid_at %}
                            <small class="text-muted">{{ order.paid_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="timeline-item {% if order.shipped_at %}completed{% endif %}">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>卖家发货</h6>
                            {% if order.shipped_at %}
                            <small class="text-muted">{{ order.shipped_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="timeline-item {% if order.delivered_at %}completed{% endif %}">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>确认收货</h6>
                            {% if order.delivered_at %}
                            <small class="text-muted">{{ order.delivered_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="timeline-item {% if order.completed_at %}completed{% endif %}">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>交易完成</h6>
                            {% if order.completed_at %}
                            <small class="text-muted">{{ order.completed_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评价信息 -->
        {% if order.review %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">交易评价</h6>
            </div>
            <div class="card-body">
                <div class="rating mb-2">
                    {% for i in range(1, 6) %}
                        {% if i <= order.review.rating %}
                            <i class="fas fa-star text-warning"></i>
                        {% else %}
                            <i class="far fa-star text-muted"></i>
                        {% endif %}
                    {% endfor %}
                    <span class="ms-2">{{ order.review.rating }}/5</span>
                </div>
                {% if order.review.content %}
                <p class="mb-0">{{ order.review.content }}</p>
                {% endif %}
                <small class="text-muted">{{ order.review.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dee2e6;
    border: 2px solid #fff;
}

.timeline-item.completed .timeline-marker {
    background: #28a745;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 14px;
}
</style>

<script>
function payOrder() {
    if (confirm('确认付款吗？\n\n注意：这是模拟付款，实际项目中需要集成真实的支付接口。')) {
        // 禁用按钮防止重复点击
        const payBtn = document.querySelector('button[onclick="payOrder()"]');
        if (payBtn) {
            payBtn.disabled = true;
            payBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 付款中...';
        }

        fetch(`/orders/{{ order.id }}/pay`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert(data.message || '付款失败', 'error');
                // 重新启用按钮
                if (payBtn) {
                    payBtn.disabled = false;
                    payBtn.innerHTML = '<i class="fas fa-credit-card"></i> 立即付款';
                }
            }
        }).catch(error => {
            console.error('Payment error:', error);
            showAlert('付款失败，请重试', 'error');
            // 重新启用按钮
            if (payBtn) {
                payBtn.disabled = false;
                payBtn.innerHTML = '<i class="fas fa-credit-card"></i> 立即付款';
            }
        });
    }
}

function cancelOrder() {
    if (confirm('确认取消订单吗？取消后无法恢复。')) {
        const cancelBtn = document.querySelector('button[onclick="cancelOrder()"]');
        if (cancelBtn) {
            cancelBtn.disabled = true;
            cancelBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 取消中...';
        }

        fetch(`/orders/{{ order.id }}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert(data.message || '取消失败', 'error');
                if (cancelBtn) {
                    cancelBtn.disabled = false;
                    cancelBtn.innerHTML = '<i class="fas fa-times"></i> 取消订单';
                }
            }
        }).catch(error => {
            console.error('Cancel error:', error);
            showAlert('取消失败，请重试', 'error');
            if (cancelBtn) {
                cancelBtn.disabled = false;
                cancelBtn.innerHTML = '<i class="fas fa-times"></i> 取消订单';
            }
        });
    }
}

function shipOrder() {
    if (confirm('确认发货吗？发货后买家将收到通知。')) {
        const shipBtn = document.querySelector('button[onclick="shipOrder()"]');
        if (shipBtn) {
            shipBtn.disabled = true;
            shipBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发货中...';
        }

        fetch(`/orders/{{ order.id }}/ship`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert(data.message || '发货失败', 'error');
                if (shipBtn) {
                    shipBtn.disabled = false;
                    shipBtn.innerHTML = '<i class="fas fa-shipping-fast"></i> 发货';
                }
            }
        }).catch(error => {
            console.error('Ship error:', error);
            showAlert('发货失败，请重试', 'error');
            if (shipBtn) {
                shipBtn.disabled = false;
                shipBtn.innerHTML = '<i class="fas fa-shipping-fast"></i> 发货';
            }
        });
    }
}

function confirmOrder() {
    if (confirm('确认收货吗？确认后订单将完成，可以进行评价。')) {
        const confirmBtn = document.querySelector('button[onclick="confirmOrder()"]');
        if (confirmBtn) {
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 确认中...';
        }

        fetch(`/orders/{{ order.id }}/confirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert(data.message || '确认收货失败', 'error');
                if (confirmBtn) {
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '<i class="fas fa-check"></i> 确认收货';
                }
            }
        }).catch(error => {
            console.error('Confirm error:', error);
            showAlert('确认收货失败，请重试', 'error');
            if (confirmBtn) {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="fas fa-check"></i> 确认收货';
            }
        });
    }
}

// 显示支付模态框
function showPaymentModal() {
    const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
    paymentModal.show();
}

// 确认付款
function confirmPayment() {
    const selectedMethod = document.querySelector('input[name="paymentMethod"]:checked');
    if (!selectedMethod) {
        alert('请选择支付方式');
        return;
    }

    // 隐藏模态框
    const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
    paymentModal.hide();

    // 执行付款
    payOrder();
}
</script>

<!-- 支付模态框 -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card me-2"></i>订单付款
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h4 class="text-primary">¥{{ "%.2f"|format(order.total_amount) }}</h4>
                    <p class="text-muted">订单号：{{ order.order_no }}</p>
                </div>

                <div class="payment-methods">
                    <h6 class="mb-3">选择支付方式：</h6>
                    <div class="list-group">
                        <label class="list-group-item">
                            <input class="form-check-input me-2" type="radio" name="paymentMethod" value="alipay" checked>
                            <i class="fab fa-alipay text-primary me-2"></i>支付宝
                        </label>
                        <label class="list-group-item">
                            <input class="form-check-input me-2" type="radio" name="paymentMethod" value="wechat">
                            <i class="fab fa-weixin text-success me-2"></i>微信支付
                        </label>
                        <label class="list-group-item">
                            <input class="form-check-input me-2" type="radio" name="paymentMethod" value="bank">
                            <i class="fas fa-university text-info me-2"></i>银行卡
                        </label>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <small>这是演示版本，实际付款不会产生费用</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="confirmPayment()">
                    <i class="fas fa-lock me-1"></i>确认付款
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}