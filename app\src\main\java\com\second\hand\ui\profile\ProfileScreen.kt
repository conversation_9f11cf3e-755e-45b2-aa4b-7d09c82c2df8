package com.second.hand.ui.profile

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.second.hand.ui.auth.AuthViewModel
import com.second.hand.ui.components.UserStateCard
import com.second.hand.ui.navigation.NavigationDestinations

/**
 * 个人中心页面Screen
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    navController: NavController,
    authViewModel: AuthViewModel,
    onNavigateToWelcome: () -> Unit = {}
) {
    val authUiState by authViewModel.uiState.collectAsState()
    val userState by authViewModel.getUserState().collectAsState()

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // 顶部应用栏
            TopAppBar(
                title = {
                    Text(
                        text = "我的",
                        fontWeight = FontWeight.Bold
                    )
                },
                actions = {
                    IconButton(
                        onClick = {
                            navController.navigate(NavigationDestinations.SETTINGS)
                        }
                    ) {
                        Icon(Icons.Default.Settings, contentDescription = "设置")
                    }
                }
            )
        }

        item {
            // 用户状态卡片
            UserStateCard(
                userState = userState,
                onLoginClick = { onNavigateToWelcome() },
                onProfileClick = {
                    navController.navigate(NavigationDestinations.EDIT_PROFILE)
                },
                onLogoutClick = { authViewModel.logout() }
            )
        }

        item {
            // 统计信息
            UserStatsCard()
        }

        item {
            // 我的商品
            MenuSection(
                title = "我的商品",
                items = listOf(
                    MenuItem("我发布的", Icons.Default.Home, NavigationDestinations.MY_PRODUCTS),
                    MenuItem("我的收藏", Icons.Default.Favorite, NavigationDestinations.FAVORITES)
                ),
                navController = navController
            )
        }

        item {
            // 交易管理
            MenuSection(
                title = "交易管理",
                items = listOf(
                    MenuItem("我的订单", Icons.Default.ShoppingCart, NavigationDestinations.ORDERS),
                    MenuItem("交易记录", Icons.Default.DateRange, "transaction_history"),
                    MenuItem("地址管理", Icons.Default.LocationOn, "address_management")
                ),
                navController = navController
            )
        }

        item {
            // 其他功能
            MenuSection(
                title = "其他",
                items = listOf(
                    MenuItem("客服帮助", Icons.Default.Info, "help"),
                    MenuItem("意见反馈", Icons.Default.Edit, "feedback"),
                    MenuItem("关于我们", Icons.Default.Info, "about")
                ),
                navController = navController
            )
        }

        item {
            // 调试功能区域
            MenuSection(
                title = "🔧 调试功能",
                items = listOf(
                    MenuItem("应用状态检查", Icons.Default.Settings, NavigationDestinations.APP_STATUS),
                    MenuItem("登录注册测试", Icons.Default.Settings, NavigationDestinations.AUTH_TEST),
                    MenuItem("Token状态监控", Icons.Default.Settings, NavigationDestinations.TOKEN_DEBUG),
                    MenuItem("Token刷新测试", Icons.Default.Settings, NavigationDestinations.TOKEN_REFRESH_TEST)
                ),
                navController = navController
            )
        }

        item {
            // 退出登录按钮
            LogoutButton(
                onLogout = {
                    authViewModel.logout()
                    onNavigateToWelcome()
                }
            )
        }

        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
private fun UserInfoCard(
    user: com.second.hand.data.model.User?,
    onEditProfile: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier.padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 用户头像
            Card(
                modifier = Modifier.size(60.dp),
                shape = CircleShape,
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = (user?.nickname?.take(1) ?: "U").uppercase(),
                        style = MaterialTheme.typography.headlineSmall,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 用户信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = user?.nickname ?: "未登录",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = user?.email ?: "",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )

                user?.phone?.let { phone ->
                    if (phone.isNotEmpty()) {
                        Text(
                            text = phone,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }

            // 编辑按钮
            IconButton(onClick = onEditProfile) {
                Icon(
                    Icons.Default.Edit,
                    contentDescription = "编辑资料",
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
    }
}

@Composable
private fun UserStatsCard() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatItem(label = "发布", count = "12")
            StatItem(label = "收藏", count = "8")
            StatItem(label = "订单", count = "5")
            StatItem(label = "评价", count = "15")
        }
    }
}

@Composable
private fun StatItem(label: String, count: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = count,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun MenuSection(
    title: String,
    items: List<MenuItem>,
    navController: NavController
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(vertical = 8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            items.forEach { item ->
                MenuItemRow(
                    item = item,
                    onClick = {
                        navController.navigate(item.route)
                    }
                )
            }
        }
    }
}

@Composable
private fun MenuItemRow(
    item: MenuItem,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = item.icon,
            contentDescription = item.title,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = item.title,
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(1f)
        )

        IconButton(onClick = onClick) {
            Icon(
                Icons.Default.KeyboardArrowRight,
                contentDescription = "进入",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun LogoutButton(onLogout: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        ),
        onClick = onLogout
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.ExitToApp,
                contentDescription = "退出登录",
                tint = MaterialTheme.colorScheme.onErrorContainer
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = "退出登录",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
        }
    }
}

// 数据类
data class MenuItem(
    val title: String,
    val icon: ImageVector,
    val route: String
)
