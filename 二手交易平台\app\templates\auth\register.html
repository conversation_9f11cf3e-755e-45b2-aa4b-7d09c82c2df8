{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header text-center">
                <h3><i class="fas fa-user-plus"></i> 用户注册</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.phone.label(class="form-label") }}
                        {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else ""), placeholder="可选") }}
                        {% if form.phone.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- 邮箱验证码 -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.email_code.label(class="form-label") }}
                                {{ form.email_code(class="form-control" + (" is-invalid" if form.email_code.errors else ""), placeholder="请输入6位数字验证码") }}
                                {% if form.email_code.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.email_code.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex align-items-center">
                                    <button type="button" id="sendCodeBtn" class="btn btn-outline-primary" onclick="sendVerificationCode()">
                                        获取验证码
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.password2.label(class="form-label") }}
                            {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else "")) }}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 验证码 -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.captcha.label(class="form-label") }}
                                {{ form.captcha(class="form-control" + (" is-invalid" if form.captcha.errors else ""), placeholder="请输入验证码") }}
                                {% if form.captcha.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.captcha.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex align-items-center">
                                    <img id="captcha-image" src="{{ url_for('auth.captcha') }}"
                                         alt="验证码" class="img-fluid me-2"
                                         style="height: 38px; cursor: pointer; border: 1px solid #ced4da; border-radius: 0.375rem;">
                                    <button type="button" class="btn btn-outline-secondary btn-sm"
                                            onclick="refreshCaptcha()" title="刷新验证码">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">
                    已有账户？ <a href="{{ url_for('auth.login') }}">立即登录</a>
                </p>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
function refreshCaptcha() {
    var captchaImg = document.getElementById('captcha-image');
    captchaImg.src = "{{ url_for('auth.captcha') }}?" + new Date().getTime();
}

// 点击验证码图片也可以刷新
document.getElementById('captcha-image').addEventListener('click', refreshCaptcha);

// 发送邮箱验证码
let countdown = 0;
let countdownTimer = null;

function sendVerificationCode() {
    const emailInput = document.getElementById('email');
    const sendBtn = document.getElementById('sendCodeBtn');
    const email = emailInput.value.trim();

    // 验证邮箱格式
    if (!email) {
        alert('请先输入邮箱地址');
        emailInput.focus();
        return;
    }

    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailPattern.test(email)) {
        alert('请输入有效的邮箱地址');
        emailInput.focus();
        return;
    }

    // 如果正在倒计时，不允许重复发送
    if (countdown > 0) {
        return;
    }

    // 禁用按钮
    sendBtn.disabled = true;
    sendBtn.textContent = '发送中...';

    // 获取CSRF令牌
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;

    // 发送请求
    fetch('{{ url_for("auth.send_verification_code") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            email: email
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            startCountdown();
        } else {
            alert(data.message);
            sendBtn.disabled = false;
            sendBtn.textContent = '获取验证码';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('发送失败，请稍后重试');
        sendBtn.disabled = false;
        sendBtn.textContent = '获取验证码';
    });
}

function startCountdown() {
    const sendBtn = document.getElementById('sendCodeBtn');
    countdown = 120; // 120秒倒计时

    countdownTimer = setInterval(() => {
        sendBtn.textContent = `${countdown}秒后重新获取`;
        countdown--;

        if (countdown < 0) {
            clearInterval(countdownTimer);
            sendBtn.disabled = false;
            sendBtn.textContent = '获取验证码';
            countdown = 0;
        }
    }, 1000);
}
</script>
{% endblock %}
