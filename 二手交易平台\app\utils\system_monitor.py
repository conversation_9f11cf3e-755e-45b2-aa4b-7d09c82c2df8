#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控工具
"""

import psutil
import platform
import os
import time
from datetime import datetime
from typing import Dict, Any


def get_system_info() -> Dict[str, Any]:
    """获取系统基本信息"""
    try:
        return {
            'platform': platform.system(),
            'platform_release': platform.release(),
            'platform_version': platform.version(),
            'architecture': platform.machine(),
            'hostname': platform.node(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'boot_time': datetime.fromtimestamp(psutil.boot_time()).strftime('%Y-%m-%d %H:%M:%S')
        }
    except Exception as e:
        return {'error': str(e)}


def get_cpu_info() -> Dict[str, Any]:
    """获取CPU信息"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_count_logical = psutil.cpu_count(logical=True)
        cpu_freq = psutil.cpu_freq()
        
        return {
            'usage_percent': round(cpu_percent, 2),
            'count_physical': cpu_count,
            'count_logical': cpu_count_logical,
            'frequency_current': round(cpu_freq.current, 2) if cpu_freq else 0,
            'frequency_max': round(cpu_freq.max, 2) if cpu_freq else 0,
            'frequency_min': round(cpu_freq.min, 2) if cpu_freq else 0,
            'per_cpu_percent': [round(x, 2) for x in psutil.cpu_percent(percpu=True)]
        }
    except Exception as e:
        return {'error': str(e)}


def get_memory_info() -> Dict[str, Any]:
    """获取内存信息"""
    try:
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        return {
            'total': round(memory.total / (1024**3), 2),  # GB
            'available': round(memory.available / (1024**3), 2),
            'used': round(memory.used / (1024**3), 2),
            'free': round(memory.free / (1024**3), 2),
            'percent': round(memory.percent, 2),
            'swap_total': round(swap.total / (1024**3), 2),
            'swap_used': round(swap.used / (1024**3), 2),
            'swap_free': round(swap.free / (1024**3), 2),
            'swap_percent': round(swap.percent, 2)
        }
    except Exception as e:
        return {'error': str(e)}


def get_disk_info() -> Dict[str, Any]:
    """获取磁盘信息"""
    try:
        disk_usage = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        
        # 获取所有磁盘分区
        partitions = []
        for partition in psutil.disk_partitions():
            try:
                partition_usage = psutil.disk_usage(partition.mountpoint)
                partitions.append({
                    'device': partition.device,
                    'mountpoint': partition.mountpoint,
                    'file_system': partition.fstype,
                    'total': round(partition_usage.total / (1024**3), 2),
                    'used': round(partition_usage.used / (1024**3), 2),
                    'free': round(partition_usage.free / (1024**3), 2),
                    'percent': round((partition_usage.used / partition_usage.total) * 100, 2)
                })
            except PermissionError:
                continue
        
        return {
            'total': round(disk_usage.total / (1024**3), 2),
            'used': round(disk_usage.used / (1024**3), 2),
            'free': round(disk_usage.free / (1024**3), 2),
            'percent': round((disk_usage.used / disk_usage.total) * 100, 2),
            'read_bytes': round(disk_io.read_bytes / (1024**2), 2) if disk_io else 0,  # MB
            'write_bytes': round(disk_io.write_bytes / (1024**2), 2) if disk_io else 0,
            'partitions': partitions
        }
    except Exception as e:
        return {'error': str(e)}


def get_network_info() -> Dict[str, Any]:
    """获取网络信息"""
    try:
        net_io = psutil.net_io_counters()
        net_connections = len(psutil.net_connections())
        
        # 获取网络接口信息
        interfaces = []
        for interface_name, interface_addresses in psutil.net_if_addrs().items():
            interface_stats = psutil.net_if_stats()[interface_name]
            addresses = []
            for addr in interface_addresses:
                addresses.append({
                    'family': str(addr.family),
                    'address': addr.address,
                    'netmask': addr.netmask,
                    'broadcast': addr.broadcast
                })
            
            interfaces.append({
                'name': interface_name,
                'is_up': interface_stats.isup,
                'speed': interface_stats.speed,
                'addresses': addresses
            })
        
        return {
            'bytes_sent': round(net_io.bytes_sent / (1024**2), 2),  # MB
            'bytes_recv': round(net_io.bytes_recv / (1024**2), 2),
            'packets_sent': net_io.packets_sent,
            'packets_recv': net_io.packets_recv,
            'connections': net_connections,
            'interfaces': interfaces
        }
    except Exception as e:
        return {'error': str(e)}


def get_process_info() -> Dict[str, Any]:
    """获取进程信息"""
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cpu_percent': round(proc.info['cpu_percent'] or 0, 2),
                    'memory_percent': round(proc.info['memory_percent'] or 0, 2)
                })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 按CPU使用率排序，取前10个
        processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
        top_processes = processes[:10]
        
        return {
            'total_processes': len(processes),
            'top_processes': top_processes,
            'current_process': {
                'pid': os.getpid(),
                'cpu_percent': round(psutil.Process().cpu_percent(), 2),
                'memory_percent': round(psutil.Process().memory_percent(), 2)
            }
        }
    except Exception as e:
        return {'error': str(e)}


def get_temperature_info() -> Dict[str, Any]:
    """获取温度信息（如果可用）"""
    try:
        temps = psutil.sensors_temperatures()
        if not temps:
            return {'message': '温度传感器不可用'}
        
        temperature_data = {}
        for name, entries in temps.items():
            temp_list = []
            for entry in entries:
                temp_list.append({
                    'label': entry.label or 'N/A',
                    'current': entry.current,
                    'high': entry.high,
                    'critical': entry.critical
                })
            temperature_data[name] = temp_list
        
        return temperature_data
    except Exception as e:
        return {'error': str(e)}


def get_complete_system_status() -> Dict[str, Any]:
    """获取完整的系统状态"""
    return {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'system_info': get_system_info(),
        'cpu': get_cpu_info(),
        'memory': get_memory_info(),
        'disk': get_disk_info(),
        'network': get_network_info(),
        'processes': get_process_info(),
        'temperature': get_temperature_info()
    }


def get_simplified_system_status() -> Dict[str, Any]:
    """获取简化的系统状态 - 统一的数据源"""
    import logging

    logger = logging.getLogger(__name__)

    try:
        logger.debug("开始获取系统状态信息")

        # 获取完整系统状态
        system_status = get_complete_system_status()
        health_score = get_system_health_score()

        logger.debug("系统状态和健康评分获取成功")

        # 安全地获取系统信息，避免KeyError
        def safe_get(data, keys, default='未知'):
            try:
                result = data
                for key in keys:
                    result = result[key]
                return result
            except (KeyError, TypeError) as e:
                logger.warning(f"获取系统信息失败 {keys}: {str(e)}")
                return default

        # 检查数据库状态
        db_status = '正常'
        try:
            import sqlite3
            import os

            # 检查多个可能的数据库文件位置
            db_paths = ['app.db', 'app/app.db']
            db_connected = False

            for db_path in db_paths:
                if os.path.exists(db_path) and os.path.getsize(db_path) > 0:
                    try:
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute("SELECT 1")
                        conn.close()
                        db_connected = True
                        break
                    except Exception:
                        continue

            if not db_connected:
                db_status = '异常'
        except Exception:
            db_status = '异常'

        # 返回统一格式的简化状态信息
        return {
            'server_status': '在线',
            'db_status': db_status,
            'health_score': health_score,
            'cpu_usage': f"{safe_get(system_status, ['cpu', 'usage_percent'], 0):.1f}%",
            'memory_usage': f"{safe_get(system_status, ['memory', 'percent'], 0):.1f}%",
            'memory_used': f"{safe_get(system_status, ['memory', 'used'], 0):.1f}GB",
            'memory_total': f"{safe_get(system_status, ['memory', 'total'], 0):.1f}GB",
            'disk_usage': f"{safe_get(system_status, ['disk', 'percent'], 0):.1f}%",
            'disk_used': f"{safe_get(system_status, ['disk', 'used'], 0):.1f}GB",
            'disk_total': f"{safe_get(system_status, ['disk', 'total'], 0):.1f}GB",
            'platform': safe_get(system_status, ['system_info', 'platform']),
            'hostname': safe_get(system_status, ['system_info', 'hostname']),
            'timestamp': safe_get(system_status, ['timestamp']),
            'processes': safe_get(system_status, ['processes', 'total_processes'], 0),
            'network_sent': f"{safe_get(system_status, ['network', 'bytes_sent'], 0):.1f}MB",
            'network_recv': f"{safe_get(system_status, ['network', 'bytes_recv'], 0):.1f}MB",
            'online_users': 0  # 这个值将在调用处设置
        }
    except Exception as e:
        # 返回错误状态
        return {
            'server_status': '在线',
            'db_status': '正常',
            'health_score': {'overall_score': 0, 'status': 'unknown', 'status_text': '未知', 'color': 'secondary'},
            'cpu_usage': '0.0%',
            'memory_usage': '0.0%',
            'memory_used': '0.0GB',
            'memory_total': '0.0GB',
            'disk_usage': '0.0%',
            'disk_used': '0.0GB',
            'disk_total': '0.0GB',
            'platform': '未知',
            'hostname': '未知',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'processes': 0,
            'network_sent': '0.0MB',
            'network_recv': '0.0MB',
            'online_users': 0,
            'error': str(e)
        }


def format_bytes(bytes_value: int) -> str:
    """格式化字节数"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.2f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.2f} PB"


def get_system_health_score() -> Dict[str, Any]:
    """计算系统健康评分"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory_percent = psutil.virtual_memory().percent
        disk_percent = psutil.disk_usage('/').percent
        
        # 计算健康评分 (0-100)
        cpu_score = max(0, 100 - cpu_percent)
        memory_score = max(0, 100 - memory_percent)
        disk_score = max(0, 100 - disk_percent)
        
        overall_score = (cpu_score + memory_score + disk_score) / 3
        
        # 确定健康状态
        if overall_score >= 80:
            status = 'excellent'
            status_text = '优秀'
            color = 'success'
        elif overall_score >= 60:
            status = 'good'
            status_text = '良好'
            color = 'info'
        elif overall_score >= 40:
            status = 'warning'
            status_text = '警告'
            color = 'warning'
        else:
            status = 'critical'
            status_text = '危险'
            color = 'danger'
        
        return {
            'overall_score': round(overall_score, 2),
            'cpu_score': round(cpu_score, 2),
            'memory_score': round(memory_score, 2),
            'disk_score': round(disk_score, 2),
            'status': status,
            'status_text': status_text,
            'color': color
        }
    except Exception as e:
        return {'error': str(e)}
