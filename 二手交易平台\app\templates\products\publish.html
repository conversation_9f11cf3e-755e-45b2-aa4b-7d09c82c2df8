{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0"><i class="fas fa-plus-circle"></i> 发布商品</h3>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <!-- 商品标题 -->
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">请输入简洁明了的商品标题</div>
                    </div>
                    
                    <!-- 商品描述 -->
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="5") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">详细描述商品的特点、使用情况等</div>
                    </div>
                    
                    <!-- 价格信息 -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.price.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">¥</span>
                                {{ form.price(class="form-control" + (" is-invalid" if form.price.errors else "")) }}
                                {% if form.price.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.price.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.original_price.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">¥</span>
                                {{ form.original_price(class="form-control" + (" is-invalid" if form.original_price.errors else "")) }}
                                {% if form.original_price.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.original_price.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="form-text">可选，用于显示折扣</div>
                        </div>
                    </div>
                    
                    <!-- 分类和新旧程度 -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.category_id.label(class="form-label") }}
                            {{ form.category_id(class="form-select" + (" is-invalid" if form.category_id.errors else "")) }}
                            {% if form.category_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.category_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.condition.label(class="form-label") }}
                            {{ form.condition(class="form-select" + (" is-invalid" if form.condition.errors else "")) }}
                            {% if form.condition.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.condition.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- 所在地 -->
                    <div class="mb-3">
                        {{ form.location.label(class="form-label") }}
                        {{ form.location(class="form-control" + (" is-invalid" if form.location.errors else "")) }}
                        {% if form.location.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.location.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">填写所在城市或地区</div>
                    </div>

                    <!-- 商品数量 -->
                    <div class="mb-3">
                        {{ form.quantity.label(class="form-label") }}
                        {{ form.quantity(class="form-control" + (" is-invalid" if form.quantity.errors else "")) }}
                        {% if form.quantity.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.quantity.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">填写拥有的商品数量</div>
                    </div>
                    
                    <!-- 商品图片 -->
                    <div class="mb-4">
                        {{ form.images.label(class="form-label") }}
                        {{ form.images(class="form-control" + (" is-invalid" if form.images.errors else ""), accept="image/*") }}
                        {% if form.images.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.images.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> 
                            支持jpg、jpeg、png、gif格式，最多可上传5张图片，第一张将作为主图显示
                        </div>
                        
                        <!-- 图片预览区域 -->
                        <div class="image-preview-container mt-3"></div>
                    </div>
                    
                    <!-- 发布须知 -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-exclamation-circle"></i> 发布须知</h6>
                        <ul class="mb-0">
                            <li>请确保商品信息真实有效</li>
                            <li>禁止发布违法违规商品</li>
                            <li>商品发布后需要管理员审核</li>
                            <li>审核通过后商品将在平台展示</li>
                        </ul>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('products.my_products') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 图片上传预览
    $('input[name="images"]').change(function() {
        var input = this;
        var preview = $('.image-preview-container');
        preview.empty();
        
        if (input.files && input.files.length > 0) {
            if (input.files.length > 5) {
                alert('最多只能上传5张图片');
                input.value = '';
                return;
            }
            
            Array.from(input.files).forEach(function(file, index) {
                if (file.type.startsWith('image/')) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var imageHtml = `
                            <div class="image-preview d-inline-block me-2 mb-2" data-index="${index}">
                                <img src="${e.target.result}" alt="预览" class="img-thumbnail" 
                                     style="width: 120px; height: 120px; object-fit: cover;">
                                <div class="text-center mt-1">
                                    <small class="text-muted">${index === 0 ? '主图' : '图片' + (index + 1)}</small>
                                </div>
                            </div>
                        `;
                        preview.append(imageHtml);
                    };
                    reader.readAsDataURL(file);
                } else {
                    alert('请选择图片文件');
                }
            });
        }
    });
    
    // 表单验证
    $('form').submit(function(e) {
        var title = $('input[name="title"]').val().trim();
        var description = $('textarea[name="description"]').val().trim();
        var price = $('input[name="price"]').val();
        var category = $('select[name="category_id"]').val();
        
        if (!title || !description || !price || !category) {
            e.preventDefault();
            alert('请填写所有必填项');
            return false;
        }
        
        if (parseFloat(price) <= 0) {
            e.preventDefault();
            alert('价格必须大于0');
            return false;
        }
        
        // 显示提交状态
        var submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>发布中...');
    });
    
    // 字符计数
    $('input[name="title"]').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 200;
        var remaining = maxLength - length;
        
        if (!$(this).siblings('.char-count').length) {
            $(this).after('<div class="form-text char-count"></div>');
        }
        
        $(this).siblings('.char-count').text(`${length}/${maxLength} 字符`);
        
        if (remaining < 0) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    $('textarea[name="description"]').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 2000;
        var remaining = maxLength - length;
        
        if (!$(this).siblings('.char-count').length) {
            $(this).after('<div class="form-text char-count"></div>');
        }
        
        $(this).siblings('.char-count').text(`${length}/${maxLength} 字符`);
        
        if (remaining < 0) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});
</script>
{% endblock %}
