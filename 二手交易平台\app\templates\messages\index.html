{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-comments me-2"></i>消息中心</h2>
        <div class="text-muted">
            共 {{ conversations|length }} 个对话
        </div>
    </div>

    {% if conversations %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">最近对话</h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="conversationsList">
                        {% for conv in conversations %}
                        <div class="list-group-item conversation-item position-relative"
                             data-user-id="{{ conv.other_user.id }}"
                             data-product-id="{{ conv.product.id if conv.product else '' }}"
                             data-last-time="{{ conv.last_time.strftime('%Y-%m-%d %H:%M:%S') }}">

                            <!-- 删除按钮 -->
                            <button class="btn btn-sm btn-outline-danger position-absolute delete-conversation-btn"
                                    style="top: 10px; right: 10px; z-index: 10; opacity: 0; transition: opacity 0.3s;"
                                    data-user-id="{{ conv.other_user.id }}"
                                    data-product-id="{{ conv.product.id if conv.product else '' }}"
                                    title="删除对话">
                                <i class="fas fa-times"></i>
                            </button>

                            <!-- 对话链接 -->
                            <a href="{{ url_for('messages.chat', user_id=conv.other_user.id, product_id=conv.product.id if conv.product else None) }}"
                               class="text-decoration-none text-dark d-block">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <div class="d-flex align-items-center flex-grow-1">
                                    <!-- 用户头像 -->
                                    <img src="{{ conv.other_user.get_avatar_url() }}" 
                                         class="rounded-circle me-3" 
                                         width="50" height="50" alt="头像">
                                    
                                    <div class="flex-grow-1">
                                        <!-- 用户名和商品信息 -->
                                        <div class="d-flex align-items-center mb-1">
                                            <h6 class="mb-0 me-2">{{ conv.other_user.nickname or conv.other_user.username }}</h6>
                                            {% if conv.product %}
                                            <small class="text-muted">关于: {{ conv.product.title[:20] }}{% if conv.product.title|length > 20 %}...{% endif %}</small>
                                            {% endif %}
                                        </div>
                                        
                                        <!-- 最后一条消息 -->
                                        {% if conv.last_message and conv.last_message.content %}
                                        <p class="mb-1 text-muted last-message-content">
                                            {% if conv.last_message.sender_id == current_user.id %}
                                            <i class="fas fa-reply me-1"></i>
                                            {% endif %}
                                            {% set content = conv.last_message.content|string|truncate(50, True) %}
                                            {{ content|e }}
                                        </p>
                                        {% else %}
                                        <p class="mb-1 text-muted last-message-content">
                                            <i class="fas fa-comment me-1"></i>
                                            暂无消息内容
                                        </p>
                                        {% endif %}

                                        <!-- 时间 -->
                                        <small class="text-muted last-message-time">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ moment(conv.last_time).fromNow() }}
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    {% if conv.unread_count > 0 %}
                                    <span class="badge bg-danger rounded-pill unread-badge">{{ conv.unread_count }}</span>
                                    {% else %}
                                    <span class="badge bg-danger rounded-pill unread-badge" style="display: none;"></span>
                                    {% endif %}
                                    <div class="mt-1">
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-comments fa-5x text-muted mb-3"></i>
        <h4 class="text-muted">暂无对话</h4>
        <p class="text-muted">开始浏览商品并与卖家交流吧！</p>
        <a href="{{ url_for('main.search') }}" class="btn btn-primary">
            <i class="fas fa-search me-1"></i>浏览商品
        </a>
    </div>
    {% endif %}
</div>

<style>
.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item-action {
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.list-group-item-action:hover {
    border-left-color: #007bff;
}

.badge {
    font-size: 0.75rem;
}

.page-transition {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.conversation-item.new-message {
    animation: highlightNew 2s ease-in-out;
}

@keyframes highlightNew {
    0% { background-color: #e3f2fd; }
    100% { background-color: transparent; }
}

.unread-badge.updated {
    animation: pulse 1s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.conversation-item {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.conversation-item:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.delete-conversation-btn {
    transition: all 0.3s ease;
}

.delete-conversation-btn:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}
</style>

{% block scripts %}
<script>
$(document).ready(function() {
    // 页面加载时刷新未读消息计数
    if (typeof refreshNotifications === 'function') {
        refreshNotifications();
    }

    let lastUpdateTime = new Date();
    let updateInterval;
    let cachedConversationsData = null; // 缓存对话数据

    // 开始实时更新
    function startRealTimeUpdates() {
        // 每0.5秒检查一次新消息
        updateInterval = setInterval(function() {
            updateConversations();
        }, 500);

        // 立即执行一次
        updateConversations();
    }

    // 更新对话列表
    function updateConversations() {
        $.ajax({
            url: '/messages/api/conversations',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    // 比较数据是否有变化
                    const newDataString = JSON.stringify(response);
                    if (cachedConversationsData !== newDataString) {
                        cachedConversationsData = newDataString;
                        updateConversationsList(response.conversations);
                        updatePageTitle(response.total_count);
                    }
                }
            },
            error: function() {
                console.log('获取对话列表失败');
            }
        });
    }

    // 更新对话列表DOM
    function updateConversationsList(conversations) {
        const conversationsList = $('#conversationsList');

        // 清空现有列表，重新构建（避免重复问题）
        conversationsList.empty();

        // 重新添加所有对话
        conversations.forEach(function(conv) {
            addConversationItem(conversationsList, conv);
        });
    }

    // 删除对话
    function deleteConversation(userId, productId) {
        if (!confirm('确定要删除这个对话吗？删除后将无法恢复。')) {
            return;
        }

        $.ajax({
            url: '/messages/api/delete_conversation',
            method: 'POST',
            data: {
                user_id: userId,
                product_id: productId || '',
                csrf_token: $('meta[name=csrf-token]').attr('content')
            },
            beforeSend: function() {
                console.log('发送删除请求:', { userId, productId });
            },
            success: function(response) {
                console.log('删除响应:', response);
                if (response.success) {
                    // 从DOM中移除对话项
                    $(`.conversation-item[data-user-id="${userId}"][data-product-id="${productId || ''}"]`)
                        .fadeOut(300, function() {
                            $(this).remove();
                            updatePageTitle($('.conversation-item').length);
                        });

                    showAlert('对话已删除', 'success');
                } else {
                    console.error('删除失败:', response);
                    showAlert('删除失败：' + (response.message || '未知错误'), 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('删除请求失败:', { xhr, status, error });
                console.error('响应内容:', xhr.responseText);

                let errorMessage = '删除失败，请重试';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = '删除失败：' + response.message;
                    }
                } catch (e) {
                    console.error('解析错误响应失败:', e);
                }

                showAlert(errorMessage, 'error');
            }
        });
    }

    // 添加对话项
    function addConversationItem(container, conv) {
        const productId = conv.product_id || '';
        const productInfo = conv.product_title ?
            `<small class="text-muted">关于: ${conv.product_title.substring(0, 20)}${conv.product_title.length > 20 ? '...' : ''}</small>` :
            '';

        let messageContent = '';
        if (conv.last_message_content) {
            if (conv.is_sender) {
                messageContent += '<i class="fas fa-reply me-1"></i>';
            }
            const content = conv.last_message_content.length > 50 ?
                conv.last_message_content.substring(0, 50) + '...' :
                conv.last_message_content;
            messageContent += $('<div>').text(content).html();
        } else {
            messageContent = '<i class="fas fa-comment me-1"></i>暂无消息内容';
        }

        const unreadBadge = conv.unread_count > 0 ?
            `<span class="badge bg-danger rounded-pill unread-badge">${conv.unread_count}</span>` :
            '<span class="badge bg-danger rounded-pill unread-badge" style="display: none;"></span>';

        // 头像处理
        const avatarHtml = conv.other_user_avatar ?
            `<img src="${conv.other_user_avatar}" class="rounded-circle me-3" width="50" height="50" alt="头像">` :
            `<div class="rounded-circle me-3 d-flex align-items-center justify-content-center bg-primary text-white" style="width: 50px; height: 50px; font-size: 20px; font-weight: bold;">${conv.other_user_name.charAt(0).toUpperCase()}</div>`;

        const conversationHtml = `
            <div class="list-group-item conversation-item position-relative"
                 data-user-id="${conv.other_user_id}"
                 data-product-id="${productId}"
                 data-last-time="${conv.last_message_time}">

                <!-- 删除按钮 -->
                <button class="btn btn-sm btn-outline-danger position-absolute delete-conversation-btn"
                        style="top: 10px; right: 10px; z-index: 10; opacity: 0; transition: opacity 0.3s;"
                        data-user-id="${conv.other_user_id}"
                        data-product-id="${productId}"
                        title="删除对话">
                    <i class="fas fa-times"></i>
                </button>

                <!-- 对话链接 -->
                <a href="/messages/chat/${conv.other_user_id}${productId ? '/' + productId : ''}"
                   class="text-decoration-none text-dark d-block conversation-link"
                   data-user-id="${conv.other_user_id}"
                   data-product-id="${productId}"
                   data-unread-count="${conv.unread_count}">
                    <div class="d-flex w-100 justify-content-between align-items-center">
                        <div class="d-flex align-items-center flex-grow-1">
                            ${avatarHtml}

                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-1">
                                    <h6 class="mb-0 me-2">${conv.other_user_name}</h6>
                                    ${productInfo}
                                </div>

                                <p class="mb-1 text-muted last-message-content">
                                    ${messageContent}
                                </p>

                                <small class="text-muted last-message-time">
                                    <i class="fas fa-clock me-1"></i>
                                    ${formatTime(conv.last_message_time)}
                                </small>
                            </div>
                        </div>

                        <div class="text-end">
                            ${unreadBadge}
                            <div class="mt-1">
                                <i class="fas fa-chevron-right text-muted"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        `;

        container.append(conversationHtml);
    }

    // 显示提示消息
    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('body').append(alertHtml);

        // 3秒后自动消失
        setTimeout(function() {
            $('.alert').fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    // 更新页面标题
    function updatePageTitle(count) {
        $('.page-transition h2').next('.text-muted').text(`共 ${count} 个对话`);
    }

    // 页面获得焦点时立即更新
    $(window).focus(function() {
        updateConversations();
    });

    // 页面可见性改变时更新
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            updateConversations();
        }
    });

    // 页面卸载时清理定时器
    $(window).on('beforeunload', function() {
        if (updateInterval) {
            clearInterval(updateInterval);
        }
    });

    // 格式化时间显示
    function formatTime(timeString) {
        if (!timeString) return '';

        const messageTime = new Date(timeString);
        const now = new Date();
        const diffMs = now - messageTime;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) {
            return '刚刚';
        } else if (diffMins < 60) {
            return `${diffMins}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return messageTime.toLocaleDateString();
        }
    }

    // 启动实时更新
    startRealTimeUpdates();

    // 对话项鼠标悬停效果
    $(document).on('mouseenter', '.conversation-item', function() {
        $(this).find('.delete-conversation-btn').css('opacity', '1');
    });

    $(document).on('mouseleave', '.conversation-item', function() {
        $(this).find('.delete-conversation-btn').css('opacity', '0');
    });

    // 删除对话按钮点击事件
    $(document).on('click', '.delete-conversation-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const userId = $(this).data('user-id');
        const productId = $(this).data('product-id');

        deleteConversation(userId, productId);
    });

    // 对话链接点击事件 - 标记消息为已读
    $(document).on('click', '.conversation-link', function(e) {
        const userId = $(this).data('user-id');
        const productId = $(this).data('product-id');
        const unreadCount = $(this).data('unread-count');

        // 如果有未读消息，先标记为已读
        if (unreadCount > 0) {
            markMessagesAsRead(userId, productId);
        }
    });

    // 标记消息为已读
    function markMessagesAsRead(userId, productId) {
        $.ajax({
            url: '/messages/api/mark_messages_read',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                user_id: userId,
                product_id: productId
            }),
            success: function(response) {
                if (response.success) {
                    console.log(`标记了 ${response.marked_count} 条消息为已读`);
                    // 立即更新对话列表
                    updateConversations();
                }
            },
            error: function() {
                console.log('标记消息已读失败');
            }
        });
    }
});
</script>
{% endblock %}
{% endblock %}
