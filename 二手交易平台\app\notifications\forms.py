#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知与反馈表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length, Email

class FeedbackForm(FlaskForm):
    """用户反馈表单"""
    title = StringField('反馈标题', validators=[
        DataRequired(message='请输入反馈标题'),
        Length(min=5, max=200, message='标题长度应在5-200个字符之间')
    ])
    
    type = SelectField('反馈类型', choices=[
        ('bug', '问题反馈'),
        ('suggestion', '功能建议'),
        ('complaint', '投诉举报'),
        ('contact', '联系我们'),
        ('other', '其他')
    ], validators=[DataRequired(message='请选择反馈类型')])
    
    content = TextAreaField('反馈内容', validators=[
        DataRequired(message='请输入反馈内容'),
        Length(min=10, max=2000, message='内容长度应在10-2000个字符之间')
    ])
    
    submit = SubmitField('提交反馈')

class ContactForm(FlaskForm):
    """联系我们表单"""
    name = StringField('姓名', validators=[
        DataRequired(message='请输入您的姓名'),
        Length(min=2, max=50, message='姓名长度应在2-50个字符之间')
    ])

    email = StringField('邮箱', validators=[
        DataRequired(message='请输入您的邮箱'),
        Email(message='请输入有效的邮箱地址')
    ])

    subject = SelectField('主题', choices=[
        ('general', '一般咨询'),
        ('support', '技术支持'),
        ('business', '商务合作'),
        ('complaint', '投诉建议'),
        ('other', '其他')
    ], validators=[DataRequired(message='请选择主题')])

    message = TextAreaField('留言内容', validators=[
        DataRequired(message='请输入留言内容'),
        Length(min=10, max=2000, message='内容长度应在10-2000个字符之间')
    ])

    submit = SubmitField('发送消息')

class FeedbackStatusForm(FlaskForm):
    """用户反馈状态更新表单"""
    status = SelectField('反馈状态', choices=[
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('waiting_user', '等待我回复'),
        ('resolved', '已解决'),
        ('closed', '已关闭')
    ], validators=[DataRequired(message='请选择反馈状态')])

    user_note = TextAreaField('备注说明', validators=[
        Length(max=500, message='备注长度不能超过500个字符')
    ])

    submit = SubmitField('更新状态')
