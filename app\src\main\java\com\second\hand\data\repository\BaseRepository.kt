package com.second.hand.data.repository

import com.second.hand.data.api.ApiHelper
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.model.BaseResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Response

/**
 * Repository基础类
 * 提供通用的数据处理方法
 */
abstract class BaseRepository {

    /**
     * 安全的API调用包装器
     * 使用新的NetworkResult和ApiHelper
     */
    protected suspend fun <T> safeApiCall(
        apiCall: suspend () -> Response<BaseResponse<T>>
    ): NetworkResult<T> {
        return withContext(Dispatchers.IO) {
            ApiHelper.safeApiCall(apiCall)
        }
    }

    /**
     * 安全的API调用包装器（无数据返回）
     */
    protected suspend fun safeApiCallNoData(
        apiCall: suspend () -> Response<BaseResponse<Any>>
    ): NetworkResult<Boolean> {
        return withContext(Dispatchers.IO) {
            ApiHelper.safeApiCallNoData(apiCall)
        }
    }

    /**
     * 安全的API调用包装器（列表数据）
     */
    protected suspend fun <T> safeApiCallList(
        apiCall: suspend () -> Response<BaseResponse<List<T>>>
    ): NetworkResult<List<T>> {
        return withContext(Dispatchers.IO) {
            ApiHelper.safeApiCallList(apiCall)
        }
    }

    /**
     * 将NetworkResult转换为Result（兼容旧代码）
     */
    protected fun <T> NetworkResult<T>.toResult(): Result<T> {
        return when (this) {
            is NetworkResult.Success -> Result.success(data)
            is NetworkResult.Error -> Result.failure(Exception(message))
            is NetworkResult.Loading -> Result.failure(Exception("请求正在处理中"))
        }
    }
}
