package com.second.hand.ui.search

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.second.hand.data.model.Product
import com.second.hand.ui.components.ProductCard
import com.second.hand.ui.common.LoadingIndicator
import com.second.hand.ui.common.ErrorMessage
import com.second.hand.ui.common.EmptyState
import com.second.hand.ui.navigation.NavigationDestinations
import com.second.hand.ui.product.ProductListViewModel

/**
 * 搜索页面
 * 提供商品搜索功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchScreen(
    navController: NavController,
    viewModel: ProductListViewModel = hiltViewModel()
) {
    val products by viewModel.products.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val uiState by viewModel.uiState.collectAsState()

    var searchText by remember { mutableStateOf("") }
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current

    // 自动聚焦搜索框
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 搜索栏
        TopAppBar(
            title = {
                OutlinedTextField(
                    value = searchText,
                    onValueChange = { searchText = it },
                    placeholder = { Text("搜索商品...") },
                    leadingIcon = {
                        Icon(Icons.Default.Search, contentDescription = "搜索")
                    },
                    trailingIcon = {
                        if (searchText.isNotEmpty()) {
                            IconButton(
                                onClick = {
                                    searchText = ""
                                    viewModel.clearSearch()
                                }
                            ) {
                                Icon(Icons.Default.Clear, contentDescription = "清除")
                            }
                        }
                    },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Search
                    ),
                    keyboardActions = KeyboardActions(
                        onSearch = {
                            if (searchText.isNotBlank()) {
                                viewModel.searchProducts(searchText.trim())
                                keyboardController?.hide()
                            }
                        }
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester)
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.navigateUp() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )

        // 搜索结果
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            when {
                isLoading && products.isEmpty() -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center),
                        message = "搜索中..."
                    )
                }
                error != null && products.isEmpty() -> {
                    ErrorMessage(
                        message = error ?: "搜索失败",
                        modifier = Modifier.align(Alignment.Center),
                        onRetry = {
                            if (searchText.isNotBlank()) {
                                viewModel.searchProducts(searchText.trim())
                            }
                        }
                    )
                }
                searchQuery.isBlank() -> {
                    // 搜索提示
                    SearchHint(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                products.isEmpty() -> {
                    // 无搜索结果
                    EmptyState(
                        message = "未找到相关商品",
                        icon = "🔍",
                        actionText = "换个关键词试试",
                        onAction = {
                            searchText = ""
                            focusRequester.requestFocus()
                        },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                else -> {
                    // 搜索结果列表
                    SearchResultList(
                        products = products,
                        searchQuery = searchQuery,
                        isLoadingMore = uiState.isLoadingMore,
                        hasMorePages = uiState.hasMorePages,
                        onProductClick = { product ->
                            viewModel.incrementViewCount(product.id)
                            navController.navigate(NavigationDestinations.productDetail(product.id.toString()))
                        },
                        onFavoriteClick = { productId ->
                            viewModel.toggleFavorite(productId)
                        },
                        onLoadMore = { viewModel.loadMoreProducts() }
                    )
                }
            }
        }
    }
}

@Composable
private fun SearchHint(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "🔍",
            style = MaterialTheme.typography.displayLarge
        )
        
        Text(
            text = "输入关键词搜索商品",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = "支持商品名称、描述等关键词搜索",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun SearchResultList(
    products: List<Product>,
    searchQuery: String,
    isLoadingMore: Boolean,
    hasMorePages: Boolean,
    onProductClick: (Product) -> Unit,
    onFavoriteClick: (Int) -> Unit,
    onLoadMore: () -> Unit
) {
    LazyColumn(
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 搜索结果提示
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Text(
                    text = "搜索 \"$searchQuery\" 找到 ${products.size} 个结果",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.padding(12.dp)
                )
            }
        }

        // 商品列表
        items(products) { product ->
            ProductCard(
                product = product,
                onProductClick = onProductClick,
                onFavoriteClick = onFavoriteClick,
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 加载更多指示器
        if (isLoadingMore) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        }
    }

    // 检测滚动到底部
    LaunchedEffect(products.size) {
        // 简单的加载更多逻辑，实际应该基于滚动位置
        if (products.isNotEmpty() && hasMorePages && !isLoadingMore) {
            // 这里可以添加更复杂的滚动检测逻辑
        }
    }
}
