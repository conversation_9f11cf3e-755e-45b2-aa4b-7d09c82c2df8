package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import com.second.hand.data.model.enums.NotificationType

/**
 * 通知数据模型
 * 对应Flask后端的Notification模型
 */
@Entity(tableName = "notifications")
data class Notification(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    // 外键
    @SerializedName("user_id")
    var userId: Int = 0,

    // 通知内容
    @SerializedName("title")
    var title: String = "",

    @SerializedName("content")
    var content: String = "",

    @SerializedName("type")
    var type: NotificationType = NotificationType.SYSTEM,

    @SerializedName("is_read")
    var isRead: Boolean = false,

    // 相关对象ID（可选）
    @SerializedName("related_id")
    var relatedId: Int? = null,

    // 时间戳
    @SerializedName("created_at")
    var createdAt: String = "",
    
    // 关联数据（API返回时可能包含，不存储到数据库）
    @Ignore
    @SerializedName("user")
    val user: User? = null
) {
    /**
     * 获取通知图标
     */
    fun getIcon(): String {
        return when (type) {
            NotificationType.ORDER -> "🛒"
            NotificationType.SYSTEM -> "⚙️"
            NotificationType.MESSAGE -> "💬"
            NotificationType.REVIEW -> "⭐"
            NotificationType.PAYMENT -> "💳"
            NotificationType.SHIPPING -> "🚚"
        }
    }
    
    /**
     * 获取通知颜色
     */
    fun getColor(): String {
        return when (type) {
            NotificationType.ORDER -> "#2196F3"      // 蓝色
            NotificationType.SYSTEM -> "#607D8B"     // 灰蓝色
            NotificationType.MESSAGE -> "#4CAF50"    // 绿色
            NotificationType.REVIEW -> "#FF9800"     // 橙色
            NotificationType.PAYMENT -> "#F44336"    // 红色
            NotificationType.SHIPPING -> "#9C27B0"   // 紫色
        }
    }
    
    /**
     * 获取类型显示文本
     */
    fun getTypeDisplay(): String {
        return when (type) {
            NotificationType.ORDER -> "订单通知"
            NotificationType.SYSTEM -> "系统通知"
            NotificationType.MESSAGE -> "消息通知"
            NotificationType.REVIEW -> "评价通知"
            NotificationType.PAYMENT -> "支付通知"
            NotificationType.SHIPPING -> "物流通知"
        }
    }
}
