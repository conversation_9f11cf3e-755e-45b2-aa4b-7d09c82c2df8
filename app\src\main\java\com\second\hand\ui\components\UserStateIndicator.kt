package com.second.hand.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.second.hand.data.model.User
import com.second.hand.data.user.UserState

/**
 * 用户状态指示器
 * 显示用户登录状态和基本信息
 */
@Composable
fun UserStateIndicator(
    userState: UserState,
    modifier: Modifier = Modifier,
    showDetails: Boolean = true,
    onUserClick: (() -> Unit)? = null
) {
    val cardModifier = if (onUserClick != null) {
        modifier.fillMaxWidth()
    } else {
        modifier.fillMaxWidth()
    }

    Card(
        modifier = cardModifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (userState.isLoggedIn)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 用户头像/状态图标
            UserAvatar(
                user = userState.user,
                isLoggedIn = userState.isLoggedIn,
                size = 48.dp
            )
            
            // 用户信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                if (userState.isLoggedIn && userState.user != null) {
                    // 已登录状态
                    Text(
                        text = userState.user.nickname ?: userState.user.username ?: "用户",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    
                    if (showDetails) {
                        Text(
                            text = "ID: ${userState.user.id} • ${userState.user.role.name}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                        )
                        
                        userState.user.email?.let { email ->
                            Text(
                                text = email,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                            )
                        }
                    }
                } else {
                    // 未登录状态
                    Text(
                        text = "未登录",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    if (showDetails) {
                        Text(
                            text = "点击登录以使用完整功能",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                        )
                    }
                }
            }
            
            // 状态指示器
            UserStatusBadge(userState.isLoggedIn)
        }
    }
}

/**
 * 用户头像组件
 */
@Composable
fun UserAvatar(
    user: User?,
    isLoggedIn: Boolean,
    size: androidx.compose.ui.unit.Dp = 40.dp,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        if (isLoggedIn) {
            // TODO: 如果有头像URL，可以使用AsyncImage加载
            // 目前使用默认图标
            Surface(
                modifier = Modifier.fillMaxSize(),
                shape = CircleShape,
                color = MaterialTheme.colorScheme.primary
            ) {
                Icon(
                    Icons.Default.Person,
                    contentDescription = "用户头像",
                    tint = MaterialTheme.colorScheme.onPrimary,
                    modifier = Modifier.padding(8.dp)
                )
            }
        } else {
            Surface(
                modifier = Modifier.fillMaxSize(),
                shape = CircleShape,
                color = MaterialTheme.colorScheme.outline
            ) {
                Icon(
                    Icons.Default.Person,
                    contentDescription = "未登录",
                    tint = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(8.dp)
                )
            }
        }
    }
}

/**
 * 用户状态徽章
 */
@Composable
fun UserStatusBadge(
    isLoggedIn: Boolean,
    modifier: Modifier = Modifier
) {
    val (color, text) = if (isLoggedIn) {
        Color(0xFF4CAF50) to "在线"
    } else {
        Color(0xFF9E9E9E) to "离线"
    }
    
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

/**
 * 简化的用户状态指示器
 */
@Composable
fun SimpleUserStateIndicator(
    userState: UserState,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        UserAvatar(
            user = userState.user,
            isLoggedIn = userState.isLoggedIn,
            size = 24.dp
        )
        
        Text(
            text = if (userState.isLoggedIn) {
                userState.user?.nickname ?: userState.user?.username ?: "用户"
            } else {
                "未登录"
            },
            style = MaterialTheme.typography.bodyMedium,
            color = if (userState.isLoggedIn) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.onSurfaceVariant
            }
        )
    }
}

/**
 * 用户状态卡片（详细版）
 */
@Composable
fun UserStateCard(
    userState: UserState,
    onLoginClick: () -> Unit = {},
    onProfileClick: () -> Unit = {},
    onLogoutClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "用户状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            UserStateIndicator(
                userState = userState,
                showDetails = true
            )
            
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                if (userState.isLoggedIn) {
                    Button(
                        onClick = onProfileClick,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("个人资料")
                    }
                    
                    OutlinedButton(
                        onClick = onLogoutClick,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("登出")
                    }
                } else {
                    Button(
                        onClick = onLoginClick,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("登录")
                    }
                }
            }
        }
    }
}
