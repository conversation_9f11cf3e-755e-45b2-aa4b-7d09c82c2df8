package com.second.hand.data.auth

import android.content.Context
import android.content.SharedPreferences
import android.util.Base64
import com.second.hand.data.preferences.PreferencesManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.json.JSONObject
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

/**
 * JWT Token管理器
 * 负责Token的存储、验证、刷新等核心功能
 */
@Singleton
class TokenManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val preferencesManager: PreferencesManager
) {
    
    companion object {
        private const val KEY_TOKEN_EXPIRY = "token_expiry"
        private const val KEY_USER_INFO = "user_info"
        private const val KEY_LAST_REFRESH = "last_refresh"
        private const val KEY_REFRESH_COUNT = "refresh_count"
        private const val KEY_LAST_REFRESH_ATTEMPT = "last_refresh_attempt"

        // Token过期前提前刷新的时间（5分钟）
        private const val REFRESH_THRESHOLD_MS = 5 * 60 * 1000L

        // 刷新重试间隔（30秒）
        private const val REFRESH_RETRY_INTERVAL_MS = 30 * 1000L

        // 最大刷新重试次数
        private const val MAX_REFRESH_RETRIES = 3
    }
    
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("token_prefs", Context.MODE_PRIVATE)

    // Token状态流
    private val _tokenState = MutableStateFlow(TokenState())
    val tokenState: StateFlow<TokenState> = _tokenState.asStateFlow()

    // 刷新状态流
    private val _refreshState = MutableStateFlow(TokenRefreshState())
    val refreshState: StateFlow<TokenRefreshState> = _refreshState.asStateFlow()

    // 并发控制
    private val refreshMutex = Mutex()
    private val isRefreshing = AtomicBoolean(false)
    
    /**
     * 保存Token信息
     */
    fun saveTokens(accessToken: String, refreshToken: String, userInfo: Map<String, Any>? = null) {
        try {
            // 保存到PreferencesManager（保持兼容性）
            preferencesManager.saveAccessToken(accessToken)
            preferencesManager.saveRefreshToken(refreshToken)
            preferencesManager.setLoggedIn(true)
            
            // 解析Token过期时间
            val expiryTime = parseTokenExpiry(accessToken)
            
            // 保存到TokenManager专用存储
            sharedPreferences.edit().apply {
                putLong(KEY_TOKEN_EXPIRY, expiryTime)
                putLong(KEY_LAST_REFRESH, System.currentTimeMillis())
                userInfo?.let { info ->
                    putString(KEY_USER_INFO, JSONObject(info).toString())
                }
                apply()
            }
            
            // 更新Token状态
            updateTokenState()
            
            println("🔐 Token保存成功 - 过期时间: ${Date(expiryTime)}")
            
        } catch (e: Exception) {
            println("🔐 Token保存失败: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 获取访问Token
     */
    fun getAccessToken(): String? {
        return preferencesManager.getAccessToken()
    }
    
    /**
     * 获取刷新Token
     */
    fun getRefreshToken(): String? {
        return preferencesManager.getRefreshToken()
    }
    
    /**
     * 检查Token是否有效
     */
    fun isTokenValid(): Boolean {
        val accessToken = getAccessToken()
        if (accessToken.isNullOrEmpty()) {
            return false
        }
        
        val expiryTime = sharedPreferences.getLong(KEY_TOKEN_EXPIRY, 0L)
        val currentTime = System.currentTimeMillis()
        
        return currentTime < expiryTime
    }
    
    /**
     * 检查是否需要刷新Token
     */
    fun shouldRefreshToken(): Boolean {
        val accessToken = getAccessToken()
        if (accessToken.isNullOrEmpty()) {
            return false
        }
        
        val expiryTime = sharedPreferences.getLong(KEY_TOKEN_EXPIRY, 0L)
        val currentTime = System.currentTimeMillis()
        
        // 如果Token在5分钟内过期，则需要刷新
        return (expiryTime - currentTime) <= REFRESH_THRESHOLD_MS
    }
    
    /**
     * 获取Token过期时间
     */
    fun getTokenExpiryTime(): Long {
        return sharedPreferences.getLong(KEY_TOKEN_EXPIRY, 0L)
    }
    
    /**
     * 获取用户信息
     */
    fun getUserInfo(): Map<String, Any>? {
        return try {
            val userInfoJson = sharedPreferences.getString(KEY_USER_INFO, null)
            if (userInfoJson != null) {
                val jsonObject = JSONObject(userInfoJson)
                val map = mutableMapOf<String, Any>()
                jsonObject.keys().forEach { key ->
                    map[key] = jsonObject.get(key)
                }
                map
            } else {
                null
            }
        } catch (e: Exception) {
            println("🔐 获取用户信息失败: ${e.message}")
            null
        }
    }
    
    /**
     * 检查登录状态
     */
    fun isLoggedIn(): Boolean {
        return preferencesManager.isLoggedIn() && isTokenValid()
    }
    
    /**
     * 清除所有Token信息
     */
    fun clearTokens() {
        preferencesManager.clearAll()
        sharedPreferences.edit().clear().apply()
        
        // 更新Token状态
        _tokenState.value = TokenState()
        
        println("🔐 Token已清除")
    }
    
    /**
     * 获取Token剩余有效时间（毫秒）
     */
    fun getTokenRemainingTime(): Long {
        val expiryTime = getTokenExpiryTime()
        val currentTime = System.currentTimeMillis()
        return maxOf(0L, expiryTime - currentTime)
    }
    
    /**
     * 解析JWT Token的过期时间
     */
    private fun parseTokenExpiry(token: String): Long {
        return try {
            val parts = token.split(".")
            if (parts.size >= 2) {
                val payload = String(Base64.decode(parts[1], Base64.URL_SAFE))
                val jsonObject = JSONObject(payload)
                val exp = jsonObject.optLong("exp", 0L)
                
                // JWT的exp是秒级时间戳，需要转换为毫秒
                if (exp > 0) {
                    exp * 1000L
                } else {
                    // 如果无法解析，默认1小时后过期
                    System.currentTimeMillis() + 60 * 60 * 1000L
                }
            } else {
                // Token格式不正确，默认1小时后过期
                System.currentTimeMillis() + 60 * 60 * 1000L
            }
        } catch (e: Exception) {
            println("🔐 解析Token过期时间失败: ${e.message}")
            // 解析失败，默认1小时后过期
            System.currentTimeMillis() + 60 * 60 * 1000L
        }
    }
    
    /**
     * 更新Token状态
     */
    private fun updateTokenState() {
        val accessToken = getAccessToken()
        val refreshToken = getRefreshToken()
        val expiryTime = getTokenExpiryTime()
        val isValid = isTokenValid()
        val shouldRefresh = shouldRefreshToken()
        
        _tokenState.value = TokenState(
            hasAccessToken = !accessToken.isNullOrEmpty(),
            hasRefreshToken = !refreshToken.isNullOrEmpty(),
            isValid = isValid,
            shouldRefresh = shouldRefresh,
            expiryTime = expiryTime,
            remainingTime = getTokenRemainingTime()
        )
    }
    
    /**
     * 手动更新Token状态（用于外部调用）
     */
    fun refreshTokenState() {
        updateTokenState()
    }

    /**
     * 检查是否可以重试刷新
     */
    fun canRetryRefresh(): Boolean {
        val lastAttempt = sharedPreferences.getLong(KEY_LAST_REFRESH_ATTEMPT, 0L)
        val currentTime = System.currentTimeMillis()
        val refreshCount = sharedPreferences.getInt(KEY_REFRESH_COUNT, 0)

        return refreshCount < MAX_REFRESH_RETRIES &&
               (currentTime - lastAttempt) >= REFRESH_RETRY_INTERVAL_MS
    }

    /**
     * 记录刷新尝试
     */
    private fun recordRefreshAttempt(success: Boolean, error: String? = null) {
        val currentTime = System.currentTimeMillis()
        val refreshCount = if (success) 0 else sharedPreferences.getInt(KEY_REFRESH_COUNT, 0) + 1

        sharedPreferences.edit().apply {
            putLong(KEY_LAST_REFRESH_ATTEMPT, currentTime)
            putInt(KEY_REFRESH_COUNT, refreshCount)
            if (success) {
                putLong(KEY_LAST_REFRESH, currentTime)
            }
            apply()
        }

        // 更新刷新状态
        _refreshState.value = _refreshState.value.copy(
            isRefreshing = false,
            lastRefreshTime = if (success) currentTime else _refreshState.value.lastRefreshTime,
            refreshCount = refreshCount,
            lastRefreshSuccess = success,
            lastRefreshError = error,
            canRetryRefresh = refreshCount < MAX_REFRESH_RETRIES,
            nextRetryTime = if (!success) currentTime + REFRESH_RETRY_INTERVAL_MS else 0L
        )
    }

    /**
     * 设置刷新状态
     */
    private fun setRefreshingState(isRefreshing: Boolean) {
        _refreshState.value = _refreshState.value.copy(isRefreshing = isRefreshing)
    }
}

/**
 * Token刷新状态数据类
 * 用于表示Token刷新过程的状态
 */
data class TokenRefreshState(
    val isRefreshing: Boolean = false,
    val lastRefreshTime: Long = 0L,
    val refreshCount: Int = 0,
    val lastRefreshSuccess: Boolean = true,
    val lastRefreshError: String? = null,
    val canRetryRefresh: Boolean = true,
    val nextRetryTime: Long = 0L
)

/**
 * Token状态数据类
 */
data class TokenState(
    val hasAccessToken: Boolean = false,
    val hasRefreshToken: Boolean = false,
    val isValid: Boolean = false,
    val shouldRefresh: Boolean = false,
    val expiryTime: Long = 0L,
    val remainingTime: Long = 0L
)
