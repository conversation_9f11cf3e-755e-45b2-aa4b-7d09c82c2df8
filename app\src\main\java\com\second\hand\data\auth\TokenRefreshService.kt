package com.second.hand.data.auth

import com.second.hand.data.api.NetworkResult
import com.second.hand.data.repository.AuthRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Token刷新服务
 * 提供高级的Token刷新策略和用户反馈
 */
@Singleton
class TokenRefreshService @Inject constructor(
    private val tokenManager: TokenManager,
    private val authRepository: AuthRepository
) {
    
    // 刷新状态流
    private val _refreshStatus = MutableStateFlow(RefreshStatus.IDLE)
    val refreshStatus: StateFlow<RefreshStatus> = _refreshStatus.asStateFlow()
    
    // 用户消息流
    private val _userMessage = MutableStateFlow<String?>(null)
    val userMessage: StateFlow<String?> = _userMessage.asStateFlow()
    
    private var refreshJob: Job? = null
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    /**
     * 主动刷新Token
     */
    suspend fun refreshToken(reason: String = "手动刷新"): Boolean {
        if (_refreshStatus.value == RefreshStatus.REFRESHING) {
            println("🔄 Token刷新已在进行中")
            return false
        }
        
        return try {
            _refreshStatus.value = RefreshStatus.REFRESHING
            _userMessage.value = "正在刷新登录状态..."
            
            println("🔄 开始Token刷新 - 原因: $reason")
            
            when (val result = authRepository.refreshToken()) {
                is NetworkResult.Success -> {
                    _refreshStatus.value = RefreshStatus.SUCCESS
                    _userMessage.value = null
                    println("🔄 Token刷新成功")
                    true
                }
                is NetworkResult.Error -> {
                    _refreshStatus.value = RefreshStatus.FAILED
                    _userMessage.value = "登录状态刷新失败: ${result.message}"
                    println("🔄 Token刷新失败: ${result.message}")
                    
                    // 如果是认证错误，清除Token
                    if (result.message.contains("无效") || result.message.contains("过期")) {
                        tokenManager.clearTokens()
                        _userMessage.value = "登录已过期，请重新登录"
                    }
                    false
                }
                is NetworkResult.Loading -> false
            }
        } catch (e: Exception) {
            _refreshStatus.value = RefreshStatus.FAILED
            _userMessage.value = "登录状态刷新异常"
            println("🔄 Token刷新异常: ${e.message}")
            false
        } finally {
            // 延迟清除状态，让UI有时间显示结果
            delay(2000)
            if (_refreshStatus.value != RefreshStatus.REFRESHING) {
                _refreshStatus.value = RefreshStatus.IDLE
            }
        }
    }
    
    /**
     * 启动自动刷新监控
     */
    fun startAutoRefreshMonitoring() {
        refreshJob?.cancel()
        refreshJob = serviceScope.launch {
            while (isActive) {
                try {
                    // 检查是否需要刷新
                    if (tokenManager.shouldRefreshToken() && tokenManager.canRetryRefresh()) {
                        println("🔄 自动刷新监控触发")
                        refreshToken("自动监控")
                    }
                    
                    // 每30秒检查一次
                    delay(30_000)
                } catch (e: Exception) {
                    println("🔄 自动刷新监控异常: ${e.message}")
                    delay(60_000) // 异常时延长检查间隔
                }
            }
        }
    }
    
    /**
     * 停止自动刷新监控
     */
    fun stopAutoRefreshMonitoring() {
        refreshJob?.cancel()
        refreshJob = null
    }
    
    /**
     * 清除用户消息
     */
    fun clearUserMessage() {
        _userMessage.value = null
    }
    
    /**
     * 检查Token健康状态
     */
    fun checkTokenHealth(): TokenHealthStatus {
        val tokenState = tokenManager.tokenState.value
        val refreshState = tokenManager.refreshState.value
        
        return when {
            !tokenState.hasAccessToken -> TokenHealthStatus.NO_TOKEN
            !tokenState.isValid -> TokenHealthStatus.EXPIRED
            tokenState.shouldRefresh -> TokenHealthStatus.NEEDS_REFRESH
            refreshState.refreshCount >= 3 -> TokenHealthStatus.REFRESH_FAILED
            else -> TokenHealthStatus.HEALTHY
        }
    }
    
    /**
     * 获取Token健康状态描述
     */
    fun getTokenHealthDescription(): String {
        return when (checkTokenHealth()) {
            TokenHealthStatus.HEALTHY -> "登录状态正常"
            TokenHealthStatus.NEEDS_REFRESH -> "登录状态即将过期，正在自动刷新"
            TokenHealthStatus.EXPIRED -> "登录已过期"
            TokenHealthStatus.NO_TOKEN -> "未登录"
            TokenHealthStatus.REFRESH_FAILED -> "登录状态刷新失败，请重新登录"
        }
    }
    
    /**
     * 强制刷新（忽略重试限制）
     */
    suspend fun forceRefresh(): Boolean {
        // 重置刷新计数
        tokenManager.clearTokens()
        
        // 重新获取Token（需要重新登录）
        _userMessage.value = "登录已过期，请重新登录"
        return false
    }
}

/**
 * 刷新状态枚举
 */
enum class RefreshStatus {
    IDLE,       // 空闲
    REFRESHING, // 刷新中
    SUCCESS,    // 刷新成功
    FAILED      // 刷新失败
}

/**
 * Token健康状态枚举
 */
enum class TokenHealthStatus {
    HEALTHY,        // 健康
    NEEDS_REFRESH,  // 需要刷新
    EXPIRED,        // 已过期
    NO_TOKEN,       // 无Token
    REFRESH_FAILED  // 刷新失败
}
