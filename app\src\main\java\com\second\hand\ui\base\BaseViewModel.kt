package com.second.hand.ui.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel基础类
 * 提供通用的状态管理和错误处理
 */
abstract class BaseViewModel : ViewModel() {
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 错误状态
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    /**
     * 设置加载状态
     */
    protected fun setLoading(loading: Boolean) {
        _isLoading.value = loading
    }
    
    /**
     * 设置错误信息
     */
    protected fun setError(error: String?) {
        _error.value = error
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * 安全执行协程操作
     * 自动处理加载状态和异常
     */
    protected fun launchSafely(
        showLoading: Boolean = true,
        onError: ((String) -> Unit)? = null,
        block: suspend () -> Unit
    ) {
        viewModelScope.launch {
            try {
                if (showLoading) setLoading(true)
                clearError()
                block()
            } catch (e: Exception) {
                val errorMessage = e.message ?: "未知错误"
                setError(errorMessage)
                onError?.invoke(errorMessage)
            } finally {
                if (showLoading) setLoading(false)
            }
        }
    }
}
