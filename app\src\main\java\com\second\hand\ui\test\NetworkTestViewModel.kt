package com.second.hand.ui.test

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.second.hand.BuildConfig
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.repository.TestRepository
import com.second.hand.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 网络测试ViewModel
 */
@HiltViewModel
class NetworkTestViewModel @Inject constructor(
    private val testRepository: TestRepository
) : BaseViewModel() {
    
    private val _uiState = MutableStateFlow(NetworkTestUiState())
    val uiState: StateFlow<NetworkTestUiState> = _uiState.asStateFlow()
    
    init {
        _uiState.value = _uiState.value.copy(
            baseUrl = BuildConfig.BASE_URL,
            imageUrl = BuildConfig.IMAGE_BASE_URL
        )
    }
    
    /**
     * 简单连接测试
     */
    fun testSimpleConnection() {
        launchSafely {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                testResult = "",
                logs = mutableListOf("开始简单连接测试...")
            )

            // 先测试基本网络连通性
            addLog("1. 测试基本网络连通性...")
            when (val connectivityResult = testRepository.networkConnectivityTest()) {
                is NetworkResult.Success -> {
                    addLog("✓ 基本网络连通性测试成功")
                    addLog("详情: ${connectivityResult.data}")

                    // 再测试API调用
                    addLog("2. 测试API调用...")
                    when (val apiResult = testRepository.simpleConnectionTest()) {
                        is NetworkResult.Success -> {
                            addLog("✓ API调用测试成功")
                            addLog("响应: ${apiResult.data}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                testResult = "✓ 所有测试成功！网络连接正常"
                            )
                        }
                        is NetworkResult.Error -> {
                            addLog("✗ API调用测试失败")
                            addLog("错误: ${apiResult.message}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                testResult = "⚠ 网络连通但API调用失败: ${apiResult.message}"
                            )
                        }
                        is NetworkResult.Loading -> {
                            addLog("API调用测试中...")
                        }
                    }
                }
                is NetworkResult.Error -> {
                    addLog("✗ 基本网络连通性测试失败")
                    addLog("错误: ${connectivityResult.message}")
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        testResult = "✗ 网络连通性测试失败: ${connectivityResult.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    addLog("网络连通性测试中...")
                }
            }
        }
    }
    
    /**
     * 完整连接测试
     */
    fun testFullConnection() {
        launchSafely {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                testResult = "",
                logs = mutableListOf("开始完整连接测试...")
            )
            
            // 测试商品列表
            addLog("1. 测试商品列表API...")
            when (val result = testRepository.getProducts()) {
                is NetworkResult.Success -> {
                    addLog("✓ 商品列表API测试成功")
                    addLog("获取到 ${result.data.items.size} 个商品")
                    
                    // 测试分类列表
                    addLog("2. 测试分类列表API...")
                    when (val categoryResult = testRepository.getCategories()) {
                        is NetworkResult.Success -> {
                            addLog("✓ 分类列表API测试成功")
                            addLog("获取到 ${categoryResult.data.size} 个分类")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                testResult = "✓ 完整连接测试成功！所有API都正常工作"
                            )
                        }
                        is NetworkResult.Error -> {
                            addLog("✗ 分类列表API测试失败: ${categoryResult.message}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                testResult = "⚠ 部分测试失败：商品API正常，分类API失败"
                            )
                        }
                        is NetworkResult.Loading -> {
                            addLog("分类API测试中...")
                        }
                    }
                }
                is NetworkResult.Error -> {
                    addLog("✗ 商品列表API测试失败: ${result.message}")
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        testResult = "✗ 完整连接测试失败: ${result.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    addLog("商品API测试中...")
                }
            }
        }
    }
    
    /**
     * 清除日志
     */
    fun clearLogs() {
        _uiState.value = _uiState.value.copy(
            logs = mutableListOf(),
            testResult = ""
        )
    }
    
    /**
     * 添加日志
     */
    private fun addLog(message: String) {
        val currentLogs = _uiState.value.logs.toMutableList()
        currentLogs.add("[${System.currentTimeMillis() % 100000}] $message")
        _uiState.value = _uiState.value.copy(logs = currentLogs)
        
        // 同时输出到控制台
        println("NetworkTest: $message")
    }
}

/**
 * 网络测试UI状态
 */
data class NetworkTestUiState(
    val isLoading: Boolean = false,
    val testResult: String = "",
    val logs: List<String> = emptyList(),
    val baseUrl: String = "",
    val imageUrl: String = ""
)
