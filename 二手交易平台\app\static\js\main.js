// 主要JavaScript功能

$(document).ready(function() {
    // 初始化页面效果
    initializePageEffects();
    initializeScrollEffects();
    initializeButtonEffects();
    initializeCardAnimations();

    // 初始化传统轮询系统
    if ($('body').hasClass('authenticated')) {
        // 使用传统的AJAX轮询来更新通知和消息
        startPolling();

        // 启动实时通知更新
        startRealtimeNotifications();

        // 页面即将关闭时发送离线信号
        window.addEventListener('beforeunload', function(event) {
            navigator.sendBeacon('/api/user/offline');
        });
    }

    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 收藏功能
    $(document).on('click', '.favorite-btn', function(e) {
        e.preventDefault();
        var btn = $(this);
        var productId = btn.data('product-id');

        // 防止重复点击
        if (btn.hasClass('processing')) {
            return;
        }

        // 设置处理状态
        btn.addClass('processing');
        var originalHtml = btn.html();
        btn.html('<i class="fas fa-spinner fa-spin"></i>');
        btn.prop('disabled', true);

        $.ajax({
            url: '/products/toggle_favorite/' + productId,
            method: 'POST',
            data: {
                csrf_token: $('meta[name=csrf-token]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    if (response.is_favorited) {
                        btn.removeClass('btn-outline-danger').addClass('btn-danger');
                        btn.find('i').removeClass('far').addClass('fas');
                    } else {
                        btn.removeClass('btn-danger').addClass('btn-outline-danger');
                        btn.find('i').removeClass('fas').addClass('far');
                    }

                    // 更新收藏数量
                    var favoriteCount = btn.find('.favorite-count');
                    if (favoriteCount.length > 0) {
                        favoriteCount.text(response.favorite_count);
                    }

                    // 显示消息
                    showAlert(response.message, 'success');
                } else {
                    showAlert('操作失败：' + response.message, 'error');
                }
            },
            error: function(xhr) {
                console.error('Favorite error:', xhr);
                if (xhr.status === 401) {
                    showAlert('请先登录', 'warning');
                    window.location.href = '/auth/login';
                } else {
                    showAlert('操作失败，请重试', 'error');
                }
            },
            complete: function() {
                // 恢复按钮状态
                btn.removeClass('processing');
                btn.prop('disabled', false);

                // 如果没有成功更新，恢复原始HTML
                if (!btn.find('i').hasClass('fa-heart')) {
                    btn.html(originalHtml);
                }
            }
        });
    });

    // 图片上传预览
    $('input[type="file"][multiple]').change(function() {
        var input = this;
        var preview = $(input).siblings('.image-preview-container');
        
        if (input.files) {
            preview.empty();
            
            Array.from(input.files).forEach(function(file, index) {
                if (file.type.startsWith('image/')) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var imageHtml = `
                            <div class="image-preview" data-index="${index}">
                                <img src="${e.target.result}" alt="预览">
                                <button type="button" class="remove-image" onclick="removeImagePreview(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        `;
                        preview.append(imageHtml);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    });

    // 搜索建议
    var searchInput = $('input[name="q"]');
    var suggestionsContainer = $('<div class="search-suggestions"></div>');
    searchInput.after(suggestionsContainer);
    
    searchInput.on('input', debounce(function() {
        var query = $(this).val().trim();
        
        if (query.length >= 2) {
            $.ajax({
                url: '/api/search/suggestions',
                data: { q: query },
                success: function(response) {
                    showSearchSuggestions(response.suggestions);
                }
            });
        } else {
            suggestionsContainer.hide();
        }
    }, 300));
    
    // 点击其他地方隐藏搜索建议
    $(document).click(function(e) {
        if (!$(e.target).closest('.search-suggestions, input[name="q"]').length) {
            suggestionsContainer.hide();
        }
    });

    // 通知功能 - 现在由实时系统处理
    // 只在实时系统不可用时作为后备
    if (!window.realtimeSystem || !window.realtimeSystem.isConnected) {
        loadNotifications();
        setInterval(loadNotifications, 10000);
    }

    // 标记通知为已读
    $(document).on('click', '.notification-item', function() {
        var notificationId = $(this).data('notification-id');
        if (!$(this).hasClass('read')) {
            markNotificationRead(notificationId);
        }
    });

    // 表单验证增强
    $('form').submit(function(e) {
        var $form = $(this);
        var submitBtn = $form.find('button[type="submit"], input[type="submit"]');

        // 避免重复提交
        if (submitBtn.prop('disabled')) {
            e.preventDefault();
            return false;
        }

        // 禁用按钮并显示加载状态
        submitBtn.prop('disabled', true);
        var originalText = submitBtn.data('original-text') || submitBtn.text() || submitBtn.val();

        if (submitBtn.is('button')) {
            submitBtn.html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>提交中...');
        } else {
            submitBtn.val('提交中...');
        }

        // 5秒后重新启用按钮（防止网络问题导致按钮永久禁用）
        setTimeout(function() {
            submitBtn.prop('disabled', false);
            if (submitBtn.is('button')) {
                submitBtn.html(originalText);
            } else {
                submitBtn.val(originalText);
            }
        }, 5000);
    });

    // 保存按钮原始文本
    $('button[type="submit"], input[type="submit"]').each(function() {
        var $btn = $(this);
        if ($btn.is('button')) {
            $btn.data('original-text', $btn.text());
        } else {
            $btn.data('original-text', $btn.val());
        }
    });
});

// 显示搜索建议
function showSearchSuggestions(suggestions) {
    var suggestionsContainer = $('.search-suggestions');
    suggestionsContainer.empty();
    
    if (suggestions.length > 0) {
        suggestions.forEach(function(suggestion) {
            var item = $('<div class="suggestion-item"></div>').text(suggestion);
            item.click(function() {
                $('input[name="q"]').val(suggestion);
                suggestionsContainer.hide();
                $('form').submit();
            });
            suggestionsContainer.append(item);
        });
        suggestionsContainer.show();
    } else {
        suggestionsContainer.hide();
    }
}

// 移除图片预览
function removeImagePreview(button) {
    $(button).closest('.image-preview').remove();
}

// 显示警告消息
function showAlert(message, type) {
    var alertClass = type === 'error' ? 'alert-danger' : 'alert-' + type;
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 80px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 将消息提示添加到body中，避免影响导航栏
    $('body').append(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').last().alert('close');
    }, 3000);
}

// 加载通知
function loadNotifications() {
    if (!$('body').hasClass('authenticated')) return;
    
    $.ajax({
        url: '/api/notifications',
        data: { per_page: 5 },
        success: function(response) {
            updateNotificationBadgeFromArray(response.notifications);
        }
    });
}

// 更新通知徽章和下拉框内容（接受通知数组）
function updateNotificationBadgeFromArray(notifications) {
    var unreadCount = notifications.filter(n => !n.is_read).length;
    var badge = $('.notification-badge');
    var notificationList = $('.notification-list');

    // 更新徽章
    if (unreadCount > 0) {
        badge.text(unreadCount).show();
    } else {
        badge.hide();
    }

    // 更新下拉框内容
    if (notifications.length > 0) {
        var html = '';
        notifications.slice(0, 5).forEach(function(notification) {
            var timeAgo = moment(notification.created_at).fromNow();
            var readClass = notification.is_read ? 'read' : 'unread';
            var newBadge = notification.is_read ? '' : '<span class="badge bg-primary ms-2">新</span>';

            html += `
                <li class="notification-item ${readClass}" data-notification-id="${notification.id}">
                    <a class="dropdown-item py-2" href="#" onclick="viewNotification(${notification.id}, '${notification.type}', ${notification.related_id || 'null'})">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1 small">${notification.title}${newBadge}</h6>
                                <p class="mb-1 small text-muted">${notification.content.substring(0, 50)}${notification.content.length > 50 ? '...' : ''}</p>
                                <small class="text-muted">${timeAgo}</small>
                            </div>
                        </div>
                    </a>
                </li>
            `;
        });
        notificationList.html(html);
    } else {
        notificationList.html(`
            <div class="text-center p-3 text-muted">
                <i class="fas fa-bell-slash fa-2x mb-2"></i>
                <p class="mb-0">暂无新通知</p>
            </div>
        `);
    }
}

// 标记通知为已读
function markNotificationRead(notificationId) {
    $.ajax({
        url: '/api/notifications/' + notificationId + '/read',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                $('[data-notification-id="' + notificationId + '"]').removeClass('unread').addClass('read');
                // 立即更新通知徽章
                setTimeout(function() {
                    loadNotifications();
                }, 100);
            }
        }
    });
}

// 强制刷新通知（用于用户操作后立即更新）
function refreshNotifications() {
    if ($('body').hasClass('authenticated')) {
        loadNotifications();
        loadUnreadMessageCount();
        loadUnreadCounts(); // 加载总的未读数量
    }
}

// 缓存当前的未读数量，避免不必要的DOM更新
window.cachedNotificationCount = -1;
window.cachedMessageCount = -1;

// 防抖计时器
let loadUnreadCountsTimer = null;

// 加载未读数量（分别处理通知和消息）- 带防抖机制
function loadUnreadCounts() {
    if (!$('body').hasClass('authenticated')) return;

    // 清除之前的计时器，避免频繁请求
    if (loadUnreadCountsTimer) {
        clearTimeout(loadUnreadCountsTimer);
    }

    // 设置新的计时器，100ms后执行（减少延迟提高实时性）
    loadUnreadCountsTimer = setTimeout(function() {
        $.ajax({
            url: '/notifications/api/unread_count',
            success: function(response) {
                if (response.success) {
                    // 只有数量变化时才更新DOM，避免闪烁
                    if (window.cachedNotificationCount !== response.unread_notifications) {
                        window.cachedNotificationCount = response.unread_notifications;

                        // 更新通知徽章
                        updateNotificationBadgeCount(response.unread_notifications);

                        // 如果在通知中心页面，同时更新页面内容
                        if (window.location.pathname.includes('/notifications') && typeof refreshNotificationList === 'function') {
                            refreshNotificationList();
                        }
                    }

                    if (window.cachedMessageCount !== response.unread_messages) {
                        window.cachedMessageCount = response.unread_messages;
                        updateMessageBadge(response.unread_messages);
                    }
                }
            },
            error: function() {
                // 静默失败，避免控制台错误
            }
        });
    }, 200);
}

// 更新消息徽章（消息图标）
function updateMessageBadge(unreadMessages) {
    const badge = $('.message-badge');
    if (unreadMessages > 0) {
        badge.text(unreadMessages > 99 ? '99+' : unreadMessages).show();
    } else {
        badge.hide();
    }
}

// 启动实时通知更新（已被base.html系统替代）
function startRealtimeNotifications() {
    if (!$('body').hasClass('authenticated')) return;

    // 注释掉，避免与base.html中的系统冲突
    // 现在由base.html中的refreshNotifications()处理
    console.log('实时通知系统已被base.html系统替代');
}

// 页面获得焦点时刷新通知
$(window).on('focus', function() {
    if (window.realtimeSystem && window.realtimeSystem.isConnected) {
        window.realtimeSystem.requestInitialData();
    } else {
        refreshNotifications();
    }
});

// 页面可见性改变时刷新通知
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        if (window.realtimeSystem && window.realtimeSystem.isConnected) {
            window.realtimeSystem.requestInitialData();
        } else {
            refreshNotifications();
        }
    }
});

// 传统轮询系统 - 已被实时系统替代，保留作为备用
function startPolling() {
    // 注释掉传统轮询，避免与实时系统冲突
    // 现在由 startRealtimeNotifications() 处理
    console.log('传统轮询系统已被实时系统替代');
}

function checkNotifications() {
    $.get('/api/notifications?per_page=5')
        .done(function(data) {
            if (data.notifications && data.notifications.length > 0) {
                updateNotificationBadgeCount(data.unread_count);
            }
        })
        .fail(function() {
            console.log('检查通知失败');
        });
}

function checkMessages() {
    $.get('/api/messages/unread_count')
        .done(function(data) {
            if (data.unread_count > 0) {
                updateMessageBadge(data.unread_count);
            }
        })
        .fail(function() {
            console.log('检查消息失败');
        });
}

// 统一的通知徽章更新函数（接受数字）
function updateNotificationBadgeCount(count) {
    const badge = $('.notification-badge');
    if (count > 0) {
        badge.text(count > 99 ? '99+' : count).show();
    } else {
        badge.hide();
    }
}

// 统一的消息徽章更新函数
function updateMessageBadge(count) {
    const badge = $('.message-badge');
    if (count > 0) {
        badge.text(count > 99 ? '99+' : count).show();
    } else {
        badge.hide();
    }
}

// 全局的通知计数更新函数（供其他页面调用）
function updateNotificationCounts() {
    // 重置缓存，强制更新
    if (typeof window.cachedNotificationCount !== 'undefined') {
        window.cachedNotificationCount = -1;
    }
    if (typeof window.cachedMessageCount !== 'undefined') {
        window.cachedMessageCount = -1;
    }

    // 立即更新计数
    loadUnreadCounts();
}

// 将函数暴露到全局作用域
window.updateNotificationCounts = updateNotificationCounts;
window.updateNotificationBadgeCount = updateNotificationBadgeCount;
window.updateMessageBadge = updateMessageBadge;

// 显示通知Toast
function showNotificationToast(data) {
    const toast = `
        <div class="toast align-items-center text-white bg-primary border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${data.title}</strong><br>
                    ${data.content}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    let toastContainer = $('#toast-container');
    if (toastContainer.length === 0) {
        toastContainer = $('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
        $('body').append(toastContainer);
    }

    const toastElement = $(toast);
    toastContainer.append(toastElement);

    const bsToast = new bootstrap.Toast(toastElement[0]);
    bsToast.show();

    // 自动移除
    toastElement.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

// 显示消息Toast
function showMessageToast(data) {
    const toast = `
        <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-envelope me-2"></i>
                    <strong>新消息</strong><br>
                    您收到了一条新消息
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    let toastContainer = $('#toast-container');
    if (toastContainer.length === 0) {
        toastContainer = $('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
        $('body').append(toastContainer);
    }

    const toastElement = $(toast);
    toastContainer.append(toastElement);

    const bsToast = new bootstrap.Toast(toastElement[0]);
    bsToast.show();

    // 自动移除
    toastElement.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

// 处理系统更新
function handleSystemUpdate(data) {
    console.log('系统更新:', data);

    if (data.type === 'maintenance') {
        showMaintenanceAlert(data);
    } else if (data.type === 'announcement') {
        showAnnouncementAlert(data);
    }
}

// 显示维护提醒
function showMaintenanceAlert(data) {
    const alert = `
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-tools me-2"></i>
            <strong>系统维护通知</strong><br>
            ${data.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.container').first().prepend(alert);
}

// 显示公告提醒
function showAnnouncementAlert(data) {
    const alert = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-bullhorn me-2"></i>
            <strong>系统公告</strong><br>
            ${data.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.container').first().prepend(alert);
}

// 查看通知详情
function viewNotification(notificationId, type, relatedId) {
    // 先标记为已读
    markNotificationRead(notificationId);

    // 根据通知类型跳转到相应页面
    switch(type) {
        case 'order':
            if (relatedId) {
                window.location.href = `/orders/detail/${relatedId}`;
            } else {
                window.location.href = '/orders/my_orders';
            }
            break;
        case 'message':
            window.location.href = '/messages/';
            break;
        case 'review':
            window.location.href = '/auth/profile#reviews';
            break;
        case 'audit':
            if (relatedId) {
                window.location.href = `/products/detail/${relatedId}`;
            } else {
                window.location.href = '/products/my_products';
            }
            break;
        case 'system':
        default:
            window.location.href = '/notifications/';
            break;
    }
}

// 防抖函数
function debounce(func, wait) {
    var timeout;
    return function executedFunction(...args) {
        var later = function() {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 确认删除
function confirmDelete(message) {
    return confirm(message || '确定要删除吗？此操作不可撤销。');
}

// 格式化时间
function formatTime(dateString) {
    var date = new Date(dateString);
    var now = new Date();
    var diff = now - date;

    if (diff < 60000) { // 1分钟内
        return '刚刚';
    } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前';
    } else {
        return date.toLocaleDateString();
    }
}

/**
 * 初始化页面效果
 */
function initializePageEffects() {
    // 页面过渡动画
    $('.page-transition').addClass('loaded');

    // 添加滚动进度条
    if (!$('.scroll-indicator').length) {
        $('body').prepend('<div class="scroll-indicator"><div class="scroll-progress"></div></div>');
    }

    // 为卡片添加动画类
    $('.product-card, .category-card').addClass('card-animate');
}

/**
 * 初始化滚动效果
 */
function initializeScrollEffects() {
    // 卡片进入动画
    function animateCards() {
        $('.card-animate').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('in-view');
            }
        });
    }

    // 滚动进度条更新
    function updateScrollProgress() {
        var scrollTop = $(window).scrollTop();
        var docHeight = $(document).height();
        var winHeight = $(window).height();
        var scrollPercent = (scrollTop) / (docHeight - winHeight);
        var scrollPercentRounded = Math.round(scrollPercent * 100);
        $('.scroll-progress').css('width', scrollPercentRounded + '%');
    }

    // 绑定滚动事件
    $(window).scroll(function() {
        animateCards();
        updateScrollProgress();
    });

    // 初始化
    animateCards();
    updateScrollProgress();

    // 平滑滚动
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        var target = this.hash;
        var $target = $(target);

        if ($target.length) {
            $('html, body').animate({
                'scrollTop': $target.offset().top - 80
            }, 800, 'swing');
        }
    });
}

/**
 * 初始化按钮效果
 */
function initializeButtonEffects() {
    // 按钮点击波纹效果
    $('.btn').on('click', function(e) {
        var $btn = $(this);
        var $ripple = $('<span class="ripple"></span>');
        var btnOffset = $btn.offset();
        var xPos = e.pageX - btnOffset.left;
        var yPos = e.pageY - btnOffset.top;

        $ripple.css({
            position: 'absolute',
            top: yPos + 'px',
            left: xPos + 'px',
            width: '0',
            height: '0',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.6)',
            transform: 'translate(-50%, -50%)',
            animation: 'ripple-effect 0.6s linear',
            pointerEvents: 'none',
            zIndex: 1
        });

        $btn.css('position', 'relative').append($ripple);

        setTimeout(function() {
            $ripple.remove();
        }, 600);
    });
}

/**
 * 初始化卡片动画
 */
function initializeCardAnimations() {
    // 商品卡片悬停效果增强
    $('.product-card').hover(
        function() {
            $(this).addClass('card-hover');
        },
        function() {
            $(this).removeClass('card-hover');
        }
    );

    // 分类卡片点击效果
    $('.category-card').on('click', function() {
        $(this).addClass('card-clicked');
        setTimeout(() => {
            $(this).removeClass('card-clicked');
        }, 200);
    });
}

// 添加CSS动画样式
var style = document.createElement('style');
style.textContent = `
    @keyframes ripple-effect {
        to {
            width: 200px;
            height: 200px;
            opacity: 0;
        }
    }

    .notification-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.3s ease;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .form-group-focus .form-control {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .card-clicked {
        transform: scale(0.98);
    }
`;
document.head.appendChild(style);

// 加载未读消息数量
function loadUnreadMessageCount() {
    if (!$('body').hasClass('authenticated')) return;

    $.ajax({
        url: '/api/messages/unread_count',
        success: function(response) {
            if (response.success) {
                updateMessageBadge(response.count);
            }
        },
        error: function() {
            // 静默失败
        }
    });
}

// 更新消息徽章
function updateMessageBadge(count) {
    const badge = $('.message-badge');
    if (count > 0) {
        badge.text(count).show();
    } else {
        badge.hide();
    }
}

// 发送用户心跳
function sendHeartbeat() {
    if (!$('body').hasClass('authenticated')) return;

    $.ajax({
        url: '/api/user/heartbeat',
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            // 心跳成功，可以在这里添加调试信息
            console.log('Heartbeat sent successfully');
        },
        error: function() {
            // 心跳失败，静默处理
            console.log('Heartbeat failed');
        }
    });
}

// 响应式设计增强功能
class ResponsiveEnhancer {
    constructor() {
        this.init();
        this.bindEvents();
    }

    init() {
        this.initAnimations();
        this.initLazyLoading();
        this.initScrollEffects();
        this.initTouchEnhancements();
        this.initAccessibility();
    }

    // 初始化动画效果
    initAnimations() {
        // 页面加载动画
        $('.page-transition').addClass('loaded');

        // 卡片进入动画
        if ('IntersectionObserver' in window) {
            const cardObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('in-view');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            document.querySelectorAll('.card-animate, .product-card, .category-card').forEach(card => {
                cardObserver.observe(card);
            });
        } else {
            // 降级处理
            $('.card-animate, .product-card, .category-card').addClass('in-view');
        }
    }

    // 图片懒加载
    initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            img.classList.add('loaded');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px 0px'
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // 滚动效果
    initScrollEffects() {
        let ticking = false;

        const updateScrollProgress = () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = Math.min((scrollTop / docHeight) * 100, 100);

            const progressBar = document.querySelector('.scroll-progress');
            if (progressBar) {
                progressBar.style.width = scrollPercent + '%';
            }

            // 返回顶部按钮
            const backToTop = $('#back-to-top');
            if (scrollTop > 300) {
                backToTop.fadeIn(300);
            } else {
                backToTop.fadeOut(300);
            }

            // 导航栏背景透明度
            const navbar = $('.navbar');
            if (scrollTop > 50) {
                navbar.addClass('scrolled');
            } else {
                navbar.removeClass('scrolled');
            }

            ticking = false;
        };

        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollProgress);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestScrollUpdate, { passive: true });

        // 平滑滚动
        $('a[href^="#"]').on('click', function(event) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                event.preventDefault();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });

        // 返回顶部
        $(document).on('click', '#back-to-top', function() {
            $('html, body').animate({scrollTop: 0}, 800);
            return false;
        });
    }

    // 触摸设备增强
    initTouchEnhancements() {
        if ('ontouchstart' in window) {
            // 为触摸设备添加特殊样式
            document.body.classList.add('touch-device');

            // 改善触摸反馈
            $('.btn, .card, .nav-link').on('touchstart', function() {
                $(this).addClass('touch-active');
            }).on('touchend touchcancel', function() {
                setTimeout(() => {
                    $(this).removeClass('touch-active');
                }, 150);
            });

            // 触摸滑动支持
            let startX, startY, distX, distY;

            $('.carousel').on('touchstart', function(e) {
                const touch = e.originalEvent.touches[0];
                startX = touch.clientX;
                startY = touch.clientY;
            });

            $('.carousel').on('touchmove', function(e) {
                if (!startX || !startY) return;

                const touch = e.originalEvent.touches[0];
                distX = touch.clientX - startX;
                distY = touch.clientY - startY;

                if (Math.abs(distX) > Math.abs(distY)) {
                    e.preventDefault();
                }
            });

            $('.carousel').on('touchend', function(e) {
                if (!startX || !startY) return;

                if (Math.abs(distX) > 50) {
                    if (distX > 0) {
                        $(this).carousel('prev');
                    } else {
                        $(this).carousel('next');
                    }
                }

                startX = startY = distX = distY = null;
            });
        }
    }

    // 无障碍访问增强
    initAccessibility() {
        // 键盘导航支持
        $(document).on('keydown', function(e) {
            // ESC键关闭模态框和下拉菜单
            if (e.key === 'Escape') {
                $('.modal').modal('hide');
                $('.dropdown-menu.show').dropdown('hide');
            }
        });

        // 焦点管理
        $('.modal').on('shown.bs.modal', function() {
            $(this).find('[autofocus]').focus();
        });

        // 跳过链接
        if (!$('.skip-link').length) {
            $('body').prepend(`
                <a href="#main-content" class="skip-link sr-only sr-only-focusable">
                    跳转到主要内容
                </a>
            `);
        }
    }

    // 绑定事件
    bindEvents() {
        // 窗口大小改变时的响应
        let resizeTimer;
        $(window).on('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResize();
            }, 250);
        });

        // 导航栏折叠处理
        $('.navbar-toggler').on('click', function() {
            $(this).toggleClass('active');
            $('body').toggleClass('nav-open');
        });

        // 搜索框增强
        $('.navbar .form-control').on('focus', function() {
            $(this).closest('.search-form').addClass('search-focused');
        }).on('blur', function() {
            $(this).closest('.search-form').removeClass('search-focused');
        });

        // 卡片悬停效果增强
        $('.product-card, .category-card').on('mouseenter', function() {
            $(this).addClass('hover-active');
        }).on('mouseleave', function() {
            $(this).removeClass('hover-active');
        });
    }

    // 处理窗口大小改变
    handleResize() {
        const width = $(window).width();

        // 移动端导航处理
        if (width < 768) {
            $('.navbar-collapse').removeClass('show');
            $('.navbar-toggler').removeClass('active');
            $('body').removeClass('nav-open');
        }

        // 重新计算布局
        this.recalculateLayout();
    }

    // 重新计算布局
    recalculateLayout() {
        // 卡片高度均衡（仅在桌面端）
        if ($(window).width() >= 768) {
            $('.row').each(function() {
                const cards = $(this).find('.card');
                if (cards.length > 1) {
                    let maxHeight = 0;
                    cards.css('height', 'auto');
                    cards.each(function() {
                        const height = $(this).outerHeight();
                        if (height > maxHeight) {
                            maxHeight = height;
                        }
                    });
                    if (maxHeight > 0) {
                        cards.css('height', maxHeight + 'px');
                    }
                }
            });
        } else {
            $('.card').css('height', 'auto');
        }
    }
}

// 初始化响应式增强功能
$(document).ready(function() {
    // 创建响应式增强实例
    window.responsiveEnhancer = new ResponsiveEnhancer();

    // 添加滚动进度条
    if (!$('.scroll-indicator').length) {
        $('body').prepend('<div class="scroll-indicator"><div class="scroll-progress"></div></div>');
    }

    // 添加返回顶部按钮
    if (!$('#back-to-top').length) {
        $('body').append(`
            <button id="back-to-top" class="btn btn-primary"
                    style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;
                           border-radius: 50%; width: 50px; height: 50px; display: none;
                           box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
                    aria-label="返回顶部" title="返回顶部">
                <i class="fas fa-arrow-up"></i>
            </button>
        `);
    }

    // 添加CSS样式支持
    if (!$('#responsive-styles').length) {
        $('head').append(`
            <style id="responsive-styles">
                .touch-active {
                    transform: scale(0.98);
                    opacity: 0.8;
                }

                .search-focused {
                    transform: scale(1.02);
                }

                .hover-active {
                    z-index: 10;
                }

                .navbar.scrolled {
                    backdrop-filter: blur(10px);
                    background: rgba(0, 123, 255, 0.95) !important;
                }

                .skip-link {
                    position: absolute;
                    top: -40px;
                    left: 6px;
                    background: #000;
                    color: #fff;
                    padding: 8px;
                    text-decoration: none;
                    z-index: 10000;
                }

                .skip-link:focus {
                    top: 6px;
                }

                @media (max-width: 767.98px) {
                    .nav-open {
                        overflow: hidden;
                    }

                    .nav-open .navbar-collapse {
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 123, 255, 0.98);
                        z-index: 1050;
                        padding: 60px 20px 20px;
                        overflow-y: auto;
                    }
                }
            </style>
        `);
    }
});
