#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间工具模块 - 统一使用本地时间
"""

from datetime import datetime, timedelta
import pytz
from flask import current_app


def get_local_timezone():
    """获取本地时区"""
    # 默认使用中国时区
    return pytz.timezone('Asia/Shanghai')


def now():
    """获取当前本地时间"""
    return datetime.now(get_local_timezone())


def utcnow():
    """获取当前UTC时间（兼容旧代码）"""
    return datetime.utcnow()


def local_now():
    """获取当前本地时间（无时区信息）"""
    return datetime.now()


def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """格式化时间"""
    if dt is None:
        return ''
    
    # 如果是UTC时间，转换为本地时间
    if dt.tzinfo is None:
        # 假设数据库中的时间是UTC时间，转换为本地时间
        utc_dt = pytz.utc.localize(dt)
        local_dt = utc_dt.astimezone(get_local_timezone())
        return local_dt.strftime(format_str)
    
    return dt.strftime(format_str)


def format_relative_time(dt):
    """格式化相对时间（如：3分钟前）"""
    if dt is None:
        return ''
    
    # 转换为本地时间进行比较
    if dt.tzinfo is None:
        utc_dt = pytz.utc.localize(dt)
        local_dt = utc_dt.astimezone(get_local_timezone())
    else:
        local_dt = dt.astimezone(get_local_timezone())
    
    now_local = now()
    diff = now_local - local_dt.replace(tzinfo=None)
    
    if diff.total_seconds() < 60:
        return '刚刚'
    elif diff.total_seconds() < 3600:
        minutes = int(diff.total_seconds() / 60)
        return f'{minutes}分钟前'
    elif diff.total_seconds() < 86400:
        hours = int(diff.total_seconds() / 3600)
        return f'{hours}小时前'
    elif diff.days < 7:
        return f'{diff.days}天前'
    else:
        return format_datetime(local_dt, '%Y-%m-%d')


def days_ago(days):
    """获取N天前的时间"""
    return local_now() - timedelta(days=days)


def hours_ago(hours):
    """获取N小时前的时间"""
    return local_now() - timedelta(hours=hours)


def minutes_ago(minutes):
    """获取N分钟前的时间"""
    return local_now() - timedelta(minutes=minutes)


def start_of_day(dt=None):
    """获取某天的开始时间（00:00:00）"""
    if dt is None:
        dt = local_now()
    return dt.replace(hour=0, minute=0, second=0, microsecond=0)


def end_of_day(dt=None):
    """获取某天的结束时间（23:59:59）"""
    if dt is None:
        dt = local_now()
    return dt.replace(hour=23, minute=59, second=59, microsecond=999999)


def start_of_month(dt=None):
    """获取某月的开始时间"""
    if dt is None:
        dt = local_now()
    return dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)


def end_of_month(dt=None):
    """获取某月的结束时间"""
    if dt is None:
        dt = local_now()
    
    # 获取下个月的第一天，然后减去一秒
    if dt.month == 12:
        next_month = dt.replace(year=dt.year + 1, month=1, day=1)
    else:
        next_month = dt.replace(month=dt.month + 1, day=1)
    
    return next_month - timedelta(seconds=1)


def parse_date_string(date_str, format_str='%Y-%m-%d'):
    """解析日期字符串"""
    try:
        return datetime.strptime(date_str, format_str)
    except ValueError:
        return None


def is_today(dt):
    """判断是否是今天"""
    if dt is None:
        return False
    
    today = start_of_day()
    tomorrow = start_of_day() + timedelta(days=1)
    
    # 转换为本地时间进行比较
    if dt.tzinfo is None:
        utc_dt = pytz.utc.localize(dt)
        local_dt = utc_dt.astimezone(get_local_timezone()).replace(tzinfo=None)
    else:
        local_dt = dt.astimezone(get_local_timezone()).replace(tzinfo=None)
    
    return today <= local_dt < tomorrow


def is_this_week(dt):
    """判断是否是本周"""
    if dt is None:
        return False
    
    # 转换为本地时间进行比较
    if dt.tzinfo is None:
        utc_dt = pytz.utc.localize(dt)
        local_dt = utc_dt.astimezone(get_local_timezone()).replace(tzinfo=None)
    else:
        local_dt = dt.astimezone(get_local_timezone()).replace(tzinfo=None)
    
    now_local = local_now()
    week_start = now_local - timedelta(days=now_local.weekday())
    week_start = start_of_day(week_start)
    
    return week_start <= local_dt < week_start + timedelta(days=7)


def get_timestamp():
    """获取当前时间戳字符串"""
    return local_now().strftime('%Y-%m-%d %H:%M:%S')


def get_date_range_filter(date_str):
    """根据日期字符串获取日期范围过滤条件"""
    if not date_str:
        return None, None
    
    try:
        filter_date = datetime.strptime(date_str, '%Y-%m-%d')
        start_time = start_of_day(filter_date)
        end_time = end_of_day(filter_date)
        return start_time, end_time
    except ValueError:
        return None, None


# 兼容性函数，逐步替换项目中的datetime.utcnow()
def compatible_utcnow():
    """兼容性函数，返回本地时间但保持接口一致"""
    return local_now()


# 为模板提供的时间格式化函数
def template_format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """模板中使用的时间格式化函数"""
    return format_datetime(dt, format_str)


def template_format_relative_time(dt):
    """模板中使用的相对时间格式化函数"""
    return format_relative_time(dt)


def get_online_users_count():
    """获取在线用户数量 - 统一的计算方法"""
    import logging

    logger = logging.getLogger(__name__)

    try:
        from app.models import User
        from datetime import timedelta

        # 15分钟内活跃的用户被认为是在线用户
        cutoff_time = local_now() - timedelta(minutes=15)

        # 执行查询并记录日志
        online_count = User.query.filter(
            User.last_seen >= cutoff_time,
            User.is_active == True
        ).count()

        logger.debug(f"在线用户数查询成功: {online_count} 用户在过去15分钟内活跃")
        return online_count

    except Exception as e:
        logger.error(f"获取在线用户数失败: {str(e)}")
        # 返回0而不是抛出异常，确保系统稳定性
        return 0
