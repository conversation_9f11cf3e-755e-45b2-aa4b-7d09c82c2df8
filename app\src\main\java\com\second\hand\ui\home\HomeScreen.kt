package com.second.hand.ui.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.second.hand.ui.auth.AuthViewModel
import com.second.hand.ui.navigation.NavigationDestinations
import com.second.hand.ui.components.ProductCard
import com.second.hand.ui.product.ProductListViewModel

/**
 * 首页Screen - 商品浏览主页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    navController: NavController,
    authViewModel: AuthViewModel = hiltViewModel(),
    productListViewModel: ProductListViewModel = hiltViewModel(),
    onNavigateToWelcome: () -> Unit = {}
) {
    val authUiState by authViewModel.uiState.collectAsState()
    val products by productListViewModel.products.collectAsState()
    val categories by productListViewModel.categories.collectAsState()

    // 如果未登录，导航到欢迎页面
    LaunchedEffect(authUiState.isLoggedIn) {
        if (!authUiState.isLoggedIn) {
            onNavigateToWelcome()
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部搜索栏
        TopAppBar(
            title = {
                Text(
                    "二手交易平台",
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                IconButton(
                    onClick = {
                        navController.navigate(NavigationDestinations.SEARCH)
                    }
                ) {
                    Icon(Icons.Default.Search, contentDescription = "搜索")
                }
            }
        )

        // 主要内容
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Spacer(modifier = Modifier.height(8.dp))
            }

            // 用户欢迎卡片
            item {
                UserWelcomeCard(
                    username = authUiState.currentUser?.nickname ?: "用户"
                )
            }

            // 快捷功能
            item {
                QuickActionsCard(navController = navController)
            }

            // 商品分类
            item {
                CategorySection(
                    categories = categories,
                    navController = navController
                )
            }

            // 推荐商品
            item {
                RecommendedProductsSection(
                    products = products.take(6), // 显示前6个商品
                    navController = navController,
                    onProductClick = { product ->
                        productListViewModel.incrementViewCount(product.id)
                        navController.navigate(NavigationDestinations.productDetail(product.id.toString()))
                    },
                    onFavoriteClick = { productId ->
                        productListViewModel.toggleFavorite(productId)
                    }
                )
            }

            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Composable
private fun UserWelcomeCard(username: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier.padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Card(
                modifier = Modifier.size(50.dp),
                shape = RoundedCornerShape(25.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = username.take(1).uppercase(),
                        style = MaterialTheme.typography.titleLarge,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            Column {
                Text(
                    text = "欢迎回来！",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = username,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
    }
}

@Composable
private fun QuickActionsCard(navController: NavController) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "快捷功能",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                QuickActionItem(
                    icon = "🛍️",
                    title = "商品列表",
                    onClick = { navController.navigate(NavigationDestinations.PRODUCT_LIST) }
                )
                QuickActionItem(
                    icon = "📱",
                    title = "发布商品",
                    onClick = { navController.navigate(NavigationDestinations.PUBLISH) }
                )
                QuickActionItem(
                    icon = "🛒",
                    title = "我的订单",
                    onClick = { navController.navigate(NavigationDestinations.ORDERS) }
                )
                QuickActionItem(
                    icon = "💬",
                    title = "消息中心",
                    onClick = { navController.navigate(NavigationDestinations.MESSAGE) }
                )
            }
        }
    }
}

@Composable
private fun QuickActionItem(
    icon: String,
    title: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Card(
            modifier = Modifier.size(48.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            ),
            onClick = onClick
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = icon,
                    style = MaterialTheme.typography.titleLarge
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = title,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun CategorySection(
    categories: List<com.second.hand.data.model.Category>,
    navController: NavController
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "商品分类",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            TextButton(
                onClick = { navController.navigate(NavigationDestinations.CATEGORY) }
            ) {
                Text("查看全部")
            }
        }

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            if (categories.isNotEmpty()) {
                items(categories.take(6)) { category ->
                    CategoryItem(
                        category = CategoryData(category.name, getCategoryIcon(category.name)),
                        onClick = { navController.navigate(NavigationDestinations.CATEGORY) }
                    )
                }
            } else {
                // 显示默认分类
                items(getCategoryList()) { category ->
                    CategoryItem(
                        category = category,
                        onClick = { navController.navigate(NavigationDestinations.CATEGORY) }
                    )
                }
            }
        }
    }
}

@Composable
private fun CategoryItem(
    category: CategoryData,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.width(100.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = category.icon,
                style = MaterialTheme.typography.headlineSmall
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = category.name,
                style = MaterialTheme.typography.labelMedium,
                maxLines = 1
            )
        }
    }
}

@Composable
private fun RecommendedProductsSection(
    products: List<com.second.hand.data.model.Product>,
    navController: NavController,
    onProductClick: (com.second.hand.data.model.Product) -> Unit,
    onFavoriteClick: (Int) -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "推荐商品",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            TextButton(
                onClick = { navController.navigate(NavigationDestinations.PRODUCT_LIST) }
            ) {
                Text("查看更多")
            }
        }

        if (products.isNotEmpty()) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(vertical = 8.dp)
            ) {
                items(products) { product ->
                    ProductCard(
                        product = product,
                        onProductClick = onProductClick,
                        onFavoriteClick = onFavoriteClick,
                        modifier = Modifier.width(160.dp),
                        showFavoriteButton = false
                    )
                }
            }
        } else {
            // 显示空状态或加载状态
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无推荐商品",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

// 示例数据类
data class CategoryData(val name: String, val icon: String)

// 示例数据
// 工具函数
private fun getCategoryIcon(categoryName: String): String {
    return when (categoryName.lowercase()) {
        "数码产品", "电子产品", "数码" -> "📱"
        "服装配饰", "服装", "配饰" -> "👕"
        "家居用品", "家居", "家具" -> "🏠"
        "图书文具", "图书", "文具" -> "📚"
        "运动户外", "运动", "户外" -> "⚽"
        "美妆护肤", "美妆", "护肤" -> "💄"
        "汽车用品", "汽车" -> "🚗"
        "母婴用品", "母婴" -> "👶"
        "食品饮料", "食品" -> "🍎"
        "玩具游戏", "玩具" -> "🎮"
        else -> "📦"
    }
}

private fun getCategoryList() = listOf(
    CategoryData("数码产品", "📱"),
    CategoryData("服装配饰", "👕"),
    CategoryData("家居用品", "🏠"),
    CategoryData("图书文具", "📚"),
    CategoryData("运动户外", "⚽"),
    CategoryData("美妆护肤", "💄")
)
