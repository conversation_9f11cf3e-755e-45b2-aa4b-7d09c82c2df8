package com.second.hand.data.model.enums

import com.google.gson.annotations.SerializedName

/**
 * 通知类型枚举
 * 对应Flask后端的通知类型
 */
enum class NotificationType {
    @SerializedName("order")
    ORDER,          // 订单相关
    
    @SerializedName("system")
    SYSTEM,         // 系统通知
    
    @SerializedName("message")
    MESSAGE,        // 消息相关
    
    @SerializedName("review")
    REVIEW,         // 评价相关
    
    @SerializedName("payment")
    PAYMENT,        // 支付相关
    
    @SerializedName("shipping")
    SHIPPING        // 物流相关
}
