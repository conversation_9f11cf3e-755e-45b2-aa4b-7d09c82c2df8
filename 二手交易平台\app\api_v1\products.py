#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端商品API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import Product, ProductImage, Category, Favorite, User, ProductStatus
from app import db
from app.utils.jwt_utils import jwt_required
from app.utils.api_response import (
    success_response, error_response, validation_error_response,
    paginated_response, not_found_response
)
from app.utils.datetime_utils import local_now
from sqlalchemy import or_, and_, desc, asc
import base64
import os
from PIL import Image
from io import BytesIO
import uuid
from datetime import datetime


def validate_product_data(data, is_update=False):
    """验证商品数据"""
    errors = {}
    
    if not is_update or 'title' in data:
        title = data.get('title', '').strip()
        if not title:
            errors['title'] = '商品标题不能为空'
        elif len(title) > 100:
            errors['title'] = '商品标题不能超过100个字符'
    
    if not is_update or 'description' in data:
        description = data.get('description', '').strip()
        if not description:
            errors['description'] = '商品描述不能为空'
        elif len(description) > 2000:
            errors['description'] = '商品描述不能超过2000个字符'
    
    if not is_update or 'price' in data:
        try:
            price = float(data.get('price', 0))
            if price <= 0:
                errors['price'] = '商品价格必须大于0'
            elif price > 999999.99:
                errors['price'] = '商品价格不能超过999999.99'
        except (ValueError, TypeError):
            errors['price'] = '商品价格格式不正确'
    
    if 'originalPrice' in data and data['originalPrice']:
        try:
            original_price = float(data['originalPrice'])
            if original_price <= 0:
                errors['originalPrice'] = '原价必须大于0'
            elif original_price > 999999.99:
                errors['originalPrice'] = '原价不能超过999999.99'
        except (ValueError, TypeError):
            errors['originalPrice'] = '原价格式不正确'
    
    if not is_update or 'categoryId' in data:
        category_id = data.get('categoryId')
        if not category_id:
            errors['categoryId'] = '商品分类不能为空'
        else:
            category = Category.query.get(category_id)
            if not category:
                errors['categoryId'] = '商品分类不存在'
    
    if not is_update or 'quantity' in data:
        try:
            quantity = int(data.get('quantity', 1))
            if quantity <= 0:
                errors['quantity'] = '商品数量必须大于0'
            elif quantity > 9999:
                errors['quantity'] = '商品数量不能超过9999'
        except (ValueError, TypeError):
            errors['quantity'] = '商品数量格式不正确'
    
    return errors


def save_product_image(image_data, product_id, is_main=False):
    """保存商品图片"""
    try:
        # 解析base64图片
        if image_data.startswith('data:image'):
            image_data = image_data.split(',')[1]
        
        image_bytes = base64.b64decode(image_data)
        image = Image.open(BytesIO(image_bytes))
        
        if image.format.lower() not in ['jpeg', 'jpg', 'png', 'gif']:
            return None, '不支持的图片格式'
        
        # 生成文件名
        filename = f"product_{product_id}_{uuid.uuid4().hex[:8]}.jpg"
        image_dir = os.path.join(current_app.static_folder, 'uploads', 'products')
        os.makedirs(image_dir, exist_ok=True)
        
        # 调整图片大小
        image = image.convert('RGB')
        image.thumbnail((800, 800), Image.Resampling.LANCZOS)
        
        image_path = os.path.join(image_dir, filename)
        image.save(image_path, 'JPEG', quality=85)
        
        # 保存到数据库
        product_image = ProductImage(
            filename=filename,
            product_id=product_id,
            is_main=is_main,
            created_at=local_now()
        )
        db.session.add(product_image)
        
        return product_image, None
        
    except Exception as e:
        return None, f'图片保存失败: {str(e)}'


def serialize_product(product, include_seller=True):
    """序列化商品数据"""
    def format_datetime(dt):
        """安全地格式化日期时间"""
        if dt is None:
            return None
        try:
            # 如果是字符串，先解析为datetime对象
            if isinstance(dt, str):
                # 尝试解析包含微秒的格式
                try:
                    dt = datetime.strptime(dt, '%Y-%m-%dT%H:%M:%S.%f')
                except ValueError:
                    try:
                        dt = datetime.strptime(dt, '%Y-%m-%dT%H:%M:%S')
                    except ValueError:
                        # 如果都解析失败，返回原字符串
                        return dt

            # 格式化为标准ISO格式
            return dt.strftime('%Y-%m-%dT%H:%M:%S') + 'Z'
        except Exception as e:
            current_app.logger.error(f"日期格式化失败: {str(e)}, 原始值: {dt}")
            return str(dt) if dt else None

    data = {
        'id': product.id,
        'title': product.title,
        'description': product.description,
        'price': float(product.price),
        'originalPrice': float(product.original_price) if product.original_price else None,
        'status': product.status.value,
        'condition': product.condition,
        'location': product.location,
        'quantity': product.quantity,
        'soldQuantity': product.sold_quantity,
        'viewCount': product.view_count,
        'favoriteCount': product.favorite_count,
        'images': [
            {
                'id': img.id,
                'url': f'/static/uploads/products/{img.filename}',
                'isMain': img.is_main
            }
            for img in product.images
        ],
        'category': {
            'id': product.category.id,
            'name': product.category.name,
            'icon': product.category.icon,
            'color': product.category.color
        } if product.category else None,
        'createdAt': format_datetime(product.created_at),
        'updatedAt': format_datetime(product.updated_at)
    }
    
    if include_seller and product.seller:
        data['seller'] = {
            'id': product.seller.id,
            'username': product.seller.username,
            'nickname': product.seller.nickname,
            'avatar': product.seller.avatar_url()
        }
    
    return data


@bp.route('/products', methods=['GET'])
def get_products():
    """获取商品列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        category_id = request.args.get('category', type=int)
        status = request.args.get('status', 'active')
        search = request.args.get('search', '').strip()
        sort = request.args.get('sort', 'created_at')
        order = request.args.get('order', 'desc')
        min_price = request.args.get('minPrice', type=float)
        max_price = request.args.get('maxPrice', type=float)
        condition = request.args.get('condition', '').strip()
        location = request.args.get('location', '').strip()
        
        # 构建查询
        query = Product.query
        
        # 状态筛选
        if status == 'active':
            query = query.filter(Product.status == ProductStatus.ACTIVE)
        elif status == 'sold':
            query = query.filter(Product.status == ProductStatus.SOLD)
        elif status == 'inactive':
            query = query.filter(Product.status == ProductStatus.INACTIVE)
        
        # 分类筛选
        if category_id:
            query = query.filter(Product.category_id == category_id)
        
        # 关键词搜索
        if search:
            query = query.filter(
                or_(
                    Product.title.contains(search),
                    Product.description.contains(search)
                )
            )
        
        # 价格筛选
        if min_price is not None:
            query = query.filter(Product.price >= min_price)
        if max_price is not None:
            query = query.filter(Product.price <= max_price)
        
        # 成色筛选
        if condition:
            query = query.filter(Product.condition == condition)
        
        # 地区筛选
        if location:
            query = query.filter(Product.location.contains(location))
        
        # 排序
        if sort == 'price':
            if order == 'asc':
                query = query.order_by(asc(Product.price))
            else:
                query = query.order_by(desc(Product.price))
        elif sort == 'view_count':
            query = query.order_by(desc(Product.view_count))
        elif sort == 'favorite_count':
            query = query.order_by(desc(Product.favorite_count))
        else:  # created_at
            if order == 'asc':
                query = query.order_by(asc(Product.created_at))
            else:
                query = query.order_by(desc(Product.created_at))
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )
        
        # 序列化数据
        products = [serialize_product(product) for product in pagination.items]
        
        return paginated_response(products, pagination)
        
    except Exception as e:
        current_app.logger.error(f"获取商品列表失败: {str(e)}")
        return error_response('SYS_001', '获取商品列表失败', http_code=500)


@bp.route('/products/<int:product_id>', methods=['GET'])
def get_product(product_id):
    """获取商品详情"""
    try:
        product = Product.query.get(product_id)
        if not product:
            return not_found_response('商品不存在')
        
        # 增加浏览量
        product.view_count += 1
        db.session.commit()
        
        return success_response(serialize_product(product))
        
    except Exception as e:
        current_app.logger.error(f"获取商品详情失败: {str(e)}")
        return error_response('SYS_001', '获取商品详情失败', http_code=500)


@bp.route('/products', methods=['POST'])
@jwt_required
def create_product():
    """发布商品"""
    try:
        user = request.current_user
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 验证数据
        errors = validate_product_data(data)
        if errors:
            return validation_error_response(errors)

        # 创建商品
        product = Product(
            title=data['title'].strip(),
            description=data['description'].strip(),
            price=float(data['price']),
            original_price=float(data['originalPrice']) if data.get('originalPrice') else None,
            condition=data.get('condition', '').strip(),
            location=data.get('location', '').strip(),
            quantity=int(data.get('quantity', 1)),
            category_id=data['categoryId'],
            seller_id=user.id,
            status=ProductStatus.PENDING,  # 待审核状态
            created_at=local_now()
        )

        db.session.add(product)
        db.session.flush()  # 获取product.id

        # 处理图片上传
        images_data = data.get('images', [])
        if not images_data:
            return validation_error_response({'images': '至少需要上传一张商品图片'})

        main_image_set = False
        for i, image_data in enumerate(images_data):
            if not image_data.get('data'):
                continue

            is_main = image_data.get('isMain', False) or (i == 0 and not main_image_set)
            if is_main:
                main_image_set = True

            product_image, error = save_product_image(
                image_data['data'],
                product.id,
                is_main
            )

            if error:
                db.session.rollback()
                return validation_error_response({'images': error})

        db.session.commit()

        return success_response(
            serialize_product(product, include_seller=False),
            '商品发布成功，等待审核',
            201
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"发布商品失败: {str(e)}")
        return error_response('SYS_001', '发布商品失败', http_code=500)


@bp.route('/products/<int:product_id>', methods=['PUT'])
@jwt_required
def update_product(product_id):
    """更新商品"""
    try:
        user = request.current_user
        product = Product.query.get(product_id)

        if not product:
            return not_found_response('商品不存在')

        if product.seller_id != user.id and not user.is_admin():
            return error_response('AUTH_004', '无权限编辑此商品', http_code=403)

        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 验证数据
        errors = validate_product_data(data, is_update=True)
        if errors:
            return validation_error_response(errors)

        # 更新商品信息
        if 'title' in data:
            product.title = data['title'].strip()
        if 'description' in data:
            product.description = data['description'].strip()
        if 'price' in data:
            product.price = float(data['price'])
        if 'originalPrice' in data:
            product.original_price = float(data['originalPrice']) if data['originalPrice'] else None
        if 'condition' in data:
            product.condition = data['condition'].strip()
        if 'location' in data:
            product.location = data['location'].strip()
        if 'quantity' in data:
            product.quantity = int(data['quantity'])
        if 'categoryId' in data:
            product.category_id = data['categoryId']

        product.updated_at = local_now()

        # 处理图片更新
        if 'images' in data:
            # 删除旧图片
            for old_image in product.images:
                old_image_path = os.path.join(
                    current_app.static_folder,
                    'uploads',
                    'products',
                    old_image.filename
                )
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)
                db.session.delete(old_image)

            # 添加新图片
            images_data = data['images']
            if images_data:
                main_image_set = False
                for i, image_data in enumerate(images_data):
                    if not image_data.get('data'):
                        continue

                    is_main = image_data.get('isMain', False) or (i == 0 and not main_image_set)
                    if is_main:
                        main_image_set = True

                    product_image, error = save_product_image(
                        image_data['data'],
                        product.id,
                        is_main
                    )

                    if error:
                        db.session.rollback()
                        return validation_error_response({'images': error})

        db.session.commit()

        return success_response(
            serialize_product(product, include_seller=False),
            '商品更新成功'
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新商品失败: {str(e)}")
        return error_response('SYS_001', '更新商品失败', http_code=500)


@bp.route('/products/<int:product_id>', methods=['DELETE'])
@jwt_required
def delete_product(product_id):
    """删除商品"""
    try:
        user = request.current_user
        product = Product.query.get(product_id)

        if not product:
            return not_found_response('商品不存在')

        if product.seller_id != user.id and not user.is_admin():
            return error_response('AUTH_004', '无权限删除此商品', http_code=403)

        # 删除商品图片文件
        for image in product.images:
            image_path = os.path.join(
                current_app.static_folder,
                'uploads',
                'products',
                image.filename
            )
            if os.path.exists(image_path):
                os.remove(image_path)

        # 删除商品
        db.session.delete(product)
        db.session.commit()

        return success_response(None, '商品删除成功')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除商品失败: {str(e)}")
        return error_response('SYS_001', '删除商品失败', http_code=500)


@bp.route('/products/<int:product_id>/favorite', methods=['POST'])
@jwt_required
def toggle_favorite(product_id):
    """收藏/取消收藏商品"""
    try:
        user = request.current_user
        product = Product.query.get(product_id)

        if not product:
            return not_found_response('商品不存在')

        # 检查是否已收藏
        favorite = Favorite.query.filter_by(
            user_id=user.id,
            product_id=product_id
        ).first()

        if favorite:
            # 取消收藏
            db.session.delete(favorite)
            product.favorite_count = max(0, product.favorite_count - 1)
            is_favorited = False
            message = '取消收藏成功'
        else:
            # 添加收藏
            favorite = Favorite(
                user_id=user.id,
                product_id=product_id,
                created_at=local_now()
            )
            db.session.add(favorite)
            product.favorite_count += 1
            is_favorited = True
            message = '收藏成功'

        db.session.commit()

        return success_response({
            'isFavorited': is_favorited,
            'favoriteCount': product.favorite_count
        }, message)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"收藏操作失败: {str(e)}")
        return error_response('SYS_001', '收藏操作失败', http_code=500)


@bp.route('/products/my', methods=['GET'])
@jwt_required
def get_my_products():
    """获取我的商品"""
    try:
        user = request.current_user

        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        status = request.args.get('status', 'all')

        # 构建查询
        query = Product.query.filter(Product.seller_id == user.id)

        # 状态筛选
        if status == 'active':
            query = query.filter(Product.status == ProductStatus.ACTIVE)
        elif status == 'pending':
            query = query.filter(Product.status == ProductStatus.PENDING)
        elif status == 'sold':
            query = query.filter(Product.status == ProductStatus.SOLD)
        elif status == 'inactive':
            query = query.filter(Product.status == ProductStatus.INACTIVE)

        # 按创建时间倒序排列
        query = query.order_by(desc(Product.created_at))

        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )

        # 序列化数据
        products = [serialize_product(product, include_seller=False) for product in pagination.items]

        return paginated_response(products, pagination)

    except Exception as e:
        current_app.logger.error(f"获取我的商品失败: {str(e)}")
        return error_response('SYS_001', '获取我的商品失败', http_code=500)


@bp.route('/products/favorites', methods=['GET'])
@jwt_required
def get_favorites():
    """获取我的收藏"""
    try:
        user = request.current_user

        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)

        # 构建查询
        query = db.session.query(Product).join(Favorite).filter(
            Favorite.user_id == user.id,
            Product.status == ProductStatus.ACTIVE  # 只显示在售商品
        ).order_by(desc(Favorite.created_at))

        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )

        # 序列化数据
        products = [serialize_product(product) for product in pagination.items]

        return paginated_response(products, pagination)

    except Exception as e:
        current_app.logger.error(f"获取收藏列表失败: {str(e)}")
        return error_response('SYS_001', '获取收藏列表失败', http_code=500)


@bp.route('/products/<int:product_id>/view', methods=['POST'])
def increment_view(product_id):
    """增加商品浏览量"""
    try:
        product = Product.query.get(product_id)
        if not product:
            return not_found_response('商品不存在')

        product.view_count += 1
        db.session.commit()

        return success_response({
            'viewCount': product.view_count
        }, '浏览量更新成功')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新浏览量失败: {str(e)}")
        return error_response('SYS_001', '更新浏览量失败', http_code=500)
