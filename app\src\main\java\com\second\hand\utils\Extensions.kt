package com.second.hand.utils

import android.content.Context
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext

/**
 * 扩展函数集合
 * 提供常用的扩展方法
 */

/**
 * Context扩展
 */
fun Context.showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(this, message, duration).show()
}

/**
 * String扩展
 */
fun String?.isNotNullOrEmpty(): Boolean {
    return !this.isNullOrEmpty()
}

fun String?.orDefault(default: String = ""): String {
    return this ?: default
}

/**
 * Compose扩展
 */
@Composable
fun ShowToast(message: String) {
    val context = LocalContext.current
    context.showToast(message)
}

/**
 * 结果处理扩展
 */
inline fun <T> Result<T>.onSuccessCompose(action: (T) -> Unit): Result<T> {
    if (isSuccess) {
        action(getOrNull()!!)
    }
    return this
}

inline fun <T> Result<T>.onFailureCompose(action: (Throwable) -> Unit): Result<T> {
    if (isFailure) {
        action(exceptionOrNull()!!)
    }
    return this
}
