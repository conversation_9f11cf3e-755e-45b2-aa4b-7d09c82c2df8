package com.second.hand.ui.product

import androidx.lifecycle.viewModelScope
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.model.Product
import com.second.hand.data.repository.ProductRepository
import com.second.hand.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 商品详情ViewModel
 * 管理商品详情页面的数据和状态
 */
@HiltViewModel
class ProductDetailViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : BaseViewModel() {

    // UI状态
    private val _uiState = MutableStateFlow(ProductDetailUiState())
    val uiState: StateFlow<ProductDetailUiState> = _uiState.asStateFlow()

    // 商品详情
    private val _product = MutableStateFlow<Product?>(null)
    val product: StateFlow<Product?> = _product.asStateFlow()

    // 当前商品ID
    private var currentProductId: Int = 0

    /**
     * 加载商品详情
     */
    fun loadProductDetail(productId: Int) {
        if (productId <= 0) {
            setError("无效的商品ID")
            return
        }

        currentProductId = productId
        
        launchSafely {
            setLoading(true)
            clearError()

            when (val result = productRepository.getProduct(productId)) {
                is NetworkResult.Success -> {
                    _product.value = result.data
                    _uiState.value = _uiState.value.copy(
                        isLoaded = true
                    )
                    
                    // 增加浏览量
                    incrementViewCount(productId)
                }
                is NetworkResult.Error -> {
                    setError(result.message)
                }
                is NetworkResult.Loading -> {
                    // 已在上面处理
                }
            }

            setLoading(false)
        }
    }

    /**
     * 刷新商品详情
     */
    fun refreshProductDetail() {
        if (currentProductId > 0) {
            loadProductDetail(currentProductId)
        }
    }

    /**
     * 收藏/取消收藏商品
     */
    fun toggleFavorite() {
        val currentProduct = _product.value ?: return
        
        launchSafely {
            when (val result = productRepository.toggleFavorite(currentProduct.id)) {
                is NetworkResult.Success -> {
                    val isFavorited = result.data
                    _product.value = currentProduct.copy(isFavorited = isFavorited)
                    
                    _uiState.value = _uiState.value.copy(
                        favoriteMessage = if (isFavorited) "已添加到收藏" else "已取消收藏"
                    )
                }
                is NetworkResult.Error -> {
                    setError(result.message)
                }
                is NetworkResult.Loading -> {
                    // 不处理
                }
            }
        }
    }

    /**
     * 增加商品浏览量
     */
    private fun incrementViewCount(productId: Int) {
        launchSafely {
            productRepository.incrementViewCount(productId)
            // 更新本地商品的浏览量
            _product.value?.let { product ->
                _product.value = product.copy(viewCount = product.viewCount + 1)
            }
        }
    }

    /**
     * 清除收藏消息
     */
    fun clearFavoriteMessage() {
        _uiState.value = _uiState.value.copy(favoriteMessage = null)
    }

    /**
     * 联系卖家
     */
    fun contactSeller() {
        val currentProduct = _product.value ?: return
        val seller = currentProduct.seller ?: return
        
        // TODO: 实现联系卖家功能
        _uiState.value = _uiState.value.copy(
            contactMessage = "联系卖家功能即将上线"
        )
    }

    /**
     * 立即购买
     */
    fun buyNow() {
        val currentProduct = _product.value ?: return
        
        if (currentProduct.isSoldOut()) {
            setError("商品已售完")
            return
        }
        
        // TODO: 实现立即购买功能
        _uiState.value = _uiState.value.copy(
            buyMessage = "购买功能即将上线"
        )
    }

    /**
     * 分享商品
     */
    fun shareProduct() {
        val currentProduct = _product.value ?: return
        
        // TODO: 实现分享功能
        _uiState.value = _uiState.value.copy(
            shareMessage = "分享功能即将上线"
        )
    }

    /**
     * 清除操作消息
     */
    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            favoriteMessage = null,
            contactMessage = null,
            buyMessage = null,
            shareMessage = null
        )
    }
}

/**
 * 商品详情UI状态
 */
data class ProductDetailUiState(
    val isLoaded: Boolean = false,
    val favoriteMessage: String? = null,
    val contactMessage: String? = null,
    val buyMessage: String? = null,
    val shareMessage: String? = null
)
