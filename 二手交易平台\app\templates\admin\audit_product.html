{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-cogs me-2"></i>商品状态管理</h2>
        <a href="{{ url_for('admin_panel.products') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回商品列表
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> 更新商品状态</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <div class="mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select", id="status-select") }}
                            {% for error in form.status.errors %}
                            <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                            <div class="form-text">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    选择合适的状态来管理商品的显示和销售状态
                                </small>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.audit_note.label(class="form-label") }}
                            {{ form.audit_note(class="form-control", rows="4", placeholder="请输入审核备注或状态变更说明...") }}
                            {% for error in form.audit_note.errors %}
                            <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                            <div class="form-text">
                                <small class="text-muted">备注将发送给商品发布者</small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>更新状态
                            </button>
                            <a href="{{ url_for('admin_panel.products') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    
    <div class="col-lg-4">
        <!-- 商品信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">商品信息</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <img src="{{ product.get_main_image_url() }}"
                         class="img-fluid rounded" alt="商品图片" style="max-height: 200px;">
                </div>
                
                <h6>{{ product.title }}</h6>
                <p class="text-muted">{{ product.description[:100] }}...</p>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>价格:</strong></div>
                    <div class="col-7">¥{{ "%.2f"|format(product.price) }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>卖家:</strong></div>
                    <div class="col-7">{{ product.seller.username }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>分类:</strong></div>
                    <div class="col-7">{{ product.category.name }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>发布时间:</strong></div>
                    <div class="col-7">{{ product.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>当前状态:</strong></div>
                    <div class="col-7">
                        {% if product.status.value == 'pending' %}
                        <span class="badge bg-warning">待审核</span>
                        {% elif product.status.value == 'active' %}
                        <span class="badge bg-success">在售</span>
                        {% elif product.status.value == 'inactive' %}
                        <span class="badge bg-secondary">已下架</span>
                        {% elif product.status.value == 'sold' %}
                        <span class="badge bg-info">已售出</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-5"><strong>浏览次数:</strong></div>
                    <div class="col-7">{{ product.view_count }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-5"><strong>收藏次数:</strong></div>
                    <div class="col-7">{{ product.favorite_count }}</div>
                </div>

                {% if product.location %}
                <div class="row mb-2">
                    <div class="col-5"><strong>所在地:</strong></div>
                    <div class="col-7">{{ product.location }}</div>
                </div>
                {% endif %}

                {% if product.condition %}
                <div class="row mb-2">
                    <div class="col-5"><strong>新旧程度:</strong></div>
                    <div class="col-7">{{ product.condition }}</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">快速操作</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="setStatus('active')">
                        <i class="fas fa-check me-1"></i>审核通过
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setStatus('inactive')">
                        <i class="fas fa-eye-slash me-1"></i>下架商品
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="setStatus('pending')">
                        <i class="fas fa-clock me-1"></i>重新审核
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="setStatus('sold')">
                        <i class="fas fa-handshake me-1"></i>标记售出
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
function setStatus(status) {
    const statusSelect = document.getElementById('status-select');
    statusSelect.value = status;

    // 添加视觉反馈
    statusSelect.classList.add('border-primary');
    setTimeout(() => {
        statusSelect.classList.remove('border-primary');
    }, 1000);

    // 根据状态设置建议的备注
    const auditNote = document.querySelector('textarea[name="audit_note"]');
    const suggestions = {
        'active': '商品信息完整，符合平台规范，审核通过。',
        'inactive': '商品暂时下架，如有疑问请联系管理员。',
        'pending': '商品需要进一步审核，请耐心等待。',
        'sold': '商品已确认售出，感谢使用平台。'
    };

    if (auditNote && suggestions[status]) {
        auditNote.value = suggestions[status];
    }
}

// 状态变更时的提示
document.getElementById('status-select').addEventListener('change', function() {
    const statusTexts = {
        'active': '审核通过后，商品将在平台上正常显示和销售',
        'inactive': '下架后，商品将不在平台上显示',
        'pending': '重新进入待审核状态，商品暂时不显示',
        'sold': '标记为已售出，商品将显示为售出状态'
    };

    const helpText = statusTexts[this.value];
    if (helpText) {
        // 显示状态说明
        let helpDiv = document.getElementById('status-help');
        if (!helpDiv) {
            helpDiv = document.createElement('div');
            helpDiv.id = 'status-help';
            helpDiv.className = 'alert alert-info mt-2';
            this.parentNode.appendChild(helpDiv);
        }
        helpDiv.innerHTML = '<i class="fas fa-info-circle me-1"></i>' + helpText;
    }
});
</script>
{% endblock %}
{% endblock %}
