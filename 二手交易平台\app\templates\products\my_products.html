{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-box"></i> 我的商品</h2>
    <a href="{{ url_for('products.publish') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 发布商品
    </a>
</div>

<!-- 状态筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="btn-group" role="group">
            <a href="{{ url_for('products.my_products', status='all') }}" 
               class="btn {% if status_filter == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                全部
            </a>
            <a href="{{ url_for('products.my_products', status='pending') }}" 
               class="btn {% if status_filter == 'pending' %}btn-warning{% else %}btn-outline-warning{% endif %}">
                待审核
            </a>
            <a href="{{ url_for('products.my_products', status='active') }}" 
               class="btn {% if status_filter == 'active' %}btn-success{% else %}btn-outline-success{% endif %}">
                在售中
            </a>
            <a href="{{ url_for('products.my_products', status='sold') }}" 
               class="btn {% if status_filter == 'sold' %}btn-secondary{% else %}btn-outline-secondary{% endif %}">
                已售出
            </a>
            <a href="{{ url_for('products.my_products', status='inactive') }}" 
               class="btn {% if status_filter == 'inactive' %}btn-danger{% else %}btn-outline-danger{% endif %}">
                已下架
            </a>
        </div>
    </div>
</div>

<!-- 商品列表 -->
{% if products.items %}
<div class="row">
    {% for product in products.items %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="position-relative">
                <img src="{{ product.get_main_image_url() }}"
                     class="card-img-top" alt="{{ product.title }}" style="height: 200px; object-fit: cover;">
                
                <!-- 状态标签 -->
                <span class="position-absolute top-0 start-0 m-2">
                    {% if product.status.value == 'pending' %}
                    <span class="badge bg-warning">待审核</span>
                    {% elif product.status.value == 'active' %}
                    <span class="badge bg-success">在售中</span>
                    {% elif product.status.value == 'sold' %}
                    <span class="badge bg-secondary">已售出</span>
                    {% elif product.status.value == 'inactive' %}
                    <span class="badge bg-danger">已下架</span>
                    {% endif %}
                </span>
            </div>
            
            <div class="card-body d-flex flex-column">
                <h6 class="card-title">
                    <a href="{{ url_for('products.detail', id=product.id) }}" class="text-decoration-none">
                        {{ product.title }}
                    </a>
                </h6>
                
                <p class="card-text text-muted small flex-grow-1">
                    {{ product.description[:80] }}{% if product.description|length > 80 %}...{% endif %}
                </p>
                
                <div class="mt-auto">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-danger fw-bold">¥{{ "%.2f"|format(product.price) }}</span>
                        <small class="text-muted">
                            <i class="fas fa-eye"></i> {{ product.view_count }}
                            <i class="fas fa-heart ms-1"></i> {{ product.favorite_count }}
                        </small>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            {{ moment(product.created_at).fromNow() }}
                        </small>
                        <div class="btn-group btn-group-sm">
                            <a href="{{ url_for('products.detail', id=product.id) }}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('products.edit', id=product.id) }}" 
                               class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="deleteProduct({{ product.id }})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 分页 -->
{% if products.pages > 1 %}
<nav aria-label="商品分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if products.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('products.my_products', page=products.prev_num, status=status_filter) }}">
                上一页
            </a>
        </li>
        {% endif %}
        
        {% for page_num in products.iter_pages() %}
            {% if page_num %}
                {% if page_num != products.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('products.my_products', page=page_num, status=status_filter) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if products.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('products.my_products', page=products.next_num, status=status_filter) }}">
                下一页
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- 空状态 -->
<div class="empty-state">
    <i class="fas fa-box-open fa-5x text-muted mb-3"></i>
    <h4 class="text-muted">
        {% if status_filter == 'all' %}
        还没有发布商品
        {% else %}
        没有{{ status_filter }}状态的商品
        {% endif %}
    </h4>
    <p class="text-muted">快来发布你的第一个商品吧！</p>
    <a href="{{ url_for('products.publish') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 发布商品
    </a>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function deleteProduct(productId) {
    if (confirm('确定要删除这个商品吗？此操作不可撤销。')) {
        $.ajax({
            url: '/products/delete/' + productId,
            method: 'POST',
            data: {
                csrf_token: $('meta[name=csrf-token]').attr('content')
            },
            beforeSend: function() {
                console.log('发送删除请求，商品ID:', productId);
            },
            success: function(response) {
                console.log('删除成功:', response);
                location.reload();
            },
            error: function(xhr, status, error) {
                console.error('删除失败:', { xhr, status, error });
                console.error('响应内容:', xhr.responseText);
                alert('删除失败，请重试。错误信息: ' + (xhr.responseText || error));
            }
        });
    }
}
</script>
{% endblock %}
