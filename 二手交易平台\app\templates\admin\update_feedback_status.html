{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit"></i> 更新反馈状态</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.status.label(class="form-label") }}
                        {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                        {% if form.status.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.status.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> 
                            选择反馈的处理状态
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.user_note.label(class="form-label") }}
                        {{ form.user_note(class="form-control" + (" is-invalid" if form.user_note.errors else ""), rows="4", placeholder="可选：添加状态更新的说明...") }}
                        {% if form.user_note.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.user_note.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            <i class="fas fa-lightbulb"></i> 
                            您可以添加备注说明状态更新的原因
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('admin_panel.feedback_detail', id=feedback.id) }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- 反馈信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> 反馈信息</h6>
            </div>
            <div class="card-body">
                <h6>{{ feedback.title }}</h6>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>当前状态:</strong></div>
                    <div class="col-7">
                        {% if feedback.status == 'pending' %}
                        <span class="badge bg-warning">待处理</span>
                        {% elif feedback.status == 'processing' %}
                        <span class="badge bg-info">处理中</span>
                        {% elif feedback.status == 'waiting_user' %}
                        <span class="badge bg-primary">等待用户回复</span>
                        {% elif feedback.status == 'resolved' %}
                        <span class="badge bg-success">已解决</span>
                        {% elif feedback.status == 'closed' %}
                        <span class="badge bg-secondary">已关闭</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>反馈类型:</strong></div>
                    <div class="col-7">
                        {% if feedback.type == 'bug' %}
                        <span class="badge bg-danger">问题反馈</span>
                        {% elif feedback.type == 'suggestion' %}
                        <span class="badge bg-info">功能建议</span>
                        {% elif feedback.type == 'complaint' %}
                        <span class="badge bg-warning">投诉举报</span>
                        {% elif feedback.type == 'contact' %}
                        <span class="badge bg-primary">联系我们</span>
                        {% else %}
                        <span class="badge bg-secondary">其他</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>提交用户:</strong></div>
                    <div class="col-7">
                        {% if feedback.user %}
                            {{ feedback.user.nickname or feedback.user.username }}
                        {% else %}
                            访客用户
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-5"><strong>提交时间:</strong></div>
                    <div class="col-7">{{ feedback.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                
                <div class="mb-3">
                    <strong>反馈内容:</strong>
                    <div class="mt-2 p-3 bg-light rounded">
                        {{ feedback.content[:200] }}{% if feedback.content|length > 200 %}...{% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态说明 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-question-circle"></i> 状态说明</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <span class="badge bg-warning me-2">待处理</span>
                    <small>反馈已提交，等待处理</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-info me-2">处理中</span>
                    <small>正在处理用户的反馈</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-primary me-2">等待用户回复</span>
                    <small>已回复用户，等待用户进一步回复</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-success me-2">已解决</span>
                    <small>问题已得到解决</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-secondary me-2">已关闭</span>
                    <small>反馈已关闭，不再处理</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
