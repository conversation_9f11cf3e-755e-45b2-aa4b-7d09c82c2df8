#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库结构
"""

import sqlite3
import os

def fix_database_schema():
    """修复数据库表结构"""
    db_path = 'app/app.db'
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 修复数据库表结构 ===\n")
        
        # 检查categories表是否有sort_order字段
        cursor.execute("PRAGMA table_info(categories);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'sort_order' not in column_names:
            print("📁 添加categories表的sort_order字段...")
            cursor.execute("ALTER TABLE categories ADD COLUMN sort_order INTEGER DEFAULT 0")
            
            # 为现有分类设置排序
            cursor.execute("UPDATE categories SET sort_order = id WHERE sort_order = 0")
            print("✅ sort_order字段添加完成")
        else:
            print("✅ categories表的sort_order字段已存在")
        
        # 检查users表是否有location字段
        cursor.execute("PRAGMA table_info(users);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'location' not in column_names:
            print("👤 添加users表的location字段...")
            cursor.execute("ALTER TABLE users ADD COLUMN location VARCHAR(200)")
            print("✅ location字段添加完成")
        else:
            print("✅ users表的location字段已存在")
        
        # 检查是否有管理员用户
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            print("👤 添加管理员用户...")
            from werkzeug.security import generate_password_hash
            
            admin_data = (
                'admin', '<EMAIL>', generate_password_hash('admin123'), 
                '管理员', None, '系统管理员', 'admin', True, True, 'datetime("now")', 'datetime("now")', 'datetime("now")', '系统'
            )
            
            cursor.execute('''
            INSERT INTO users (username, email, password_hash, nickname, avatar, bio, role, is_active, email_verified, created_at, updated_at, last_seen, location)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime("now"), datetime("now"), datetime("now"), ?)
            ''', admin_data)
            
            print("✅ 管理员用户添加完成")
        else:
            print("✅ 管理员用户已存在")
        
        # 更新分类数据，确保有颜色
        print("🎨 更新分类颜色...")
        color_updates = [
            (1, '#3498db'),  # 电子产品 - 蓝色
            (2, '#e74c3c'),  # 服装配饰 - 红色
            (3, '#2ecc71'),  # 家居用品 - 绿色
            (4, '#f39c12'),  # 图书文具 - 橙色
            (5, '#9b59b6'),  # 运动户外 - 紫色
            (6, '#e91e63'),  # 美妆护肤 - 粉色
            (7, '#ff9800'),  # 母婴用品 - 橙色
            (8, '#95a5a6'),  # 其他 - 灰色
            (9, '#1abc9c'),  # 额外分类 - 青色
        ]
        
        for cat_id, color in color_updates:
            cursor.execute("UPDATE categories SET color = ? WHERE id = ?", (color, cat_id))
        
        conn.commit()
        conn.close()
        print("✅ 数据库结构修复完成")
        
    except Exception as e:
        print(f"❌ 修复数据库时出错: {e}")

def check_final_data():
    """检查最终数据"""
    db_path = 'app/app.db'
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n=== 最终数据检查 ===")
        
        # 检查分类数据
        cursor.execute("SELECT id, name, color, sort_order FROM categories ORDER BY sort_order")
        categories = cursor.fetchall()
        print(f"📁 分类数量: {len(categories)}")
        for cat in categories:
            print(f"  - ID:{cat[0]} 名称:{cat[1]} 颜色:{cat[2]} 排序:{cat[3]}")
        
        # 检查商品数据
        cursor.execute("SELECT COUNT(*) FROM products")
        product_count = cursor.fetchone()[0]
        print(f"\n🛍️ 商品数量: {product_count}")
        
        # 检查用户数据
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"👤 用户数量: {user_count}")
        
        cursor.execute("SELECT username, role FROM users")
        users = cursor.fetchall()
        for user in users:
            print(f"  - 用户:{user[0]} 角色:{user[1]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据时出错: {e}")

if __name__ == '__main__':
    fix_database_schema()
    check_final_data()
