package com.second.hand.data.model.enums

import com.google.gson.annotations.SerializedName

/**
 * 订单状态枚举
 * 对应Flask后端的OrderStatus枚举
 */
enum class OrderStatus {
    @SerializedName("pending")
    PENDING,        // 待付款
    
    @SerializedName("paid")
    PAID,           // 已付款
    
    @SerializedName("shipped")
    SHIPPED,        // 已发货
    
    @SerializedName("delivered")
    DELIVERED,      // 已送达
    
    @SerializedName("completed")
    COMPLETED,      // 已完成
    
    @SerializedName("cancelled")
    CANCELLED       // 已取消
}
