#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单管理路由
"""

import uuid
from datetime import datetime, timedelta
from flask import render_template, redirect, url_for, flash, request, current_app, abort, jsonify
from flask_login import login_required, current_user
from app import db
from app.orders import bp
from app.orders.forms import CreateOrderForm, UpdateOrderForm, ReviewForm, MessageForm
from app.models import Order, Product, Review, Message, OrderStatus, ProductStatus, Notification, local_now
from sqlalchemy import desc, or_

@bp.route('/create/<int:product_id>', methods=['GET', 'POST'])
@login_required
def create(product_id):
    """创建订单"""
    product = Product.query.get_or_404(product_id)
    
    # 检查商品状态
    if product.status != ProductStatus.ACTIVE:
        flash('该商品不可购买', 'error')
        return redirect(url_for('products.detail', id=product_id))
    
    # 不能购买自己的商品
    if product.seller_id == current_user.id:
        flash('不能购买自己的商品', 'error')
        return redirect(url_for('products.detail', id=product_id))
    
    form = CreateOrderForm()
    
    if form.validate_on_submit():
        # 生成订单号
        order_no = f"ORD{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
        
        order = Order(
            order_no=order_no,
            buyer_id=current_user.id,
            seller_id=product.seller_id,
            product_id=product.id,
            quantity=form.quantity.data,
            price=product.price,
            total_amount=product.price * form.quantity.data,
            shipping_name=form.shipping_name.data,
            shipping_phone=form.shipping_phone.data,
            shipping_address=form.shipping_address.data,
            buyer_note=form.buyer_note.data,
            status=OrderStatus.PENDING
        )
        
        db.session.add(order)
        
        # 创建通知
        notification = Notification(
            user_id=product.seller_id,
            title='新订单通知',
            content=f'您的商品"{product.title}"有新订单，订单号：{order_no}',
            type='order',
            related_id=order.id
        )
        db.session.add(notification)
        
        db.session.commit()
        
        flash('订单创建成功！', 'success')
        return redirect(url_for('orders.detail', id=order.id))
    
    return render_template('orders/create.html',
                         title='创建订单',
                         form=form,
                         product=product)

@bp.route('/detail/<int:id>')
@login_required
def detail(id):
    """订单详情"""
    order = Order.query.get_or_404(id)
    
    # 只有买家、卖家或管理员才能查看订单
    if current_user.id not in [order.buyer_id, order.seller_id] and not current_user.is_admin():
        abort(403)
    
    return render_template('orders/detail.html',
                         title=f'订单详情 - {order.order_no}',
                         order=order)

@bp.route('/send_message/<int:product_id>', methods=['GET', 'POST'])
@login_required
def send_message(product_id):
    """联系卖家"""
    product = Product.query.get_or_404(product_id)
    
    # 不能给自己发消息
    if product.seller_id == current_user.id:
        flash('不能给自己发消息', 'error')
        return redirect(url_for('products.detail', id=product_id))
    
    form = MessageForm()
    
    if form.validate_on_submit():
        # 检查是否在短时间内发送了相同内容的消息（防重复提交）
        recent_message = Message.query.filter(
            Message.sender_id == current_user.id,
            Message.receiver_id == product.seller_id,
            Message.product_id == product.id,
            Message.content == form.content.data.strip(),
            Message.created_at >= datetime.now() - timedelta(seconds=30)  # 30秒内
        ).first()

        if recent_message:
            flash('请不要重复发送相同的消息', 'warning')
            return redirect(url_for('orders.send_message', product_id=product_id, _anchor='messages'))

        try:
            message = Message(
                sender_id=current_user.id,
                receiver_id=product.seller_id,
                product_id=product.id,
                content=form.content.data.strip()
            )

            db.session.add(message)

            # 创建通知
            notification = Notification(
                user_id=product.seller_id,
                title='新消息通知',
                content=f'您收到来自 {current_user.nickname or current_user.username} 关于商品"{product.title}"的消息',
                type='message',
                related_id=message.id
            )
            db.session.add(notification)

            db.session.commit()

            flash('消息发送成功！', 'success')
            # 使用POST-redirect-GET模式防止重复提交
            return redirect(url_for('orders.send_message', product_id=product_id, _anchor='messages'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'发送消息失败: {str(e)}')
            flash('消息发送失败，请重试', 'error')
            return redirect(url_for('orders.send_message', product_id=product_id))
    
    # 获取历史消息
    messages = Message.query.filter(
        Message.product_id == product_id,
        or_(
            (Message.sender_id == current_user.id) & (Message.receiver_id == product.seller_id),
            (Message.sender_id == product.seller_id) & (Message.receiver_id == current_user.id)
        )
    ).order_by(Message.created_at.asc()).all()
    
    # 标记消息为已读
    unread_messages = Message.query.filter(
        Message.product_id == product_id,
        Message.receiver_id == current_user.id,
        Message.is_read == False
    ).all()

    if unread_messages:
        # 标记消息为已读
        for msg in unread_messages:
            msg.is_read = True

        # 同时标记相关的消息通知为已读
        message_ids = [msg.id for msg in unread_messages]
        Notification.query.filter(
            Notification.user_id == current_user.id,
            Notification.type == 'message',
            Notification.related_id.in_(message_ids),
            Notification.is_read == False
        ).update({'is_read': True}, synchronize_session=False)

        db.session.commit()
    
    return render_template('orders/send_message.html',
                         title=f'联系卖家 - {product.title}',
                         form=form,
                         product=product,
                         messages=messages)

@bp.route('/my_orders')
@login_required
def my_orders():
    """我的订单"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    order_type = request.args.get('type', 'buy')  # buy: 我买的, sell: 我卖的
    
    if order_type == 'sell':
        query = Order.query.filter_by(seller_id=current_user.id)
    else:
        query = Order.query.filter_by(buyer_id=current_user.id)
    
    if status_filter != 'all':
        if status_filter == 'pending':
            query = query.filter_by(status=OrderStatus.PENDING)
        elif status_filter == 'paid':
            query = query.filter_by(status=OrderStatus.PAID)
        elif status_filter == 'shipped':
            query = query.filter_by(status=OrderStatus.SHIPPED)
        elif status_filter == 'delivered':
            query = query.filter_by(status=OrderStatus.DELIVERED)
        elif status_filter == 'completed':
            query = query.filter_by(status=OrderStatus.COMPLETED)
        elif status_filter == 'cancelled':
            query = query.filter_by(status=OrderStatus.CANCELLED)
    
    orders = query.order_by(desc(Order.created_at)).paginate(
        page=page,
        per_page=current_app.config['ORDERS_PER_PAGE'],
        error_out=False
    )
    
    return render_template('orders/my_orders.html',
                         title='我的订单',
                         orders=orders,
                         status_filter=status_filter,
                         order_type=order_type)

@bp.route('/update/<int:id>', methods=['GET', 'POST'])
@login_required
def update(id):
    """更新订单状态"""
    order = Order.query.get_or_404(id)
    
    # 只有卖家或管理员才能更新订单状态
    if current_user.id != order.seller_id and not current_user.is_admin():
        abort(403)
    
    form = UpdateOrderForm()
    
    if form.validate_on_submit():
        old_status = order.status
        new_status = OrderStatus(form.status.data)
        
        order.status = new_status
        order.seller_note = form.seller_note.data
        
        # 更新时间戳
        now = local_now()
        if new_status == OrderStatus.PAID and old_status != OrderStatus.PAID:
            order.paid_at = now
        elif new_status == OrderStatus.SHIPPED and old_status != OrderStatus.SHIPPED:
            order.shipped_at = now
        elif new_status == OrderStatus.DELIVERED and old_status != OrderStatus.DELIVERED:
            order.delivered_at = now
        elif new_status == OrderStatus.COMPLETED and old_status != OrderStatus.COMPLETED:
            order.completed_at = now
            # 商品在付款时已经标记为售出，这里不需要重复标记
        
        # 创建通知
        status_messages = {
            OrderStatus.PAID: '订单已付款',
            OrderStatus.SHIPPED: '订单已发货',
            OrderStatus.DELIVERED: '订单已送达',
            OrderStatus.COMPLETED: '订单已完成',
            OrderStatus.CANCELLED: '订单已取消'
        }
        
        if new_status in status_messages:
            notification = Notification(
                user_id=order.buyer_id,
                title='订单状态更新',
                content=f'您的订单{order.order_no}状态已更新：{status_messages[new_status]}',
                type='order',
                related_id=order.id
            )
            db.session.add(notification)
        
        db.session.commit()
        
        flash('订单状态已更新', 'success')
        return redirect(url_for('orders.detail', id=order.id))
    
    elif request.method == 'GET':
        form.status.data = order.status.value
        form.seller_note.data = order.seller_note
    
    return render_template('orders/update.html',
                         title='更新订单',
                         form=form,
                         order=order)

@bp.route('/cancel/<int:id>', methods=['POST'])
@login_required
def cancel(id):
    """取消订单"""
    order = Order.query.get_or_404(id)

    # 只有买家或管理员才能取消订单
    if current_user.id != order.buyer_id and not current_user.is_admin():
        abort(403)

    # 只有待付款状态的订单才能取消
    if order.status != OrderStatus.PENDING:
        flash('该订单无法取消', 'error')
        return redirect(url_for('orders.detail', id=order.id))

    order.status = OrderStatus.CANCELLED

    # 创建通知
    notification = Notification(
        user_id=order.seller_id,
        title='订单取消通知',
        content=f'订单{order.order_no}已被买家取消',
        type='order',
        related_id=order.id
    )
    db.session.add(notification)

    db.session.commit()

    flash('订单已取消', 'success')
    return redirect(url_for('orders.my_orders'))

@bp.route('/review/<int:id>', methods=['GET', 'POST'])
@login_required
def review(id):
    """评价订单"""
    order = Order.query.get_or_404(id)

    # 只有买家才能评价
    if current_user.id != order.buyer_id:
        abort(403)

    # 只有已完成的订单才能评价
    if order.status != OrderStatus.COMPLETED:
        flash('只有已完成的订单才能评价', 'error')
        return redirect(url_for('orders.detail', id=order.id))

    # 检查是否已经评价过
    if order.review:
        flash('该订单已经评价过了', 'info')
        return redirect(url_for('orders.detail', id=order.id))

    form = ReviewForm()

    if form.validate_on_submit():
        review = Review(
            order_id=order.id,
            reviewer_id=current_user.id,
            reviewee_id=order.seller_id,
            rating=form.rating.data,
            content=form.content.data
        )

        db.session.add(review)

        # 创建通知
        notification = Notification(
            user_id=order.seller_id,
            title='新评价通知',
            content=f'您收到了来自订单{order.order_no}的{form.rating.data}星评价',
            type='review',
            related_id=review.id
        )
        db.session.add(notification)

        db.session.commit()

        flash('评价提交成功', 'success')
        return redirect(url_for('orders.detail', id=order.id))

    return render_template('orders/review.html',
                         title='评价订单',
                         form=form,
                         order=order)

@bp.route('/messages/<int:product_id>')
@login_required
def messages(product_id):
    """商品消息列表"""
    product = Product.query.get_or_404(product_id)

    # 只有买家和卖家才能查看消息
    if current_user.id not in [product.seller_id] and \
       not Message.query.filter_by(sender_id=current_user.id, product_id=product_id).first():
        abort(403)

    page = request.args.get('page', 1, type=int)

    messages = Message.query.filter_by(product_id=product_id).filter(
        or_(
            Message.sender_id == current_user.id,
            Message.receiver_id == current_user.id
        )
    ).order_by(Message.created_at.asc()).paginate(
        page=page,
        per_page=20,
        error_out=False
    )

    # 标记消息为已读
    unread_messages = Message.query.filter_by(
        product_id=product_id,
        receiver_id=current_user.id,
        is_read=False
    ).all()

    if unread_messages:
        # 标记消息为已读
        for msg in unread_messages:
            msg.is_read = True

        # 同时标记相关的消息通知为已读
        message_ids = [msg.id for msg in unread_messages]
        Notification.query.filter(
            Notification.user_id == current_user.id,
            Notification.type == 'message',
            Notification.related_id.in_(message_ids),
            Notification.is_read == False
        ).update({'is_read': True}, synchronize_session=False)

        db.session.commit()

    return render_template('orders/messages.html',
                         title='商品消息',
                         messages=messages,
                         product=product)

# 订单状态操作API
@bp.route('/<int:id>/pay', methods=['POST'])
@login_required
def pay_order(id):
    """付款"""
    try:
        order = Order.query.get_or_404(id)

        # 只有买家可以付款
        if current_user.id != order.buyer_id:
            return jsonify({'success': False, 'message': '无权限操作'}), 403

        if order.status != OrderStatus.PENDING:
            return jsonify({'success': False, 'message': '订单状态不正确，当前状态：' + order.status.value}), 400

        # 检查商品是否还在售
        if order.product.status != ProductStatus.ACTIVE:
            return jsonify({'success': False, 'message': '商品已下架，无法付款'}), 400

        # 更新订单状态
        order.status = OrderStatus.PAID
        order.paid_at = local_now()
        order.updated_at = local_now()

        # 更新商品已售数量
        order.product.sold_quantity += order.quantity
        order.product.updated_at = local_now()

        # 如果商品已售完，则标记为已售出
        if order.product.is_sold_out():
            order.product.status = ProductStatus.SOLD

        # 创建通知
        notification = Notification(
            user_id=order.seller_id,
            title='订单付款通知',
            content=f'订单 {order.order_no} 已付款，请及时发货',
            type='order',
            related_id=order.id
        )
        db.session.add(notification)

        db.session.commit()

        # WebSocket实时通知已移除，改为传统模式

        return jsonify({'success': True, 'message': '付款成功，请等待卖家发货'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '付款失败'}), 500

@bp.route('/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_order(id):
    """取消订单"""
    try:
        order = Order.query.get_or_404(id)

        # 添加调试日志
        current_app.logger.info(f'取消订单请求 - 订单ID: {id}, 当前用户ID: {current_user.id}, 买家ID: {order.buyer_id}, 订单状态: {order.status.value}')

        # 只有买家可以取消订单
        if current_user.id != order.buyer_id:
            current_app.logger.warning(f'权限检查失败 - 当前用户ID: {current_user.id}, 买家ID: {order.buyer_id}')
            return jsonify({'success': False, 'message': f'无权限操作，当前用户ID: {current_user.id}, 买家ID: {order.buyer_id}'}), 403

        # 检查订单状态
        if order.status not in [OrderStatus.PENDING, OrderStatus.PAID]:
            status_names = {
                OrderStatus.PENDING: '待付款',
                OrderStatus.PAID: '已付款',
                OrderStatus.SHIPPED: '已发货',
                OrderStatus.DELIVERED: '已送达',
                OrderStatus.COMPLETED: '已完成',
                OrderStatus.CANCELLED: '已取消'
            }
            current_status = status_names.get(order.status, order.status.value)
            current_app.logger.warning(f'状态检查失败 - 当前状态: {order.status.value}')
            return jsonify({'success': False, 'message': f'当前订单状态为"{current_status}"，不允许取消'}), 400

        # 如果订单已付款，需要恢复商品数量
        if order.status == OrderStatus.PAID:
            order.product.sold_quantity -= order.quantity
            # 如果商品之前是已售出状态，现在有库存了，恢复为在售状态
            if order.product.status == ProductStatus.SOLD and not order.product.is_sold_out():
                order.product.status = ProductStatus.ACTIVE
            order.product.updated_at = local_now()

        order.status = OrderStatus.CANCELLED
        order.updated_at = local_now()

        # 创建通知
        notification = Notification(
            user_id=order.seller_id,
            title='订单取消通知',
            content=f'订单 {order.order_no} 已被买家取消',
            type='order',
            related_id=order.id
        )
        db.session.add(notification)

        db.session.commit()

        # WebSocket实时通知已移除，改为传统模式

        return jsonify({'success': True, 'message': '订单已取消'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'取消订单失败: {str(e)}')
        return jsonify({'success': False, 'message': '取消失败，请重试'}), 500

@bp.route('/<int:id>/ship', methods=['POST'])
@login_required
def ship_order(id):
    """发货"""
    order = Order.query.get_or_404(id)
    
    # 只有卖家可以发货
    if current_user.id != order.seller_id:
        return jsonify({'success': False, 'message': '无权限操作'}), 403
    
    if order.status != OrderStatus.PAID:
        return jsonify({'success': False, 'message': '订单状态不正确'}), 400
    
    try:
        order.status = OrderStatus.SHIPPED
        order.shipped_at = local_now()
        order.updated_at = local_now()
        
        # 创建通知
        notification = Notification(
            user_id=order.buyer_id,
            title='订单发货通知',
            content=f'订单 {order.order_no} 已发货，请注意查收',
            type='order',
            related_id=order.id
        )
        db.session.add(notification)
        
        db.session.commit()
        return jsonify({'success': True, 'message': '发货成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '发货失败'}), 500

@bp.route('/<int:id>/confirm', methods=['POST'])
@login_required
def confirm_order(id):
    """确认收货"""
    order = Order.query.get_or_404(id)
    
    # 只有买家可以确认收货
    if current_user.id != order.buyer_id:
        return jsonify({'success': False, 'message': '无权限操作'}), 403
    
    if order.status not in [OrderStatus.SHIPPED, OrderStatus.DELIVERED]:
        return jsonify({'success': False, 'message': '订单状态不正确，只有已发货或已送达的订单才能确认收货'}), 400
    
    try:
        # 如果是已发货状态，先更新为已送达，再更新为已完成
        if order.status == OrderStatus.SHIPPED:
            order.status = OrderStatus.DELIVERED
            order.delivered_at = local_now()

        # 确认收货后直接更新为已完成
        order.status = OrderStatus.COMPLETED
        order.completed_at = local_now()
        order.updated_at = local_now()
        
        # 创建通知
        notification = Notification(
            user_id=order.seller_id,
            title='订单确认收货通知',
            content=f'订单 {order.order_no} 买家已确认收货',
            type='order',
            related_id=order.id
        )
        db.session.add(notification)

        db.session.commit()

        # WebSocket实时通知已移除，改为传统模式

        return jsonify({'success': True, 'message': '确认收货成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '确认收货失败'}), 500
