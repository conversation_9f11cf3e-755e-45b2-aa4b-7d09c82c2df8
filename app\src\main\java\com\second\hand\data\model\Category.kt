package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 商品分类数据模型
 * 对应Flask后端的Category模型
 */
@Entity(tableName = "categories")
data class Category(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    @SerializedName("name")
    var name: String = "",

    @SerializedName("description")
    var description: String? = null,

    @SerializedName("icon")
    var icon: String? = null,

    @SerializedName("is_active")
    var isActive: Boolean = true
) {
    /**
     * 获取分类图标
     * 如果没有自定义图标，返回默认图标
     */
    fun getIconResource(): String {
        return icon ?: getDefaultIcon()
    }
    
    /**
     * 根据分类名称获取默认图标
     */
    private fun getDefaultIcon(): String {
        return when (name) {
            "电子产品", "手机数码" -> "📱"
            "电脑办公" -> "💻"
            "家用电器" -> "📺"
            "服装配饰", "男装", "女装" -> "👕"
            "鞋靴" -> "👟"
            "箱包" -> "🎒"
            "家居用品", "家具" -> "🏠"
            "厨具" -> "🍴"
            "图书文具", "图书" -> "📚"
            "文具" -> "✏️"
            "运动户外", "健身" -> "🏃"
            "美妆护肤" -> "💄"
            "食品饮料" -> "🍎"
            "母婴用品" -> "👶"
            "玩具" -> "🧸"
            "汽车用品" -> "🚗"
            else -> "📦"
        }
    }
    
    /**
     * 获取分类颜色（用于UI显示）
     */
    fun getCategoryColor(): String {
        val colors = listOf(
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
            "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
            "#A3E4D7", "#F9E79F", "#FADBD8", "#D5DBDB", "#AED6F1"
        )
        return colors[id % colors.size]
    }
}
