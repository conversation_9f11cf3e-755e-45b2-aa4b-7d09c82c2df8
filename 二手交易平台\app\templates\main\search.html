{% extends "base.html" %}

{% block content %}
<!-- 搜索过滤器 -->
<div class="filter-section">
    <form method="GET" class="row g-3">
        <!-- 关键词搜索 -->
        <div class="col-md-4">
            <label class="form-label">关键词</label>
            <input type="text" class="form-control" name="q" value="{{ query }}" placeholder="搜索商品...">
        </div>
        
        <!-- 分类筛选 -->
        <div class="col-md-2">
            <label class="form-label">分类</label>
            <select class="form-select" name="category">
                <option value="">全部分类</option>
                {% for category in categories %}
                <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>
                    {{ category.name }}
                </option>
                {% endfor %}
            </select>
        </div>
        
        <!-- 价格筛选 -->
        <div class="col-md-2">
            <label class="form-label">最低价格</label>
            <input type="number" class="form-control" name="min_price" value="{{ min_price or '' }}" 
                   placeholder="0" min="0" step="0.01">
        </div>
        
        <div class="col-md-2">
            <label class="form-label">最高价格</label>
            <input type="number" class="form-control" name="max_price" value="{{ max_price or '' }}" 
                   placeholder="不限" min="0" step="0.01">
        </div>
        
        <!-- 排序方式 -->
        <div class="col-md-2">
            <label class="form-label">排序</label>
            <select class="form-select" name="sort">
                <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>最新发布</option>
                <option value="price_asc" {% if sort_by == 'price_asc' %}selected{% endif %}>价格从低到高</option>
                <option value="price_desc" {% if sort_by == 'price_desc' %}selected{% endif %}>价格从高到低</option>
                <option value="popular" {% if sort_by == 'popular' %}selected{% endif %}>最受欢迎</option>
            </select>
        </div>
        
        <!-- 搜索按钮 -->
        <div class="col-12">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search"></i> 搜索
            </button>
            <a href="{{ url_for('main.search') }}" class="btn btn-outline-secondary">
                <i class="fas fa-undo"></i> 重置
            </a>
        </div>
    </form>
</div>

<!-- 搜索结果 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4>
        {% if query %}
        "{{ query }}" 的搜索结果
        {% else %}
        全部商品
        {% endif %}
        <small class="text-muted">(共 {{ products.total }} 件商品)</small>
    </h4>
    
    <!-- 视图切换 -->
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-secondary active" id="gridView">
            <i class="fas fa-th"></i>
        </button>
        <button type="button" class="btn btn-outline-secondary" id="listView">
            <i class="fas fa-list"></i>
        </button>
    </div>
</div>

<!-- 商品列表 -->
{% if products.items %}
<div id="productContainer">
    <!-- 网格视图 -->
    <div id="gridViewContainer" class="row">
        {% for product in products.items %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card h-100 product-card">
                <div class="position-relative">
                    <a href="{{ url_for('products.detail', id=product.id) }}">
                        <img src="{{ product.get_main_image_url() }}"
                             class="card-img-top" alt="{{ product.title }}" style="height: 200px; object-fit: cover;">
                    </a>
                    
                    <!-- 商品状态标签 -->
                    {% if product.status.value == 'sold' %}
                    <span class="position-absolute top-0 start-0 badge bg-secondary m-2">已售</span>
                    {% elif product.original_price and product.original_price > product.price %}
                    <span class="position-absolute top-0 start-0 badge bg-danger m-2">
                        {{ "%.0f"|format((1 - product.price/product.original_price) * 100) }}% OFF
                    </span>
                    {% endif %}
                    
                    <!-- 收藏按钮 -->
                    {% if current_user.is_authenticated and current_user.id != product.seller_id %}
                    <button class="btn btn-sm btn-outline-danger position-absolute top-0 end-0 m-2 favorite-btn" 
                            data-product-id="{{ product.id }}">
                        <i class="far fa-heart"></i>
                    </button>
                    {% endif %}
                </div>
                
                <div class="card-body d-flex flex-column">
                    <h6 class="card-title">
                        <a href="{{ url_for('products.detail', id=product.id) }}" class="text-decoration-none">
                            {{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}
                        </a>
                    </h6>
                    
                    <p class="card-text text-muted small flex-grow-1">
                        {{ product.description[:80] }}{% if product.description|length > 80 %}...{% endif %}
                    </p>
                    
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-danger fw-bold h6 mb-0">¥{{ "%.2f"|format(product.price) }}</span>
                            {% if product.original_price and product.original_price > product.price %}
                            <small class="text-muted text-decoration-line-through">
                                ¥{{ "%.2f"|format(product.original_price) }}
                            </small>
                            {% endif %}
                        </div>
                        
                        <!-- 商品信息列表 -->
                        <div class="product-info-list-grid mt-2">
                            <div class="info-item-grid mb-1">
                                <small class="text-muted">分类:</small>
                                <span>{{ product.category.name }}</span>
                            </div>
                            <div class="info-item-grid mb-1">
                                <small class="text-muted">痕迹:</small>
                                <span>{{ product.condition }}</span>
                            </div>
                            <div class="info-item-grid mb-1">
                                <small class="text-muted">发布者:</small>
                                <span>{{ product.seller.nickname or product.seller.username }}</span>
                            </div>
                            <div class="info-item-grid mb-1">
                                <small class="text-muted">地址:</small>
                                <span>{{ product.location or '未知' }}</span>
                            </div>
                            <div class="info-item-grid mb-1">
                                <small class="text-muted">浏览:</small>
                                <span>{{ product.view_count }}</span>
                            </div>
                            <div class="info-item-grid mb-1">
                                <small class="text-muted">点赞:</small>
                                <span>{{ product.favorite_count }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- 列表视图 -->
    <div id="listViewContainer" class="d-none">
        {% for product in products.items %}
        <div class="card mb-3 product-card">
            <div class="row g-0">
                <div class="col-md-3">
                    <a href="{{ url_for('products.detail', id=product.id) }}">
                        <img src="{{ product.get_main_image_url() }}"
                             class="img-fluid rounded-start h-100" alt="{{ product.title }}"
                             style="object-fit: cover; min-height: 200px;">
                    </a>
                </div>
                <div class="col-md-9">
                    <div class="card-body h-100 d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title">
                                <a href="{{ url_for('products.detail', id=product.id) }}" class="text-decoration-none">
                                    {{ product.title }}
                                </a>
                            </h5>
                            {% if current_user.is_authenticated and current_user.id != product.seller_id %}
                            <button class="btn btn-sm btn-outline-danger favorite-btn" data-product-id="{{ product.id }}">
                                <i class="far fa-heart"></i>
                            </button>
                            {% endif %}
                        </div>
                        
                        <p class="card-text flex-grow-1">{{ product.description[:150] }}{% if product.description|length > 150 %}...{% endif %}</p>
                        
                        <div class="row align-items-end">
                            <div class="col-12 col-md-6 mb-2 mb-md-0">
                                <div class="price-info">
                                    <span class="text-danger fw-bold h4">¥{{ "%.2f"|format(product.price) }}</span>
                                    {% if product.original_price and product.original_price > product.price %}
                                    <small class="text-muted text-decoration-line-through ms-2">
                                        ¥{{ "%.2f"|format(product.original_price) }}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-12 col-md-6 text-md-start">
                                <div class="product-info-list">
                                    <div class="info-item mb-1">
                                        <small class="text-muted">分类:</small>
                                        <span>{{ product.category.name }}</span>
                                    </div>
                                    <div class="info-item mb-1">
                                        <small class="text-muted">痕迹:</small>
                                        <span>{{ product.condition }}</span>
                                    </div>
                                    <div class="info-item mb-1">
                                        <small class="text-muted">发布者:</small>
                                        <span>{{ product.seller.nickname or product.seller.username }}</span>
                                    </div>
                                    <div class="info-item mb-1">
                                        <small class="text-muted">地址:</small>
                                        <span>{{ product.location or '未知' }}</span>
                                    </div>
                                    <div class="info-item mb-1">
                                        <small class="text-muted">浏览:</small>
                                        <span>{{ product.view_count }}</span>
                                    </div>
                                    <div class="info-item mb-1">
                                        <small class="text-muted">点赞:</small>
                                        <span>{{ product.favorite_count }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- 分页 -->
{% if products.pages > 1 %}
<nav aria-label="商品分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if products.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('main.search', page=products.prev_num, q=query, category=category_id, min_price=min_price, max_price=max_price, sort=sort_by) }}">
                <i class="fas fa-chevron-left"></i> 上一页
            </a>
        </li>
        {% endif %}
        
        {% for page_num in products.iter_pages() %}
            {% if page_num %}
                {% if page_num != products.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('main.search', page=page_num, q=query, category=category_id, min_price=min_price, max_price=max_price, sort=sort_by) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if products.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('main.search', page=products.next_num, q=query, category=category_id, min_price=min_price, max_price=max_price, sort=sort_by) }}">
                下一页 <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- 空状态 -->
<div class="empty-state">
    <i class="fas fa-search fa-5x text-muted mb-3"></i>
    <h4 class="text-muted">没有找到相关商品</h4>
    <p class="text-muted">试试调整搜索条件或浏览其他分类</p>
    <a href="{{ url_for('main.index') }}" class="btn btn-primary">返回首页</a>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 视图切换
    $('#gridView').click(function() {
        $(this).addClass('active');
        $('#listView').removeClass('active');
        $('#gridViewContainer').removeClass('d-none');
        $('#listViewContainer').addClass('d-none');
        localStorage.setItem('viewMode', 'grid');
    });
    
    $('#listView').click(function() {
        $(this).addClass('active');
        $('#gridView').removeClass('active');
        $('#listViewContainer').removeClass('d-none');
        $('#gridViewContainer').addClass('d-none');
        localStorage.setItem('viewMode', 'list');
    });
    
    // 恢复视图模式
    var savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode === 'list') {
        $('#listView').click();
    }
    
    // 价格范围验证
    $('input[name="min_price"], input[name="max_price"]').on('input', function() {
        var minPrice = parseFloat($('input[name="min_price"]').val()) || 0;
        var maxPrice = parseFloat($('input[name="max_price"]').val()) || Infinity;
        
        if (minPrice > maxPrice && maxPrice !== Infinity) {
            $(this).addClass('is-invalid');
            if (!$(this).siblings('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">最低价格不能高于最高价格</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });
});
</script>
{% endblock %}
