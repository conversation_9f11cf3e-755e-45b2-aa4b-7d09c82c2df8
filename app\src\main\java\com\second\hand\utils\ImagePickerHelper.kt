package com.second.hand.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Base64
import java.io.ByteArrayOutputStream
import java.io.InputStream

/**
 * 图片选择和处理工具类
 */
object ImagePickerHelper {
    
    /**
     * 将Uri转换为Base64字符串
     */
    fun uriToBase64(context: Context, uri: Uri): String? {
        return try {
            val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            // 压缩图片
            val compressedBitmap = compressBitmap(bitmap, 800, 800, 80)
            bitmapToBase64(compressedBitmap)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 压缩Bitmap
     */
    private fun compressBitmap(
        bitmap: Bitmap,
        maxWidth: Int,
        maxHeight: Int,
        quality: Int
    ): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        // 计算缩放比例
        val scaleWidth = maxWidth.toFloat() / width
        val scaleHeight = maxHeight.toFloat() / height
        val scale = minOf(scaleWidth, scaleHeight, 1.0f)
        
        // 如果不需要缩放，直接返回原图
        if (scale >= 1.0f) {
            return bitmap
        }
        
        // 创建缩放后的Bitmap
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    /**
     * 将Bitmap转换为Base64字符串
     */
    private fun bitmapToBase64(bitmap: Bitmap, quality: Int = 80): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.DEFAULT)
    }
    
    /**
     * 获取图片的MIME类型
     */
    fun getMimeType(context: Context, uri: Uri): String? {
        return context.contentResolver.getType(uri)
    }
    
    /**
     * 检查文件大小是否超过限制
     */
    fun isFileSizeValid(context: Context, uri: Uri, maxSizeInMB: Int = 5): Boolean {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
            val size = inputStream?.available() ?: 0
            inputStream?.close()
            
            val maxSizeInBytes = maxSizeInMB * 1024 * 1024
            size <= maxSizeInBytes
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 检查是否为有效的图片格式
     */
    fun isValidImageFormat(context: Context, uri: Uri): Boolean {
        val mimeType = getMimeType(context, uri)
        return mimeType?.startsWith("image/") == true
    }
    
    /**
     * 图片数据类
     */
    data class ImageData(
        val uri: Uri,
        val base64: String,
        val isMain: Boolean = false
    )
}
