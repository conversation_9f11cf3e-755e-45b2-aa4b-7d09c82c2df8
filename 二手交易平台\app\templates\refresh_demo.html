{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4><i class="fas fa-sync-alt"></i> 全局实时刷新系统演示</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 系统已启用！</h5>
                        <p class="mb-2">全局实时刷新系统正在运行，默认每0.5秒检查更新。</p>
                        <p class="mb-0"><strong>按 Ctrl+R 显示控制面板</strong> 来管理刷新设置。</p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>🎛️ 控制功能</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>显示控制面板</span>
                                    <kbd>Ctrl + R</kbd>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>暂停/恢复刷新</span>
                                    <kbd>Ctrl + P</kbd>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>手动刷新</span>
                                    <kbd>Ctrl + Shift + R</kbd>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>⚡ 刷新模式</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>智能刷新</strong> - 检测用户活动
                                </li>
                                <li class="list-group-item">
                                    <strong>完全刷新</strong> - 重新加载页面
                                </li>
                                <li class="list-group-item">
                                    <strong>关闭刷新</strong> - 禁用自动刷新
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6>📊 实时数据演示</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <h5 class="text-primary" id="currentTime">--:--:--</h5>
                                            <small class="text-muted">当前时间</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h5 class="text-success" id="refreshCounter">0</h5>
                                            <small class="text-muted">页面刷新次数</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h5 class="text-warning" id="randomNumber">0</h5>
                                            <small class="text-muted">随机数</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h5 class="text-info" id="userActivity">活跃</h5>
                                            <small class="text-muted">用户状态</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6>🧪 交互测试</h6>
                            <div class="card">
                                <div class="card-body">
                                    <p>在下面的输入框中输入内容，观察刷新系统如何智能暂停：</p>
                                    <div class="mb-3">
                                        <label class="form-label">文本输入测试</label>
                                        <input type="text" class="form-control" placeholder="开始输入时刷新会自动暂停...">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">文本域测试</label>
                                        <textarea class="form-control" rows="3" placeholder="在这里输入多行文本..."></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">选择测试</label>
                                        <select class="form-select">
                                            <option>选择一个选项...</option>
                                            <option>选项 1</option>
                                            <option>选项 2</option>
                                            <option>选项 3</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6>📈 性能监控</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div id="performanceInfo" class="text-muted">
                                        性能数据将在这里显示...
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="showPerformanceReport()">
                                        <i class="fas fa-chart-line"></i> 显示性能报告
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

kbd {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
}

#performanceInfo {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    white-space: pre-line;
}
</style>

<script>
$(document).ready(function() {
    let pageRefreshCount = 0;
    let lastActivityTime = Date.now();
    
    // 更新实时数据
    function updateDemoData() {
        // 当前时间
        const now = new Date();
        $('#currentTime').text(now.toLocaleTimeString());
        
        // 随机数
        $('#randomNumber').text(Math.floor(Math.random() * 1000));
        
        // 用户活动状态
        const timeSinceActivity = Date.now() - lastActivityTime;
        const activityStatus = timeSinceActivity < 2000 ? '活跃' : '空闲';
        $('#userActivity').text(activityStatus);
        $('#userActivity').removeClass('text-info text-muted').addClass(
            activityStatus === '活跃' ? 'text-info' : 'text-muted'
        );
    }
    
    // 监听用户活动
    $(document).on('mousemove keypress click scroll', function() {
        lastActivityTime = Date.now();
    });
    
    // 监听全局刷新系统
    if (window.globalRefreshSystem) {
        // 如果刷新系统已经初始化，直接使用
        setupRefreshMonitoring();
    } else {
        // 等待刷新系统初始化
        setTimeout(setupRefreshMonitoring, 1000);
    }
    
    function setupRefreshMonitoring() {
        // 监听刷新事件（如果刷新系统提供了事件接口）
        setInterval(function() {
            if (window.globalRefreshSystem) {
                $('#refreshCounter').text(window.globalRefreshSystem.refreshCount || 0);
            }
        }, 100);
    }
    
    // 定期更新演示数据
    setInterval(updateDemoData, 100);
    
    // 立即更新一次
    updateDemoData();
    
    // 全局函数：显示性能报告
    window.showPerformanceReport = function() {
        if (window.globalRefreshSystem && window.globalRefreshSystem.performanceData) {
            const perfData = window.globalRefreshSystem.performanceData;
            const refreshCount = window.globalRefreshSystem.refreshCount;
            const startTime = window.globalRefreshSystem.startTime;
            
            if (perfData.length > 0) {
                const avgDuration = perfData.reduce((sum, data) => sum + data.duration, 0) / perfData.length;
                const maxDuration = Math.max(...perfData.map(data => data.duration));
                const minDuration = Math.min(...perfData.map(data => data.duration));
                const runtime = ((Date.now() - startTime) / 1000).toFixed(1);
                
                const report = `性能报告 (${new Date().toLocaleTimeString()})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
总刷新次数: ${refreshCount}
运行时间: ${runtime}秒
平均耗时: ${avgDuration.toFixed(2)}ms
最大耗时: ${maxDuration.toFixed(2)}ms
最小耗时: ${minDuration.toFixed(2)}ms
数据样本: ${perfData.length}条
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`;
                
                $('#performanceInfo').text(report);
            } else {
                $('#performanceInfo').text('暂无性能数据，请启用性能监控功能。');
            }
        } else {
            $('#performanceInfo').text('刷新系统未初始化或性能监控未启用。');
        }
    };
});
</script>
{% endblock %}
