{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-server me-2"></i>系统监控</h2>
        <div>
            <button class="btn btn-primary" onclick="refreshSystemStatus()">
                <i class="fas fa-sync-alt me-1"></i>刷新
            </button>
            <button class="btn btn-info" onclick="toggleAutoRefresh()">
                <i class="fas fa-play me-1" id="autoRefreshIcon"></i>
                <span id="autoRefreshText">开启自动刷新</span>
            </button>
        </div>
    </div>

    <!-- 系统健康评分 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">系统健康评分</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="health-score-circle" id="overallScore">
                                <span class="score-text">--</span>
                            </div>
                            <p class="mt-2">总体评分</p>
                        </div>
                        <div class="col-md-3">
                            <div class="progress mb-2">
                                <div class="progress-bar bg-info" id="cpuScoreBar" style="width: 0%"></div>
                            </div>
                            <p>CPU 评分: <span id="cpuScore">--</span></p>
                        </div>
                        <div class="col-md-3">
                            <div class="progress mb-2">
                                <div class="progress-bar bg-warning" id="memoryScoreBar" style="width: 0%"></div>
                            </div>
                            <p>内存评分: <span id="memoryScore">--</span></p>
                        </div>
                        <div class="col-md-3">
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" id="diskScoreBar" style="width: 0%"></div>
                            </div>
                            <p>磁盘评分: <span id="diskScore">--</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 实时状态卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white system-status-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">CPU 使用率</h6>
                            <h3 id="cpuUsage">--</h3>
                            <small id="cpuDetails">&nbsp;</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-microchip fa-2x"></i>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 5px;">
                        <div class="progress-bar bg-white" id="cpuProgress" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white system-status-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">内存使用</h6>
                            <h3 id="memoryUsage">--</h3>
                            <small id="memoryDetails">-- / --</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-memory fa-2x"></i>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 5px;">
                        <div class="progress-bar bg-white" id="memoryProgress" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white system-status-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">磁盘使用</h6>
                            <h3 id="diskUsage">--</h3>
                            <small id="diskDetails">-- / --</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hdd fa-2x"></i>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 5px;">
                        <div class="progress-bar bg-white" id="diskProgress" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white system-status-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">网络流量</h6>
                            <h3 id="networkUsage">网络</h3>
                            <small id="networkDetails">上传: -- / 下载: --</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-network-wired fa-2x"></i>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 5px;">
                        <div class="progress-bar bg-white" id="networkProgress" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细信息 -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>操作系统:</strong></td>
                            <td id="platform">--</td>
                        </tr>
                        <tr>
                            <td><strong>主机名:</strong></td>
                            <td id="hostname">--</td>
                        </tr>
                        <tr>
                            <td><strong>进程数:</strong></td>
                            <td id="processes">--</td>
                        </tr>
                        <tr>
                            <td><strong>服务器状态:</strong></td>
                            <td><span class="badge bg-success" id="serverStatus">在线</span></td>
                        </tr>
                        <tr>
                            <td><strong>数据库状态:</strong></td>
                            <td><span class="badge bg-success" id="dbStatus">正常</span></td>
                        </tr>
                        <tr>
                            <td><strong>最后更新:</strong></td>
                            <td id="lastUpdate">--</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>实时图表</h5>
                </div>
                <div class="card-body">
                    <canvas id="systemChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细系统信息按钮 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#detailedInfo" aria-expanded="false">
                            <i class="fas fa-chevron-down me-2"></i>详细系统信息
                        </button>
                    </h5>
                </div>
                <div class="collapse" id="detailedInfo">
                    <div class="card-body">
                        <pre id="detailedSystemInfo" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">
                            点击刷新按钮获取详细信息...
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.health-score-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    background: conic-gradient(#28a745 0deg, #28a745 0deg, #e9ecef 0deg, #e9ecef 360deg);
}

.score-text {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

/* 确保系统监控卡片高度一致 */
.system-status-card {
    height: 150px !important;
}

.system-status-card .card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1.25rem;
}

.system-status-card .d-flex {
    flex: 1;
    align-items: flex-start;
}

.system-status-card .progress {
    margin-top: auto;
}

.system-status-card h3 {
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.system-status-card small {
    display: block;
    min-height: 1.2em;
    line-height: 1.2;
}

.progress {
    background-color: rgba(255, 255, 255, 0.3);
}

.table td {
    border: none;
    padding: 0.5rem 0;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let autoRefreshInterval = null;
let systemChart = null;
let chartData = {
    labels: [],
    cpu: [],
    memory: [],
    disk: []
};

// 初始化图表
function initChart() {
    const ctx = document.getElementById('systemChart').getContext('2d');
    systemChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'CPU %',
                data: chartData.cpu,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }, {
                label: '内存 %',
                data: chartData.memory,
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                tension: 0.4
            }, {
                label: '磁盘 %',
                data: chartData.disk,
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// 更新健康评分圆环
function updateHealthScore(score, color) {
    const circle = document.getElementById('overallScore');
    const percentage = (score / 100) * 360;
    circle.style.background = `conic-gradient(${color} 0deg, ${color} ${percentage}deg, #e9ecef ${percentage}deg, #e9ecef 360deg)`;
    circle.querySelector('.score-text').textContent = Math.round(score);
}

// 刷新系统状态
function refreshSystemStatus() {
    fetch('/admin_panel/api/system_status')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // 安全地更新元素内容
            function safeUpdateElement(id, value, defaultValue = '--') {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value || defaultValue;
                }
            }

            // 更新基本状态
            safeUpdateElement('cpuUsage', data.cpu_usage);
            safeUpdateElement('memoryUsage', data.memory_usage);
            safeUpdateElement('memoryDetails', `${data.memory_used || '--'} / ${data.memory_total || '--'}`);
            safeUpdateElement('diskUsage', data.disk_usage);
            safeUpdateElement('diskDetails', `${data.disk_used || '--'} / ${data.disk_total || '--'}`);
            safeUpdateElement('networkUsage', '网络');
            safeUpdateElement('networkDetails', `上传: ${data.network_sent || '--'} / 下载: ${data.network_recv || '--'}`);

            // 更新系统信息
            safeUpdateElement('platform', data.platform);
            safeUpdateElement('hostname', data.hostname);
            safeUpdateElement('processes', data.processes);
            safeUpdateElement('lastUpdate', data.timestamp);

            // 更新服务器状态
            safeUpdateElement('serverStatus', data.server_status || '在线');
            safeUpdateElement('dbStatus', data.db_status || '正常');

            // 更新进度条
            const cpuPercent = parseFloat(data.cpu_usage) || 0;
            const memoryPercent = parseFloat(data.memory_usage) || 0;
            const diskPercent = parseFloat(data.disk_usage) || 0;

            function safeUpdateProgress(id, percent) {
                const element = document.getElementById(id);
                if (element) {
                    element.style.width = percent + '%';
                }
            }

            safeUpdateProgress('cpuProgress', cpuPercent);
            safeUpdateProgress('memoryProgress', memoryPercent);
            safeUpdateProgress('diskProgress', diskPercent);

            // 更新健康评分
            if (data.health_score) {
                updateHealthScore(data.health_score.overall_score, getHealthColor(data.health_score.color));
                safeUpdateElement('cpuScore', Math.round(data.health_score.cpu_score));
                safeUpdateElement('memoryScore', Math.round(data.health_score.memory_score));
                safeUpdateElement('diskScore', Math.round(data.health_score.disk_score));

                safeUpdateProgress('cpuScoreBar', data.health_score.cpu_score);
                safeUpdateProgress('memoryScoreBar', data.health_score.memory_score);
                safeUpdateProgress('diskScoreBar', data.health_score.disk_score);
            }

            // 更新图表数据
            updateChart(cpuPercent, memoryPercent, diskPercent);
        })
        .catch(error => {
            console.error('获取系统状态失败:', error);

            // 显示错误状态
            const errorElements = [
                'cpuUsage', 'memoryUsage', 'diskUsage', 'platform',
                'hostname', 'processes', 'lastUpdate'
            ];

            errorElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = '错误';
                }
            });

            // 重置进度条
            ['cpuProgress', 'memoryProgress', 'diskProgress'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.width = '0%';
                }
            });
        });
}

// 获取健康状态颜色
function getHealthColor(status) {
    const colors = {
        'success': '#28a745',
        'info': '#17a2b8',
        'warning': '#ffc107',
        'danger': '#dc3545'
    };
    return colors[status] || '#6c757d';
}

// 更新图表
function updateChart(cpu, memory, disk) {
    const now = new Date().toLocaleTimeString();
    
    // 保持最近20个数据点
    if (chartData.labels.length >= 20) {
        chartData.labels.shift();
        chartData.cpu.shift();
        chartData.memory.shift();
        chartData.disk.shift();
    }
    
    chartData.labels.push(now);
    chartData.cpu.push(cpu);
    chartData.memory.push(memory);
    chartData.disk.push(disk);
    
    if (systemChart) {
        systemChart.update();
    }
}

// 切换自动刷新
function toggleAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        document.getElementById('autoRefreshIcon').className = 'fas fa-play me-1';
        document.getElementById('autoRefreshText').textContent = '开启自动刷新';
    } else {
        autoRefreshInterval = setInterval(refreshSystemStatus, 5000); // 每5秒刷新
        document.getElementById('autoRefreshIcon').className = 'fas fa-pause me-1';
        document.getElementById('autoRefreshText').textContent = '关闭自动刷新';
    }
}

// 获取详细系统信息
function getDetailedInfo() {
    fetch('/admin_panel/api/system_details')
        .then(response => response.json())
        .then(data => {
            document.getElementById('detailedSystemInfo').textContent = JSON.stringify(data, null, 2);
        })
        .catch(error => {
            document.getElementById('detailedSystemInfo').textContent = '获取详细信息失败: ' + error;
        });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initChart();
    refreshSystemStatus();
    
    // 监听详细信息展开事件
    document.getElementById('detailedInfo').addEventListener('shown.bs.collapse', function() {
        getDetailedInfo();
    });
});
</script>
{% endblock %}
