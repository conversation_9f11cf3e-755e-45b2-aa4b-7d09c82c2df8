#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用包初始化文件
"""

from flask import Flask
from flask_login import LoginManager
from flask_mail import Mail
from flask_migrate import Migrate
from flask_moment import Moment
from flask_admin import Admin
from flask_wtf.csrf import CSRFProtect
from flask_cors import CORS

from config import Config
import os

# 导入db实例
from app.models import db

# 创建其他扩展实例
login_manager = LoginManager()
mail = Mail()
migrate = Migrate()
moment = Moment()
admin = Admin()
csrf = CSRFProtect()
cors = CORS()


def create_app(config_class=Config):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面。'
    login_manager.login_message_category = 'info'

    # 注册用户加载回调
    @login_manager.user_loader
    def load_user(user_id):
        from app.models import User
        return User.query.get(int(user_id))

    # 添加请求前处理器，更新用户最后访问时间
    @app.before_request
    def before_request():
        from flask_login import current_user
        from datetime import datetime, timedelta
        if current_user.is_authenticated:
            # 只有当距离上次更新超过1分钟时才更新，减少数据库写入
            now = datetime.now()
            if (current_user.last_seen is None or
                now - current_user.last_seen > timedelta(minutes=1)):
                current_user.last_seen = now
                try:
                    db.session.commit()
                except:
                    db.session.rollback()

    mail.init_app(app)
    migrate.init_app(app, db)
    moment.init_app(app)
    admin.init_app(app)
    csrf.init_app(app)

    # 配置CORS支持移动端访问
    cors.init_app(app, resources={
        r"/api/v1/*": {
            "origins": ["*"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })


    # 配置CSRF豁免的API端点

    # 浏览量增加API豁免CSRF（自动触发，无需用户交互）
    csrf.exempt('api.increment_product_view')

    # 订单操作API也需要豁免CSRF（因为使用自定义头部）
    csrf.exempt('orders.cancel_order')
    csrf.exempt('orders.pay_order')
    csrf.exempt('orders.ship_order')
    csrf.exempt('orders.confirm_order')

    # 系统设置API豁免CSRF
    csrf.exempt('admin_panel.api_clear_cache')
    csrf.exempt('admin_panel.api_backup_database')
    csrf.exempt('admin_panel.api_test_email')

    # 消息相关API豁免CSRF
    csrf.exempt('messages.api_delete_conversation')
    csrf.exempt('messages.send_message')
    csrf.exempt('messages.get_messages')
    csrf.exempt('messages.api_conversations')

    # 邮箱验证码API豁免CSRF
    csrf.exempt('auth.send_verification_code')
    csrf.exempt('auth.send_change_password_code')

    # 添加自定义过滤器
    @app.template_filter('nl2br')
    def nl2br_filter(text):
        """将换行符转换为HTML的<br>标签"""
        if text is None:
            return ''
        return text.replace('\n', '<br>\n')

    @app.template_filter('local_datetime')
    def local_datetime_filter(dt, format_str='%Y-%m-%d %H:%M:%S'):
        """格式化本地时间"""
        if dt is None:
            return ''
        return dt.strftime(format_str)

    @app.template_filter('relative_time')
    def relative_time_filter(dt):
        """相对时间格式化"""
        if dt is None:
            return ''

        from datetime import datetime, timedelta
        now = datetime.now()
        diff = now - dt

        if diff.total_seconds() < 60:
            return '刚刚'
        elif diff.total_seconds() < 3600:
            minutes = int(diff.total_seconds() / 60)
            return f'{minutes}分钟前'
        elif diff.total_seconds() < 86400:
            hours = int(diff.total_seconds() / 3600)
            return f'{hours}小时前'
        elif diff.days < 7:
            return f'{diff.days}天前'
        else:
            return dt.strftime('%Y-%m-%d')

    # 注册蓝图
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.products import bp as products_bp
    app.register_blueprint(products_bp, url_prefix='/products')

    from app.orders import bp as orders_bp
    app.register_blueprint(orders_bp, url_prefix='/orders')

    from app.admin_panel import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin_panel')

    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    from app.notifications import bp as notifications_bp
    app.register_blueprint(notifications_bp, url_prefix='/notifications')

    from app.messages import bp as messages_bp
    app.register_blueprint(messages_bp, url_prefix='/messages')

    from app.analytics import bp as analytics_bp
    app.register_blueprint(analytics_bp, url_prefix='/analytics')

    # 注册移动端API v1蓝图（禁用CSRF保护）
    from app.api_v1 import bp as api_v1_bp
    app.register_blueprint(api_v1_bp)

    # 为API v1路由禁用CSRF保护
    csrf.exempt(api_v1_bp)

    # 添加应用启动时的清理任务
    @app.before_first_request
    def cleanup_expired_codes():
        """应用启动时清理过期验证码"""
        try:
            from app.models import EmailVerificationCode
            cleaned_count = EmailVerificationCode.cleanup_expired_codes()
            if cleaned_count > 0:
                app.logger.info(f'应用启动时清理了 {cleaned_count} 个过期验证码')
        except Exception as e:
            app.logger.error(f'清理过期验证码失败: {str(e)}')

    return app
