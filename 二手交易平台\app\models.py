#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义
"""

from datetime import datetime, timedelta
from flask import current_app
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import enum
import random
import string

# 导入本地时间工具
from app.utils.datetime_utils import local_now

# 创建一个全局的db实例
from flask_sqlalchemy import SQLAlchemy
db = SQLAlchemy()

class UserRole(enum.Enum):
    """用户角色枚举"""
    USER = 'user'
    ADMIN = 'admin'

class ProductStatus(enum.Enum):
    """商品状态枚举"""
    PENDING = 'pending'  # 待审核
    ACTIVE = 'active'    # 在售
    SOLD = 'sold'        # 已售
    INACTIVE = 'inactive' # 下架

class OrderStatus(enum.Enum):
    """订单状态枚举"""
    PENDING = 'pending'      # 待付款
    PAID = 'paid'           # 已付款
    SHIPPED = 'shipped'     # 已发货
    DELIVERED = 'delivered' # 已送达
    COMPLETED = 'completed' # 已完成
    CANCELLED = 'cancelled' # 已取消

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    phone = db.Column(db.String(20), unique=True, nullable=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # 个人信息
    nickname = db.Column(db.String(80), nullable=True)
    avatar = db.Column(db.String(200), nullable=True)
    bio = db.Column(db.Text, nullable=True)
    
    # 用户状态
    role = db.Column(db.Enum(UserRole), default=UserRole.USER, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    email_verified = db.Column(db.Boolean, default=False, nullable=False)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False)
    updated_at = db.Column(db.DateTime, default=local_now, onupdate=local_now)
    last_seen = db.Column(db.DateTime, default=local_now)
    
    # 关系
    products = db.relationship('Product', backref='seller', lazy='dynamic', cascade='all, delete-orphan')
    orders_as_buyer = db.relationship('Order', foreign_keys='Order.buyer_id', backref='buyer', lazy='dynamic')
    orders_as_seller = db.relationship('Order', foreign_keys='Order.seller_id', backref='seller', lazy='dynamic')
    reviews_given = db.relationship('Review', foreign_keys='Review.reviewer_id', backref='reviewer', lazy='dynamic')
    reviews_received = db.relationship('Review', foreign_keys='Review.reviewee_id', backref='reviewee', lazy='dynamic')
    messages_sent = db.relationship('Message', foreign_keys='Message.sender_id', backref='sender', lazy='dynamic')
    messages_received = db.relationship('Message', foreign_keys='Message.receiver_id', backref='receiver', lazy='dynamic')
    favorites = db.relationship('Favorite', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """检查是否为管理员"""
        return self.role == UserRole.ADMIN
    
    def get_avatar_url(self):
        """获取头像URL"""
        if self.avatar:
            return f'/static/uploads/avatars/{self.avatar}'
        # 生成基于用户名首字符的默认头像
        return f'/avatar/{self.id}'

    def avatar_url(self):
        """获取头像URL（API兼容方法）"""
        return self.get_avatar_url()

    def get_avatar_char(self):
        """获取头像显示字符"""
        import re

        # 优先使用昵称
        if self.nickname and self.nickname.strip():
            char = self.nickname.strip()[0]
        elif self.username and self.username.strip():
            char = self.username.strip()[0]
        else:
            return 'U'

        # 如果是中文字符，直接返回
        if re.match(r'[\u4e00-\u9fff]', char):
            return char
        # 如果是英文字符，转为大写
        elif char.isalpha():
            return char.upper()
        # 如果是数字或其他字符，也返回
        else:
            return char

    def get_avatar_color(self):
        """根据用户ID生成头像背景色"""
        colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
            '#A3E4D7', '#F9E79F', '#FADBD8', '#D5DBDB', '#AED6F1'
        ]
        return colors[self.id % len(colors)]
    
    def __repr__(self):
        return f'<User {self.username}>'

# 用户加载回调函数将在app/__init__.py中注册

class Category(db.Model):
    """商品分类模型"""
    __tablename__ = 'categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    icon = db.Column(db.String(100), nullable=True)
    color = db.Column(db.String(20), nullable=True)  # 添加颜色字段
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=local_now, nullable=False)
    
    # 关系
    products = db.relationship('Product', backref='category', lazy='dynamic')

    def get_icon(self):
        """获取分类图标"""
        # 如果数据库中有图标，优先使用
        if self.icon:
            return self.icon

        # 否则根据分类名称映射
        icon_mapping = {
            '电子产品': 'fas fa-laptop',
            '手机数码': 'fas fa-mobile-alt',
            '电脑办公': 'fas fa-desktop',
            '家用电器': 'fas fa-tv',
            '服装配饰': 'fas fa-tshirt',
            '男装': 'fas fa-male',
            '女装': 'fas fa-female',
            '鞋靴': 'fas fa-shoe-prints',
            '箱包': 'fas fa-suitcase',
            '家居用品': 'fas fa-home',
            '家具': 'fas fa-couch',
            '厨具': 'fas fa-utensils',
            '装饰': 'fas fa-palette',
            '图书文具': 'fas fa-book',
            '图书': 'fas fa-book-open',
            '文具': 'fas fa-pen',
            '教育': 'fas fa-graduation-cap',
            '运动户外': 'fas fa-running',
            '健身': 'fas fa-dumbbell',
            '户外': 'fas fa-mountain',
            '体育': 'fas fa-football-ball',
            '美妆护肤': 'fas fa-spa',
            '化妆品': 'fas fa-lipstick',
            '护肤': 'fas fa-hand-sparkles',
            '食品饮料': 'fas fa-utensils',
            '零食': 'fas fa-cookie-bite',
            '饮料': 'fas fa-glass-whiskey',
            '母婴用品': 'fas fa-baby',
            '玩具': 'fas fa-gamepad',
            '汽车用品': 'fas fa-car',
            '其他': 'fas fa-box'
        }
        return icon_mapping.get(self.name, 'fas fa-box')

    def get_color(self):
        """根据分类ID生成颜色"""
        colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
            '#A3E4D7', '#F9E79F', '#FADBD8', '#D5DBDB', '#AED6F1',
            '#F5B7B1', '#A9DFBF', '#F9E79F', '#D2B4DE', '#AED6F1'
        ]
        return colors[self.id % len(colors)]

    def __repr__(self):
        return f'<Category {self.name}>'

class Product(db.Model):
    """商品模型"""
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False, index=True)
    description = db.Column(db.Text, nullable=False)
    price = db.Column(db.Numeric(10, 2), nullable=False, index=True)
    original_price = db.Column(db.Numeric(10, 2), nullable=True)
    
    # 商品状态
    status = db.Column(db.Enum(ProductStatus), default=ProductStatus.PENDING, nullable=False, index=True)
    condition = db.Column(db.String(50), nullable=True)  # 新旧程度
    location = db.Column(db.String(200), nullable=True)  # 所在地
    quantity = db.Column(db.Integer, default=1, nullable=False)  # 商品数量
    sold_quantity = db.Column(db.Integer, default=0, nullable=False)  # 已售数量
    
    # 外键
    seller_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=False, index=True)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False, index=True)
    updated_at = db.Column(db.DateTime, default=local_now, onupdate=local_now)
    
    # 统计信息
    view_count = db.Column(db.Integer, default=0, nullable=False)
    favorite_count = db.Column(db.Integer, default=0, nullable=False)

    # 管理员标记
    is_featured = db.Column(db.Boolean, default=False, nullable=False)  # 重点观察
    
    # 关系
    images = db.relationship('ProductImage', backref='product', lazy='dynamic', cascade='all, delete-orphan')
    orders = db.relationship('Order', backref='product', lazy='dynamic')
    favorites = db.relationship('Favorite', backref='product', lazy='dynamic', cascade='all, delete-orphan')
    
    def get_main_image(self):
        """获取主图片"""
        image = self.images.filter_by(is_main=True).first()
        if image:
            return image.filename
        first_image = self.images.first()
        return first_image.filename if first_image else 'default-product.png'

    def get_main_image_url(self):
        """获取主图片URL"""
        filename = self.get_main_image()
        if filename == 'default-product.png':
            # 返回基于分类的动态默认图片
            if self.category_id:
                return f'/product_default_image/{self.category_id}'
            else:
                return '/static/images/default-product.png'
        return f'/static/uploads/products/{filename}'

    def get_all_images(self):
        """获取所有图片文件名列表"""
        images = self.images.order_by('sort_order').all()
        if images:
            return [img.filename for img in images]
        return []

    def is_sold_out(self):
        """检查是否已售完"""
        return self.sold_quantity >= self.quantity

    def get_remaining_quantity(self):
        """获取剩余数量"""
        return max(0, self.quantity - self.sold_quantity)

    def get_status_display(self):
        """获取状态显示文本"""
        if self.is_sold_out():
            return '已售出'
        elif self.status == ProductStatus.ACTIVE:
            return '在售'
        elif self.status == ProductStatus.INACTIVE:
            return '已下架'
        else:
            return '待审核'

    def __repr__(self):
        return f'<Product {self.title}>'

class ProductImage(db.Model):
    """商品图片模型"""
    __tablename__ = 'product_images'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(200), nullable=False)
    is_main = db.Column(db.Boolean, default=False, nullable=False)
    sort_order = db.Column(db.Integer, default=0, nullable=False)

    # 外键
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f'<ProductImage {self.filename}>'

class Order(db.Model):
    """订单模型"""
    __tablename__ = 'orders'

    id = db.Column(db.Integer, primary_key=True)
    order_no = db.Column(db.String(50), unique=True, nullable=False, index=True)

    # 外键
    buyer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    seller_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False, index=True)

    # 订单信息
    quantity = db.Column(db.Integer, default=1, nullable=False)
    price = db.Column(db.Numeric(10, 2), nullable=False)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)

    # 订单状态
    status = db.Column(db.Enum(OrderStatus), default=OrderStatus.PENDING, nullable=False, index=True)

    # 收货信息
    shipping_name = db.Column(db.String(100), nullable=True)
    shipping_phone = db.Column(db.String(20), nullable=True)
    shipping_address = db.Column(db.Text, nullable=True)

    # 备注
    buyer_note = db.Column(db.Text, nullable=True)
    seller_note = db.Column(db.Text, nullable=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False, index=True)
    updated_at = db.Column(db.DateTime, default=local_now, onupdate=local_now)
    paid_at = db.Column(db.DateTime, nullable=True)
    shipped_at = db.Column(db.DateTime, nullable=True)
    delivered_at = db.Column(db.DateTime, nullable=True)
    completed_at = db.Column(db.DateTime, nullable=True)

    # 关系
    review = db.relationship('Review', backref='order', uselist=False, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Order {self.order_no}>'

class Review(db.Model):
    """评价模型"""
    __tablename__ = 'reviews'

    id = db.Column(db.Integer, primary_key=True)

    # 外键
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, unique=True)
    reviewer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    reviewee_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)

    # 评价内容
    rating = db.Column(db.Integer, nullable=False)  # 1-5星
    content = db.Column(db.Text, nullable=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f'<Review {self.rating} stars>'

class Message(db.Model):
    """消息模型"""
    __tablename__ = 'messages'

    id = db.Column(db.Integer, primary_key=True)

    # 外键
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    receiver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=True, index=True)

    # 消息内容
    content = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False, nullable=False)

    # 逻辑删除字段 - 记录每个用户的删除状态
    deleted_by_sender = db.Column(db.Boolean, default=False, nullable=False)
    deleted_by_receiver = db.Column(db.Boolean, default=False, nullable=False)
    sender_deleted_at = db.Column(db.DateTime, nullable=True)
    receiver_deleted_at = db.Column(db.DateTime, nullable=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False, index=True)

    # 关系
    product = db.relationship('Product', backref='messages')

    def is_deleted_by_user(self, user_id):
        """检查消息是否被指定用户删除"""
        if user_id == self.sender_id:
            return self.deleted_by_sender
        elif user_id == self.receiver_id:
            return self.deleted_by_receiver
        return False

    def delete_for_user(self, user_id):
        """为指定用户删除消息"""
        if user_id == self.sender_id:
            self.deleted_by_sender = True
            self.sender_deleted_at = local_now()
        elif user_id == self.receiver_id:
            self.deleted_by_receiver = True
            self.receiver_deleted_at = local_now()

    def get_deleted_at_for_user(self, user_id):
        """获取用户删除消息的时间"""
        if user_id == self.sender_id:
            return self.sender_deleted_at
        elif user_id == self.receiver_id:
            return self.receiver_deleted_at
        return None

    def __repr__(self):
        return f'<Message from {self.sender_id} to {self.receiver_id}>'

class Favorite(db.Model):
    """收藏模型"""
    __tablename__ = 'favorites'

    id = db.Column(db.Integer, primary_key=True)

    # 外键
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False, index=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # 唯一约束
    __table_args__ = (db.UniqueConstraint('user_id', 'product_id', name='unique_user_product_favorite'),)

    def __repr__(self):
        return f'<Favorite user:{self.user_id} product:{self.product_id}>'

class Notification(db.Model):
    """通知模型"""
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)

    # 外键
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)

    # 通知内容
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), nullable=False)  # order, system, message等
    is_read = db.Column(db.Boolean, default=False, nullable=False)

    # 相关对象ID（可选）
    related_id = db.Column(db.Integer, nullable=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False, index=True)

    # 关系
    user = db.relationship('User', backref='notifications')

    def get_icon(self):
        """获取通知图标"""
        icon_map = {
            'order': 'fa-shopping-cart',
            'system': 'fa-cog',
            'message': 'fa-comment',
            'review': 'fa-star',
            'payment': 'fa-credit-card',
            'shipping': 'fa-truck'
        }
        return icon_map.get(self.type, 'fa-bell')

    def get_color(self):
        """获取通知颜色"""
        color_map = {
            'order': 'primary',
            'system': 'info',
            'message': 'success',
            'review': 'warning',
            'payment': 'danger',
            'shipping': 'secondary'
        }
        return color_map.get(self.type, 'primary')

    def __repr__(self):
        return f'<Notification {self.title}>'


class ConversationHidden(db.Model):
    """隐藏对话模型 - 用于单方面隐藏对话"""
    __tablename__ = 'conversation_hidden'

    id = db.Column(db.Integer, primary_key=True)

    # 隐藏对话的用户
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)

    # 对话的另一方用户
    other_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)

    # 相关商品ID（可选）
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=True, index=True)

    # 隐藏时间
    hidden_at = db.Column(db.DateTime, default=local_now, nullable=False, index=True)

    # 关系
    user = db.relationship('User', foreign_keys=[user_id], backref='hidden_conversations')
    other_user = db.relationship('User', foreign_keys=[other_user_id])
    product = db.relationship('Product', backref='hidden_conversations')

    # 唯一约束：同一用户对同一对话只能有一条隐藏记录
    __table_args__ = (
        db.UniqueConstraint('user_id', 'other_user_id', 'product_id', name='unique_hidden_conversation'),
    )

    def __repr__(self):
        return f'<ConversationHidden user={self.user_id} other={self.other_user_id} product={self.product_id}>'

class Feedback(db.Model):
    """用户反馈模型"""
    __tablename__ = 'feedbacks'

    id = db.Column(db.Integer, primary_key=True)

    # 外键
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)

    # 反馈内容
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), nullable=False)  # bug, suggestion, complaint等
    status = db.Column(db.String(50), default='pending', nullable=False)  # pending, processing, resolved

    # 管理员回复
    admin_reply = db.Column(db.Text, nullable=True)
    admin_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    user = db.relationship('User', foreign_keys=[user_id], backref='feedbacks')
    admin = db.relationship('User', foreign_keys=[admin_id])

    def __repr__(self):
        return f'<Feedback {self.title}>'

class SystemConfig(db.Model):
    """系统配置模型"""
    __tablename__ = 'system_configs'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text, nullable=True)
    description = db.Column(db.String(500), nullable=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False)
    updated_at = db.Column(db.DateTime, default=local_now, onupdate=local_now)

    def __repr__(self):
        return f'<SystemConfig {self.key}>'


class Email(db.Model):
    """邮件模型"""
    __tablename__ = 'emails'

    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    html_content = db.Column(db.Text, nullable=True)

    # 邮件类型：system(系统邮件), user(用户邮件), admin(管理员邮件)
    email_type = db.Column(db.String(20), default='admin', nullable=False)

    # 邮件状态：draft(草稿), sent(已发送), failed(发送失败)
    status = db.Column(db.String(20), default='draft', nullable=False)

    # 发送时间
    sent_at = db.Column(db.DateTime, nullable=True)

    # 删除标记（暂时注释掉，等数据库字段添加后再启用）
    # deleted_by_sender = db.Column(db.Boolean, default=False, nullable=False)

    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False)
    updated_at = db.Column(db.DateTime, default=local_now, onupdate=local_now)

    # 关系
    sender = db.relationship('User', backref='sent_emails')
    recipients = db.relationship('EmailRecipient', backref='email', cascade='all, delete-orphan')
    replies = db.relationship('EmailReply', backref='original_email', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Email {self.subject}>'


class EmailRecipient(db.Model):
    """邮件收件人模型"""
    __tablename__ = 'email_recipients'

    id = db.Column(db.Integer, primary_key=True)
    email_id = db.Column(db.Integer, db.ForeignKey('emails.id'), nullable=False)
    recipient_email = db.Column(db.String(120), nullable=False)
    recipient_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # 是否已读
    is_read = db.Column(db.Boolean, default=False, nullable=False)
    read_at = db.Column(db.DateTime, nullable=True)

    # 发送状态：pending(待发送), sent(已发送), failed(发送失败)
    send_status = db.Column(db.String(20), default='pending', nullable=False)
    error_message = db.Column(db.Text, nullable=True)

    # 删除标记（暂时注释掉，等数据库字段添加后再启用）
    # deleted_by_recipient = db.Column(db.Boolean, default=False, nullable=False)

    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False)
    updated_at = db.Column(db.DateTime, default=local_now, onupdate=local_now)

    # 关系
    recipient_user = db.relationship('User', backref='received_emails')

    def __repr__(self):
        return f'<EmailRecipient {self.recipient_email}>'


class EmailReply(db.Model):
    """邮件回复模型"""
    __tablename__ = 'email_replies'

    id = db.Column(db.Integer, primary_key=True)
    original_email_id = db.Column(db.Integer, db.ForeignKey('emails.id'), nullable=False)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    html_content = db.Column(db.Text, nullable=True)

    # 发送状态：pending(待发送), sent(已发送), failed(发送失败)
    send_status = db.Column(db.String(20), default='pending', nullable=False)
    error_message = db.Column(db.Text, nullable=True)

    # 是否已读
    is_read = db.Column(db.Boolean, default=False, nullable=False)
    read_at = db.Column(db.DateTime, nullable=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=local_now, nullable=False)

    # 关系
    sender = db.relationship('User', backref='email_replies')

    def __repr__(self):
        return f'<EmailReply from {self.sender.username if self.sender else "Unknown"}>'

class SystemLog(db.Model):
    """系统日志模型"""
    __tablename__ = 'system_logs'

    id = db.Column(db.Integer, primary_key=True)
    level = db.Column(db.String(20), nullable=False, index=True)  # INFO, WARNING, ERROR
    message = db.Column(db.Text, nullable=False)
    module = db.Column(db.String(100), nullable=True)  # 模块名
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=local_now, nullable=False, index=True)

    def __repr__(self):
        return f'<SystemLog {self.level}: {self.message[:50]}>'


class EmailVerificationCode(db.Model):
    """邮箱验证码模型"""
    __tablename__ = 'email_verification_codes'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), nullable=False, index=True)
    code = db.Column(db.String(6), nullable=False)
    created_at = db.Column(db.DateTime, default=local_now, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    is_used = db.Column(db.Boolean, default=False, nullable=False)

    def __init__(self, email, expires_in_minutes=10):
        """初始化验证码"""
        self.email = email
        self.code = self.generate_code()
        self.created_at = local_now()
        self.expires_at = self.created_at + timedelta(minutes=expires_in_minutes)
        self.is_used = False

    @staticmethod
    def generate_code():
        """生成6位数字验证码"""
        return ''.join(random.choices(string.digits, k=6))

    def is_expired(self):
        """检查验证码是否过期"""
        return local_now() > self.expires_at

    def is_valid(self):
        """检查验证码是否有效（未过期且未使用）"""
        return not self.is_expired() and not self.is_used

    def mark_as_used(self):
        """标记验证码为已使用"""
        self.is_used = True
        db.session.commit()

    @classmethod
    def get_latest_valid_code(cls, email):
        """获取指定邮箱的最新有效验证码"""
        return cls.query.filter_by(
            email=email,
            is_used=False
        ).filter(
            cls.expires_at > local_now()
        ).order_by(cls.created_at.desc()).first()

    @classmethod
    def can_send_new_code(cls, email, cooldown_seconds=120):
        """检查是否可以发送新的验证码（冷却时间检查）"""
        latest_code = cls.query.filter_by(email=email).order_by(cls.created_at.desc()).first()
        if not latest_code:
            return True

        cooldown_time = latest_code.created_at + timedelta(seconds=cooldown_seconds)
        return local_now() > cooldown_time

    @classmethod
    def cleanup_expired_codes(cls):
        """清理过期的验证码"""
        expired_codes = cls.query.filter(cls.expires_at < local_now()).all()
        for code in expired_codes:
            db.session.delete(code)
        db.session.commit()
        return len(expired_codes)

    def __repr__(self):
        return f'<EmailVerificationCode {self.email}: {self.code}>'
