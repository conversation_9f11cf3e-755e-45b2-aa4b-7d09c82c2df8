{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-cogs me-2"></i>系统设置</h2>
        <div>
            <button class="btn btn-success" onclick="saveAllSettings()">
                <i class="fas fa-save me-1"></i>保存所有设置
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- 基本设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">基本设置</h5>
                </div>
                <div class="card-body">
                    <form id="basicSettingsForm">
                        <div class="mb-3">
                            <label class="form-label">网站名称</label>
                            <input type="text" class="form-control" name="site_name" 
                                   value="{{ config.get('SITE_NAME', '二手交易平台') }}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">网站描述</label>
                            <textarea class="form-control" name="site_description" rows="3">{{ config.get('SITE_DESCRIPTION', '安全可靠的二手商品交易平台') }}</textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">每页显示商品数</label>
                                <input type="number" class="form-control" name="products_per_page" 
                                       value="{{ config.get('PRODUCTS_PER_PAGE', 12) }}" min="6" max="50">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">每页显示订单数</label>
                                <input type="number" class="form-control" name="orders_per_page" 
                                       value="{{ config.get('ORDERS_PER_PAGE', 10) }}" min="5" max="50">
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 文件上传设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">文件上传设置</h5>
                </div>
                <div class="card-body">
                    <form id="uploadSettingsForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">最大文件大小 (MB)</label>
                                <input type="number" class="form-control" name="max_file_size" 
                                       value="{{ (config.get('MAX_CONTENT_LENGTH', 16777216) / 1024 / 1024)|int }}" 
                                       min="1" max="100">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">允许的文件类型</label>
                                <input type="text" class="form-control" name="allowed_extensions" 
                                       value="{{ config.get('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif'])|join(', ') }}"
                                       placeholder="jpg, jpeg, png, gif">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">上传目录</label>
                            <input type="text" class="form-control" name="upload_folder" 
                                   value="{{ config.get('UPLOAD_FOLDER', 'app/static/uploads') }}" readonly>
                            <small class="text-muted">上传文件存储目录（只读）</small>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 邮件设置 -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">邮件设置</h5>
                    <div id="mailConfigStatus">
                        {% if config.get('MAIL_USERNAME') and config.get('MAIL_PASSWORD') %}
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>已配置
                        </span>
                        {% else %}
                        <span class="badge bg-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>未配置
                        </span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <form id="emailSettingsForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">SMTP服务器</label>
                                <input type="text" class="form-control" name="mail_server" 
                                       value="{{ config.get('MAIL_SERVER', 'smtp.qq.com') }}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">SMTP端口</label>
                                <input type="number" class="form-control" name="mail_port" 
                                       value="{{ config.get('MAIL_PORT', 587) }}">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">发件人邮箱</label>
                            <input type="email" class="form-control" name="mail_username" 
                                   value="{{ config.get('MAIL_USERNAME', '') }}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱密码</label>
                            <input type="password" class="form-control" name="mail_password" 
                                   placeholder="如不修改请留空">
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="mail_use_tls"
                                   {{ 'checked' if config.get('MAIL_USE_TLS', True) else '' }}>
                            <label class="form-check-label">使用TLS加密</label>
                        </div>

                        {% if not config.get('MAIL_USERNAME') or not config.get('MAIL_PASSWORD') %}
                        <div class="alert alert-warning mt-3" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>邮件服务未配置</strong><br>
                            请配置邮件参数以启用邮件功能（密码重置、通知等）。<br>
                            <small>
                                <a href="#" onclick="window.open('QQ邮箱配置指南.md', '_blank')" class="text-decoration-none">
                                    📖 查看QQ邮箱配置指南
                                </a>
                            </small>
                        </div>
                        {% endif %}

                        <div class="mt-3">
                            <button type="button" class="btn btn-primary" onclick="saveEmailSettings()">
                                <i class="fas fa-save me-1"></i>保存邮件配置
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="testEmailSettings()">
                                <i class="fas fa-paper-plane me-1"></i>测试邮件发送
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- 系统信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">系统信息</h5>
                </div>
                <div class="card-body">
                    <p><strong>Python版本:</strong> {{ python_version }}</p>
                    <p><strong>Flask版本:</strong> {{ flask_version }}</p>
                    <p><strong>数据库:</strong> {{ db_info }}</p>
                    <p><strong>运行时间:</strong> <span id="uptime">{{ uptime }}</span></p>
                    <p><strong>当前时间:</strong> <span id="current-time">{{ current_time }}</span></p>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">快速操作</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-warning" onclick="clearCache()">
                            <i class="fas fa-broom me-1"></i>清理缓存
                        </button>
                        <button class="btn btn-info" onclick="backupDatabase()">
                            <i class="fas fa-database me-1"></i>备份数据库
                        </button>
                        <button class="btn btn-success" onclick="testEmail()">
                            <i class="fas fa-envelope me-1"></i>测试邮件
                        </button>
                        <button class="btn btn-secondary" onclick="viewLogs()">
                            <i class="fas fa-file-alt me-1"></i>查看日志
                        </button>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">系统状态</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h4 mb-0 text-success">{{ online_users }}</div>
                            <small class="text-muted">在线用户</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 mb-0 text-primary">{{ total_users }}</div>
                            <small class="text-muted">总用户数</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-warning">{{ total_products }}</div>
                            <small class="text-muted">总商品数</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-info">{{ total_orders }}</div>
                            <small class="text-muted">总订单数</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
function saveAllSettings() {
    const basicData = new FormData(document.getElementById('basicSettingsForm'));
    const uploadData = new FormData(document.getElementById('uploadSettingsForm'));
    const emailData = new FormData(document.getElementById('emailSettingsForm'));

    // 合并所有表单数据
    const allData = new FormData();
    for (let [key, value] of basicData) allData.append(key, value);
    for (let [key, value] of uploadData) allData.append(key, value);
    for (let [key, value] of emailData) allData.append(key, value);

    // 添加CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]');
    if (csrfToken) {
        allData.append('csrf_token', csrfToken.getAttribute('content'));
    }

    // 显示加载状态
    const saveBtn = document.querySelector('button[onclick="saveAllSettings()"]');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
    saveBtn.disabled = true;

    fetch('/admin_panel/system_config', {
        method: 'POST',
        body: allData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            alert('设置保存成功！');
            // 可选：刷新页面以显示最新配置
            // window.location.reload();
        } else {
            alert('保存失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('保存失败：' + error.message + '，请重试');
    })
    .finally(() => {
        // 恢复按钮状态
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

function clearCache() {
    showConfirm('确定要清理缓存吗？', function() {
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        fetch('/admin_panel/api/clear_cache', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showInfo(data.message);
            } else {
                showInfo('清理失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showInfo('操作失败，请重试');
        });
    });
}

function backupDatabase() {
    showConfirm('确定要备份数据库吗？', function() {
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        fetch('/admin_panel/api/backup_database', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showInfo(data.message);
            } else {
                showInfo('备份失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showInfo('操作失败，请重试');
        });
    });
}

function testEmail() {
    const email = prompt('请输入测试邮箱地址：');
    if (email) {
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        fetch('/admin_panel/api/test_email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
            } else {
                showAlert('测试失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('操作失败，请重试', 'error');
        });
    }
}

function viewLogs() {
    window.open('/admin_panel/logs', '_blank');
}

// 保存邮件配置
function saveEmailSettings() {
    const form = document.getElementById('emailSettingsForm');
    const formData = new FormData(form);

    // 添加CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]').getAttribute('content');
    formData.append('csrf_token', csrfToken);

    // 处理checkbox
    const tlsCheckbox = form.querySelector('input[name="mail_use_tls"]');
    formData.set('mail_use_tls', tlsCheckbox.checked ? 'true' : 'false');

    fetch('/admin_panel/system_config', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('邮件配置保存成功！', 'success');
            // 更新配置状态显示
            updateMailConfigStatus();
        } else {
            showAlert('保存失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('保存失败，请重试', 'error');
    });
}

// 测试邮件设置
function testEmailSettings() {
    const email = prompt('请输入测试邮箱地址：');
    if (!email) return;

    if (!email.includes('@')) {
        alert('请输入有效的邮箱地址');
        return;
    }

    const testBtn = event.target;
    const originalText = testBtn.innerHTML;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>发送中...';
    testBtn.disabled = true;

    fetch('/admin_panel/test_email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('测试邮件发送成功！请检查收件箱', 'success');
        } else {
            showAlert('发送失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('发送失败，请重试', 'error');
    })
    .finally(() => {
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;
    });
}

// 更新邮件配置状态显示
function updateMailConfigStatus() {
    const statusElement = document.getElementById('mailConfigStatus');
    const mailUsername = document.querySelector('input[name="mail_username"]').value;
    const mailPassword = document.querySelector('input[name="mail_password"]').value;

    if (mailUsername && (mailPassword || statusElement.querySelector('.badge-success'))) {
        statusElement.innerHTML = '<span class="badge bg-success"><i class="fas fa-check me-1"></i>已配置</span>';
    } else {
        statusElement.innerHTML = '<span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i>未配置</span>';
    }
}

// 自定义确认对话框
function showConfirm(message, onConfirm, onCancel) {
    // 创建模态对话框HTML
    const confirmHtml = `
        <div class="modal fade" id="customConfirmModal" tabindex="-1" aria-hidden="true" style="z-index: 1060;">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="border-radius: 12px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div class="modal-header" style="border-bottom: 1px solid #e9ecef; padding: 20px 24px 16px;">
                        <h5 class="modal-title" style="color: #333; font-weight: 500;">192.168.31.52:5000 显示</h5>
                    </div>
                    <div class="modal-body" style="padding: 20px 24px; color: #333; font-size: 16px;">
                        ${message}
                    </div>
                    <div class="modal-footer" style="border-top: none; padding: 16px 24px 20px; justify-content: flex-end; gap: 12px;">
                        <button type="button" class="btn btn-secondary" id="confirmCancel" style="padding: 8px 20px; border-radius: 6px; font-weight: 500;">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmOk" style="padding: 8px 20px; border-radius: 6px; font-weight: 500; background-color: #007bff;">确定</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除现有的确认对话框
    const existingModal = document.getElementById('customConfirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', confirmHtml);

    // 获取模态框元素
    const modal = document.getElementById('customConfirmModal');
    const confirmBtn = document.getElementById('confirmOk');
    const cancelBtn = document.getElementById('confirmCancel');

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 绑定事件
    confirmBtn.onclick = function() {
        bsModal.hide();
        if (onConfirm) onConfirm();
    };

    cancelBtn.onclick = function() {
        bsModal.hide();
        if (onCancel) onCancel();
    };

    // 模态框隐藏后清理DOM
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

// 自定义信息提示对话框（类似confirm样式，但只有确定按钮）
function showInfo(message, onOk) {
    // 创建模态对话框HTML
    const infoHtml = `
        <div class="modal fade" id="customInfoModal" tabindex="-1" aria-hidden="true" style="z-index: 1060;">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="border-radius: 12px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div class="modal-header" style="border-bottom: 1px solid #e9ecef; padding: 20px 24px 16px;">
                        <h5 class="modal-title" style="color: #333; font-weight: 500;">192.168.31.52:5000 显示</h5>
                    </div>
                    <div class="modal-body" style="padding: 20px 24px; color: #333; font-size: 16px;">
                        ${message}
                    </div>
                    <div class="modal-footer" style="border-top: none; padding: 16px 24px 20px; justify-content: flex-end;">
                        <button type="button" class="btn btn-primary" id="infoOk" style="padding: 8px 20px; border-radius: 6px; font-weight: 500; background-color: #007bff;">确定</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除现有的信息对话框
    const existingModal = document.getElementById('customInfoModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', infoHtml);

    // 获取模态框元素
    const modal = document.getElementById('customInfoModal');
    const okBtn = document.getElementById('infoOk');

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 绑定事件
    okBtn.onclick = function() {
        bsModal.hide();
        if (onOk) onOk();
    };

    // 模态框隐藏后清理DOM
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

// 显示提示信息
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="margin-bottom: 20px; position: relative; z-index: 1050;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 优先在页面内容区域的最顶部显示提示
    const pageTransition = document.querySelector('.page-transition');
    const targetContainer = pageTransition || document.querySelector('.container-fluid') || document.querySelector('.container') || document.body;

    // 移除现有的提示（只移除我们创建的提示，不影响其他alert）
    const existingAlerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
    existingAlerts.forEach(alert => {
        if (alert.style.zIndex === '1050') {
            alert.remove();
        }
    });

    // 在目标容器最顶部插入新提示
    if (pageTransition) {
        // 如果有page-transition容器，在其第一个子元素之前插入
        const firstChild = pageTransition.firstElementChild;
        if (firstChild) {
            firstChild.insertAdjacentHTML('beforebegin', alertHtml);
        } else {
            pageTransition.insertAdjacentHTML('afterbegin', alertHtml);
        }
    } else {
        targetContainer.insertAdjacentHTML('afterbegin', alertHtml);
    }

    // 滚动到页面顶部以确保用户看到提示
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 3秒后自动消失
    setTimeout(() => {
        const alerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
        alerts.forEach(alert => {
            if (alert.style.zIndex === '1050') {
                alert.remove();
            }
        });
    }, 3000);
}

// 更新当前时间
setInterval(function() {
    const now = new Date();
    document.getElementById('current-time').textContent = now.toLocaleString();
}, 1000);
</script>
{% endblock %}
{% endblock %}
