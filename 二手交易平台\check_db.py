#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sqlite3

def check_database():
    """检查数据库表结构"""
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("数据库中的表:")
        for table in tables:
            print(f"- {table[0]}")
        
        # 检查是否存在email_verification_codes表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='email_verification_codes';")
        result = cursor.fetchone()
        
        if result:
            print("\n✓ email_verification_codes 表存在")
            # 查看表结构
            cursor.execute("PRAGMA table_info(email_verification_codes);")
            columns = cursor.fetchall()
            print("表结构:")
            for col in columns:
                print(f"  {col[1]} {col[2]}")
        else:
            print("\n✗ email_verification_codes 表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

if __name__ == '__main__':
    check_database()
