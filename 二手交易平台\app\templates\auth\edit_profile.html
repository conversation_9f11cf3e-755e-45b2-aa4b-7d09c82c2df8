{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-edit"></i> 编辑个人资料</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                                {% if form.username.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.username.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.nickname.label(class="form-label") }}
                                {{ form.nickname(class="form-control" + (" is-invalid" if form.nickname.errors else "")) }}
                                {% if form.nickname.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.nickname.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.phone.label(class="form-label") }}
                                {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">可选，用于接收重要通知</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.bio.label(class="form-label") }}
                        {{ form.bio(class="form-control" + (" is-invalid" if form.bio.errors else ""), rows="3") }}
                        {% if form.bio.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bio.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.avatar.label(class="form-label") }}
                        {{ form.avatar(class="form-control" + (" is-invalid" if form.avatar.errors else "")) }}
                        {% if form.avatar.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.avatar.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">支持 JPG、PNG、GIF 格式，文件大小不超过 2MB</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存更改
                        </button>
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 修改密码卡片 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-key"></i> 修改密码</h5>
            </div>
            <div class="card-body">
                <a href="{{ url_for('auth.change_password') }}" class="btn btn-warning">
                    <i class="fas fa-lock me-1"></i>修改密码
                </a>
                <p class="text-muted mt-2 mb-0">为了账户安全，建议定期修改密码</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- 当前头像预览 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">当前头像</h6>
            </div>
            <div class="card-body text-center">
                <img src="{{ current_user.get_avatar_url() }}" 
                     class="rounded-circle mb-3" 
                     width="120" height="120" alt="当前头像">
                <p class="text-muted">上传新头像后将替换当前头像</p>
            </div>
        </div>
        
        <!-- 账户信息 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">账户信息</h6>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-5"><strong>注册时间:</strong></div>
                    <div class="col-7">{{ current_user.created_at.strftime('%Y-%m-%d') if current_user.created_at else '未知' }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>最后登录:</strong></div>
                    <div class="col-7">{{ current_user.last_seen.strftime('%Y-%m-%d %H:%M') if current_user.last_seen else '从未' }}</div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>用户角色:</strong></div>
                    <div class="col-7">
                        {% if current_user.is_admin() %}
                        <span class="badge bg-danger">管理员</span>
                        {% else %}
                        <span class="badge bg-primary">普通用户</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5"><strong>账户状态:</strong></div>
                    <div class="col-7">
                        {% if current_user.is_active %}
                        <span class="badge bg-success">正常</span>
                        {% else %}
                        <span class="badge bg-danger">已禁用</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
