package com.second.hand.data.api

import com.second.hand.data.auth.TokenManager
import com.second.hand.data.preferences.PreferencesManager
import com.second.hand.utils.Constants
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.MediaType.Companion.toMediaType
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 认证拦截器
 * 自动添加Authorization头部，处理Token刷新
 */
@Singleton
class AuthInterceptor @Inject constructor(
    private val preferencesManager: PreferencesManager,
    private val tokenManager: TokenManager
) : Interceptor {

    // 并发控制 - 确保同时只有一个刷新请求
    private val refreshMutex = Mutex()
    private val isRefreshing = AtomicBoolean(false)
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // 检查是否为认证相关的请求
        if (isAuthRequest(originalRequest.url.encodedPath)) {
            return chain.proceed(originalRequest)
        }

        // 检查Token状态
        if (!tokenManager.isLoggedIn()) {
            // 没有有效Token，直接执行请求
            return chain.proceed(originalRequest)
        }

        // 检查是否需要刷新Token（主动刷新策略）
        if (tokenManager.shouldRefreshToken() && tokenManager.canRetryRefresh()) {
            println("🔐 Token即将过期，尝试主动刷新...")
            val refreshSuccess = runBlocking {
                refreshTokenWithLock("主动刷新")
            }
            if (!refreshSuccess) {
                println("🔐 主动Token刷新失败")
                // 不立即清除Token，等待401响应时再处理
            }
        }

        // 获取当前有效的访问令牌
        val accessToken = tokenManager.getAccessToken()
        if (accessToken.isNullOrEmpty()) {
            return chain.proceed(originalRequest)
        }

        // 添加Authorization头部
        val authenticatedRequest = originalRequest.newBuilder()
            .header(Constants.HEADER_AUTHORIZATION, "Bearer $accessToken")
            .build()

        val response = chain.proceed(authenticatedRequest)

        // 如果返回401未授权，尝试刷新token
        if (response.code == 401) {
            response.close()
            println("🔐 收到401响应，尝试刷新Token...")

            // 检查是否可以重试刷新
            if (!tokenManager.canRetryRefresh()) {
                println("🔐 已达到最大刷新重试次数，清除登录状态")
                tokenManager.clearTokens()
                return chain.proceed(originalRequest) // 返回原始请求，让上层处理401
            }

            // 尝试刷新token（带并发控制）
            val refreshSuccess = runBlocking {
                refreshTokenWithLock("401响应触发")
            }

            if (refreshSuccess) {
                // 刷新成功，重新发送请求
                val newAccessToken = tokenManager.getAccessToken()
                if (!newAccessToken.isNullOrEmpty()) {
                    val newRequest = originalRequest.newBuilder()
                        .header(Constants.HEADER_AUTHORIZATION, "Bearer $newAccessToken")
                        .build()
                    println("🔐 Token刷新成功，重新发送请求")
                    return chain.proceed(newRequest)
                }
            }

            // 刷新失败，清除token并返回401响应
            println("🔐 Token刷新失败，清除登录状态")
            tokenManager.clearTokens()
            return chain.proceed(originalRequest) // 让上层处理401
        }

        return response
    }
    
    /**
     * 检查是否为认证相关的请求
     */
    private fun isAuthRequest(path: String): Boolean {
        return path.contains("/auth/login") || 
               path.contains("/auth/register") || 
               path.contains("/auth/refresh")
    }
    
    /**
     * 带并发控制的Token刷新
     */
    private suspend fun refreshTokenWithLock(reason: String): Boolean {
        return refreshMutex.withLock {
            // 检查是否已经在刷新中
            if (isRefreshing.get()) {
                println("🔐 Token刷新已在进行中，等待结果...")
                return@withLock false
            }

            // 双重检查：可能在等待锁的过程中Token已经被刷新
            if (tokenManager.isTokenValid() && reason == "401响应触发") {
                println("🔐 Token已经有效，跳过刷新")
                return@withLock true
            }

            refreshTokenSync(reason)
        }
    }

    /**
     * 同步刷新token
     */
    private suspend fun refreshTokenSync(reason: String): Boolean {
        return try {
            isRefreshing.set(true)
            println("🔐 开始Token刷新 - 原因: $reason")

            val refreshToken = tokenManager.getRefreshToken()
            if (refreshToken.isNullOrEmpty()) {
                println("🔐 刷新Token不存在")
                return false
            }

            // 创建刷新Token的请求
            val refreshRequest = okhttp3.Request.Builder()
                .url("${getBaseUrl()}/api/v1/auth/refresh")
                .post(
                    okhttp3.RequestBody.create(
                        "application/json".toMediaType(),
                        """{"refreshToken":"$refreshToken"}"""
                    )
                )
                .build()

            // 创建新的OkHttpClient（不包含AuthInterceptor，避免循环）
            val client = okhttp3.OkHttpClient.Builder()
                .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                .build()

            val response = client.newCall(refreshRequest).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val jsonObject = org.json.JSONObject(responseBody)
                    if (jsonObject.getBoolean("success")) {
                        val data = jsonObject.getJSONObject("data")
                        val newAccessToken = data.getString("token")

                        // 保存新的Token（保持原有的refreshToken和用户信息）
                        val userInfo = tokenManager.getUserInfo()
                        tokenManager.saveTokens(newAccessToken, refreshToken, userInfo)

                        println("🔐 Token刷新成功 - 原因: $reason")
                        return true
                    } else {
                        val errorMsg = jsonObject.optJSONObject("error")?.optString("message") ?: "未知错误"
                        println("🔐 Token刷新失败 - 服务器错误: $errorMsg")
                    }
                } else {
                    println("🔐 Token刷新失败 - 响应体为空")
                }
            } else {
                println("🔐 Token刷新失败 - HTTP ${response.code}: ${response.message}")
            }

            false
        } catch (e: Exception) {
            println("🔐 Token刷新异常: ${e.message}")
            e.printStackTrace()
            false
        } finally {
            isRefreshing.set(false)
        }
    }

    /**
     * 获取基础URL
     */
    private fun getBaseUrl(): String {
        return "http://192.168.1.100:5000" // 从BuildConfig或其他配置获取
    }
}
