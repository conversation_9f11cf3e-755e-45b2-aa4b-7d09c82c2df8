#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JWT工具模块
"""

import jwt
from datetime import datetime, timedelta
from flask import current_app
from functools import wraps
from flask import request, jsonify
from app.models import User


def generate_tokens(user_id):
    """生成访问令牌和刷新令牌"""
    now = datetime.utcnow()
    
    # 访问令牌（1小时有效）
    access_payload = {
        'user_id': user_id,
        'exp': now + timedelta(hours=1),
        'iat': now,
        'type': 'access'
    }
    
    # 刷新令牌（30天有效）
    refresh_payload = {
        'user_id': user_id,
        'exp': now + timedelta(days=30),
        'iat': now,
        'type': 'refresh'
    }
    
    access_token = jwt.encode(
        access_payload,
        current_app.config['SECRET_KEY'],
        algorithm='HS256'
    )
    
    refresh_token = jwt.encode(
        refresh_payload,
        current_app.config['SECRET_KEY'],
        algorithm='HS256'
    )
    
    return access_token, refresh_token


def verify_token(token, token_type='access'):
    """验证令牌"""
    try:
        # 检查token是否在黑名单中
        from app.utils.token_blacklist import is_token_blacklisted
        if is_token_blacklisted(token):
            return None

        payload = jwt.decode(
            token,
            current_app.config['SECRET_KEY'],
            algorithms=['HS256']
        )

        if payload.get('type') != token_type:
            return None

        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None


def get_current_user_from_token():
    """从请求头中获取当前用户"""
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return None
    
    try:
        token = auth_header.split(' ')[1]  # Bearer <token>
        payload = verify_token(token)
        if not payload:
            return None
            
        user = User.query.get(payload['user_id'])
        return user
    except (IndexError, ValueError):
        return None


def jwt_required(f):
    """JWT认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user_from_token()
        if not user:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'AUTH_001',
                    'message': 'Token无效或已过期'
                },
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }), 401
        
        # 将用户对象添加到请求上下文
        request.current_user = user
        return f(*args, **kwargs)
    
    return decorated_function


def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user_from_token()
        if not user or not user.is_admin():
            return jsonify({
                'success': False,
                'error': {
                    'code': 'AUTH_004',
                    'message': '需要管理员权限'
                },
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }), 403
        
        request.current_user = user
        return f(*args, **kwargs)
    
    return decorated_function


def refresh_access_token(refresh_token):
    """使用刷新令牌生成新的访问令牌"""
    payload = verify_token(refresh_token, 'refresh')
    if not payload:
        return None
    
    user = User.query.get(payload['user_id'])
    if not user or not user.is_active:
        return None
    
    access_token, _ = generate_tokens(user.id)
    return access_token
