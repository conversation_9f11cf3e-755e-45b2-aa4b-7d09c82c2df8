#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的API测试套件
"""

import requests
import json
import time
import base64
from datetime import datetime


# API基础URL
BASE_URL = 'http://localhost:5000/api/v1'

# 全局变量存储测试数据
test_data = {
    'token': None,
    'refresh_token': None,
    'user_id': None,
    'product_id': None,
    'order_id': None,
    'category_id': None
}


def print_test_result(test_name, response):
    """打印测试结果"""
    print(f"\n{'='*60}")
    print(f"测试: {test_name}")
    print(f"状态码: {response.status_code}")
    print(f"响应时间: {response.elapsed.total_seconds():.3f}s")

    try:
        result = response.json()
        print(f"响应内容:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return result
    except:
        print(f"响应内容: {response.text}")
        return None


def get_auth_headers():
    """获取认证头"""
    return {
        'Authorization': f'Bearer {test_data["token"]}',
        'Content-Type': 'application/json'
    }


# ==================== 认证API测试 ====================

def test_auth_apis():
    """测试认证相关API"""
    print("\n🔐 开始测试认证API...")

    # 1. 发送邮箱验证码
    print("\n1. 测试发送邮箱验证码")
    url = f'{BASE_URL}/auth/send-email-code'
    data = {
        'email': '<EMAIL>',
        'type': 'register'
    }
    response = requests.post(url, json=data)
    print_test_result("发送邮箱验证码", response)

    # 2. 用户注册（使用固定验证码进行测试）
    print("\n2. 测试用户注册")
    url = f'{BASE_URL}/auth/register'
    data = {
        'username': f'testuser_{int(time.time())}',
        'email': f'test_{int(time.time())}@example.com',
        'password': 'Test123456',
        'nickname': '测试用户',
        'emailCode': '123456'  # 在实际测试中需要真实验证码
    }
    response = requests.post(url, json=data)
    result = print_test_result("用户注册", response)

    if result and result.get('success'):
        test_data['token'] = result['data']['token']
        test_data['refresh_token'] = result['data']['refreshToken']
        test_data['user_id'] = result['data']['user']['id']

    # 3. 用户登录
    print("\n3. 测试用户登录")
    url = f'{BASE_URL}/auth/login'
    data = {
        'username': data['username'],  # 使用注册时的用户名
        'password': 'Test123456'
    }
    response = requests.post(url, json=data)
    result = print_test_result("用户登录", response)

    if result and result.get('success'):
        test_data['token'] = result['data']['token']
        test_data['refresh_token'] = result['data']['refreshToken']
        test_data['user_id'] = result['data']['user']['id']

    # 4. 获取用户信息
    print("\n4. 测试获取用户信息")
    url = f'{BASE_URL}/auth/profile'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("获取用户信息", response)

    # 5. 更新用户信息
    print("\n5. 测试更新用户信息")
    url = f'{BASE_URL}/auth/profile'
    data = {
        'nickname': '更新后的昵称',
        'bio': '这是我的个人简介'
    }
    response = requests.put(url, json=data, headers=get_auth_headers())
    print_test_result("更新用户信息", response)

    # 6. 刷新令牌
    print("\n6. 测试刷新令牌")
    url = f'{BASE_URL}/auth/refresh'
    data = {
        'refreshToken': test_data['refresh_token']
    }
    response = requests.post(url, json=data)
    result = print_test_result("刷新令牌", response)

    if result and result.get('success'):
        test_data['token'] = result['data']['token']


# ==================== 分类API测试 ====================

def test_category_apis():
    """测试分类相关API"""
    print("\n📂 开始测试分类API...")

    # 1. 获取分类列表
    print("\n1. 测试获取分类列表")
    url = f'{BASE_URL}/categories'
    response = requests.get(url)
    result = print_test_result("获取分类列表", response)

    if result and result.get('success') and result['data']:
        test_data['category_id'] = result['data'][0]['id']

    # 2. 获取分类详情
    if test_data['category_id']:
        print("\n2. 测试获取分类详情")
        url = f'{BASE_URL}/categories/{test_data["category_id"]}'
        response = requests.get(url)
        print_test_result("获取分类详情", response)


# ==================== 商品API测试 ====================

def test_product_apis():
    """测试商品相关API"""
    print("\n🛍️ 开始测试商品API...")

    # 1. 获取商品列表
    print("\n1. 测试获取商品列表")
    url = f'{BASE_URL}/products'
    params = {
        'page': 1,
        'limit': 10,
        'status': 'active'
    }
    response = requests.get(url, params=params)
    result = print_test_result("获取商品列表", response)

    # 2. 发布商品
    print("\n2. 测试发布商品")
    url = f'{BASE_URL}/products'

    # 创建一个简单的测试图片（1x1像素的PNG）
    test_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

    data = {
        'title': f'测试商品_{int(time.time())}',
        'description': '这是一个测试商品的描述',
        'price': 99.99,
        'originalPrice': 199.99,
        'condition': '九成新',
        'location': '北京市',
        'quantity': 1,
        'categoryId': test_data['category_id'] or 1,
        'images': [
            {
                'data': f'data:image/png;base64,{test_image_data}',
                'isMain': True
            }
        ]
    }
    response = requests.post(url, json=data, headers=get_auth_headers())
    result = print_test_result("发布商品", response)

    if result and result.get('success'):
        test_data['product_id'] = result['data']['id']

    # 3. 获取商品详情
    if test_data['product_id']:
        print("\n3. 测试获取商品详情")
        url = f'{BASE_URL}/products/{test_data["product_id"]}'
        response = requests.get(url)
        print_test_result("获取商品详情", response)

    # 4. 收藏商品
    if test_data['product_id']:
        print("\n4. 测试收藏商品")
        url = f'{BASE_URL}/products/{test_data["product_id"]}/favorite'
        response = requests.post(url, headers=get_auth_headers())
        print_test_result("收藏商品", response)

    # 5. 获取我的商品
    print("\n5. 测试获取我的商品")
    url = f'{BASE_URL}/products/my'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("获取我的商品", response)

    # 6. 获取我的收藏
    print("\n6. 测试获取我的收藏")
    url = f'{BASE_URL}/products/favorites'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("获取我的收藏", response)


# ==================== 搜索API测试 ====================

def test_search_apis():
    """测试搜索相关API"""
    print("\n🔍 开始测试搜索API...")

    # 1. 搜索商品
    print("\n1. 测试搜索商品")
    url = f'{BASE_URL}/search'
    params = {
        'q': '测试',
        'page': 1,
        'limit': 10
    }
    response = requests.get(url, params=params)
    print_test_result("搜索商品", response)

    # 2. 获取搜索建议
    print("\n2. 测试获取搜索建议")
    url = f'{BASE_URL}/search/suggestions'
    params = {'q': '测试'}
    response = requests.get(url, params=params)
    print_test_result("获取搜索建议", response)

    # 3. 获取热门关键词
    print("\n3. 测试获取热门关键词")
    url = f'{BASE_URL}/search/hot-keywords'
    response = requests.get(url)
    print_test_result("获取热门关键词", response)

    # 4. 获取搜索筛选选项
    print("\n4. 测试获取搜索筛选选项")
    url = f'{BASE_URL}/search/filters'
    response = requests.get(url)
    print_test_result("获取搜索筛选选项", response)


# ==================== 订单API测试 ====================

def test_order_apis():
    """测试订单相关API"""
    print("\n📦 开始测试订单API...")

    # 1. 创建订单
    if test_data['product_id']:
        print("\n1. 测试创建订单")
        url = f'{BASE_URL}/orders'
        data = {
            'productId': test_data['product_id'],
            'quantity': 1,
            'message': '测试订单备注'
        }
        response = requests.post(url, json=data, headers=get_auth_headers())
        result = print_test_result("创建订单", response)

        if result and result.get('success'):
            test_data['order_id'] = result['data']['id']

    # 2. 获取订单列表
    print("\n2. 测试获取订单列表")
    url = f'{BASE_URL}/orders'
    params = {
        'type': 'buy',
        'page': 1,
        'limit': 10
    }
    response = requests.get(url, params=params, headers=get_auth_headers())
    print_test_result("获取订单列表", response)

    # 3. 获取订单详情
    if test_data['order_id']:
        print("\n3. 测试获取订单详情")
        url = f'{BASE_URL}/orders/{test_data["order_id"]}'
        response = requests.get(url, headers=get_auth_headers())
        print_test_result("获取订单详情", response)


# ==================== 消息API测试 ====================

def test_message_apis():
    """测试消息相关API"""
    print("\n💬 开始测试消息API...")

    # 1. 获取对话列表
    print("\n1. 测试获取对话列表")
    url = f'{BASE_URL}/messages/conversations'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("获取对话列表", response)

    # 2. 发送消息
    if test_data['product_id'] and test_data['user_id']:
        print("\n2. 测试发送消息")
        url = f'{BASE_URL}/messages'
        data = {
            'receiverId': test_data['user_id'],  # 发给自己进行测试
            'productId': test_data['product_id'],
            'content': '这是一条测试消息'
        }
        response = requests.post(url, json=data, headers=get_auth_headers())
        print_test_result("发送消息", response)

    # 3. 获取未读消息数量
    print("\n3. 测试获取未读消息数量")
    url = f'{BASE_URL}/messages/unread-count'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("获取未读消息数量", response)


# ==================== 通知API测试 ====================

def test_notification_apis():
    """测试通知相关API"""
    print("\n🔔 开始测试通知API...")

    # 1. 获取通知列表
    print("\n1. 测试获取通知列表")
    url = f'{BASE_URL}/notifications'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("获取通知列表", response)

    # 2. 获取未读通知数量
    print("\n2. 测试获取未读通知数量")
    url = f'{BASE_URL}/notifications/unread-count'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("获取未读通知数量", response)

    # 3. 获取最新通知
    print("\n3. 测试获取最新通知")
    url = f'{BASE_URL}/notifications/latest'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("获取最新通知", response)


# ==================== 文件上传API测试 ====================

def test_upload_apis():
    """测试文件上传相关API"""
    print("\n📤 开始测试文件上传API...")

    # 1. 上传头像
    print("\n1. 测试上传头像")
    url = f'{BASE_URL}/upload/avatar'

    # 创建一个简单的测试图片
    test_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

    data = {
        'image': f'data:image/png;base64,{test_image_data}'
    }
    response = requests.post(url, json=data, headers=get_auth_headers())
    print_test_result("上传头像", response)

    # 2. 上传商品图片
    print("\n2. 测试上传商品图片")
    url = f'{BASE_URL}/upload/product-images'
    data = {
        'images': [
            {
                'data': f'data:image/png;base64,{test_image_data}',
                'isMain': True
            }
        ]
    }
    response = requests.post(url, json=data, headers=get_auth_headers())
    print_test_result("上传商品图片", response)


# ==================== 统计API测试 ====================

def test_stats_apis():
    """测试统计相关API"""
    print("\n📊 开始测试统计API...")

    # 1. 用户仪表板统计
    print("\n1. 测试用户仪表板统计")
    url = f'{BASE_URL}/stats/dashboard'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("用户仪表板统计", response)

    # 2. 销售趋势统计
    print("\n2. 测试销售趋势统计")
    url = f'{BASE_URL}/stats/sales-trend'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("销售趋势统计", response)

    # 3. 商品表现统计
    print("\n3. 测试商品表现统计")
    url = f'{BASE_URL}/stats/product-performance'
    response = requests.get(url, headers=get_auth_headers())
    print_test_result("商品表现统计", response)


# ==================== 主测试函数 ====================

def main():
    """主测试函数"""
    print("🚀 开始API完整测试套件")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"API基础URL: {BASE_URL}")

    try:
        # 按顺序执行所有测试
        test_auth_apis()
        test_category_apis()
        test_product_apis()
        test_search_apis()
        test_order_apis()
        test_message_apis()
        test_notification_apis()
        test_upload_apis()
        test_stats_apis()

        print("\n" + "="*60)
        print("✅ 所有API测试完成！")
        print("\n测试数据:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理：登出
        if test_data['token']:
            print("\n🔐 执行登出清理...")
            url = f'{BASE_URL}/auth/logout'
            response = requests.post(url, headers=get_auth_headers())
            print_test_result("用户登出", response)


if __name__ == '__main__':
    main()
