{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-bell me-2"></i>通知中心</h2>
        <div>
            <button class="btn btn-outline-primary btn-sm me-2" onclick="markAllAsRead()">
                <i class="fas fa-check-double"></i> 全部标记为已读
            </button>
            <button class="btn btn-outline-danger btn-sm" onclick="clearAllNotifications()">
                <i class="fas fa-trash"></i> 清空通知
            </button>
        </div>
    </div>

    <!-- 通知筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <select class="form-select" id="typeFilter" onchange="filterNotifications()">
                        <option value="">全部类型</option>
                        <option value="order">订单通知</option>
                        <option value="system">系统通知</option>
                        <option value="message">消息通知</option>
                        <option value="review">评价通知</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter" onchange="filterNotifications()">
                        <option value="">全部状态</option>
                        <option value="unread">未读</option>
                        <option value="read">已读</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索通知内容..." 
                           onkeyup="filterNotifications()">
                </div>
            </div>
        </div>
    </div>

    <!-- 通知列表 -->
    <div class="notification-list-container">
        {% if notifications.items %}
        {% for notification in notifications.items %}
        <div class="card mb-3 notification-card {{ 'unread' if not notification.is_read else 'read' }}" 
             data-notification-id="{{ notification.id }}" 
             data-type="{{ notification.type }}"
             data-status="{{ 'unread' if not notification.is_read else 'read' }}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas {{ notification.get_icon() }} me-2 text-{{ notification.get_color() }}"></i>
                            <h6 class="mb-0 notification-title">{{ notification.title }}</h6>
                            {% if not notification.is_read %}
                            <span class="badge bg-primary ms-2">新</span>
                            {% endif %}
                        </div>
                        <p class="notification-content mb-2">{{ notification.content }}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            {{ moment(notification.created_at).fromNow() }}
                        </small>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary" type="button" 
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            {% if not notification.is_read %}
                            <li>
                                <a class="dropdown-item" href="#" onclick="markAsRead({{ notification.id }})">
                                    <i class="fas fa-check me-2"></i>标记为已读
                                </a>
                            </li>
                            {% else %}
                            <li>
                                <a class="dropdown-item" href="#" onclick="markAsUnread({{ notification.id }})">
                                    <i class="fas fa-undo me-2"></i>标记为未读
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <a class="dropdown-item text-danger" href="#" onclick="deleteNotification({{ notification.id }})">
                                    <i class="fas fa-trash me-2"></i>删除
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                
                {% if notification.related_id and notification.type == 'order' %}
                <div class="mt-3">
                    <a href="{{ url_for('orders.detail', id=notification.related_id) }}" 
                       class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i>查看订单
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <!-- 分页 -->
        {% if notifications.pages > 1 %}
        <nav aria-label="通知分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if notifications.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('notifications.notifications', page=notifications.prev_num) }}">上一页</a>
                </li>
                {% endif %}

                {% for page_num in notifications.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != notifications.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('notifications.notifications', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if notifications.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('notifications.notifications', page=notifications.next_num) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-bell-slash fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">暂无通知</h4>
            <p class="text-muted">您还没有收到任何通知</p>
        </div>
        {% endif %}
    </div>
</div>

{% block scripts %}
<script>
// 标记单个通知为已读
function markAsRead(notificationId) {
    $.ajax({
        url: `/api/notifications/${notificationId}/read`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            showAlert('操作失败，请重试', 'error');
        }
    });
}

// 标记单个通知为未读
function markAsUnread(notificationId) {
    $.ajax({
        url: `/api/notifications/${notificationId}/unread`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            showAlert('操作失败，请重试', 'error');
        }
    });
}

// 删除单个通知
function deleteNotification(notificationId) {
    if (confirm('确定要删除这条通知吗？')) {
        $.ajax({
            url: `/api/notifications/${notificationId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    $(`[data-notification-id="${notificationId}"]`).fadeOut(function() {
                        $(this).remove();
                    });
                    showAlert('通知已删除', 'success');
                }
            },
            error: function() {
                showAlert('删除失败，请重试', 'error');
            }
        });
    }
}

// 全部标记为已读
function markAllAsRead() {
    $.ajax({
        url: '/api/notifications/mark-all-read',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            showAlert('操作失败，请重试', 'error');
        }
    });
}

// 清空所有通知
function clearAllNotifications() {
    if (confirm('确定要清空所有通知吗？此操作不可撤销。')) {
        $.ajax({
            url: '/api/notifications/clear-all',
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    location.reload();
                }
            },
            error: function() {
                showAlert('操作失败，请重试', 'error');
            }
        });
    }
}

// 筛选通知
function filterNotifications() {
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    document.querySelectorAll('.notification-card').forEach(card => {
        const type = card.dataset.type;
        const status = card.dataset.status;
        const content = card.textContent.toLowerCase();
        
        let show = true;
        
        if (typeFilter && type !== typeFilter) show = false;
        if (statusFilter && status !== statusFilter) show = false;
        if (searchInput && !content.includes(searchInput)) show = false;
        
        card.style.display = show ? 'block' : 'none';
    });
}
</script>
{% endblock %}
