{% extends "base.html" %}

{% block content %}
<div class="container">
    <h2 class="mb-4">商品默认图片系统测试</h2>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">分类默认图片展示</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">当商品没有上传图片时，系统会根据商品分类自动生成个性化的默认图片</p>
                    
                    <div class="row">
                        {% for category in categories %}
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100">
                                <img src="{{ url_for('main.generate_product_default_image', category_id=category.id) }}" 
                                     class="card-img-top" alt="{{ category.name }}" 
                                     style="height: 200px; object-fit: cover;">
                                <div class="card-body text-center">
                                    <h6 class="card-title">{{ category.name }}</h6>
                                    <p class="card-text small text-muted">
                                        <i class="{{ category.get_icon() }}"></i> 
                                        {{ category.get_icon() }}
                                    </p>
                                    <div class="d-flex justify-content-center align-items-center">
                                        <span class="badge me-2" style="background-color: {{ category.get_color() }}; color: white;">
                                            颜色示例
                                        </span>
                                        <small class="text-muted">ID: {{ category.id }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">不同尺寸展示</h5>
                </div>
                <div class="card-body">
                    {% if categories %}
                    {% set test_category = categories[0] %}
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <img src="{{ url_for('main.generate_product_default_image', category_id=test_category.id) }}" 
                                 class="img-fluid rounded" alt="{{ test_category.name }}" 
                                 style="width: 100px; height: 75px; object-fit: cover;">
                            <div class="small mt-2">小尺寸 (100x75)</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <img src="{{ url_for('main.generate_product_default_image', category_id=test_category.id) }}" 
                                 class="img-fluid rounded" alt="{{ test_category.name }}" 
                                 style="width: 200px; height: 150px; object-fit: cover;">
                            <div class="small mt-2">中尺寸 (200x150)</div>
                        </div>
                        <div class="col-md-6 text-center">
                            <img src="{{ url_for('main.generate_product_default_image', category_id=test_category.id) }}" 
                                 class="img-fluid rounded" alt="{{ test_category.name }}" 
                                 style="width: 400px; height: 300px; object-fit: cover;">
                            <div class="small mt-2">原始尺寸 (400x300)</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">商品卡片模拟</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">模拟实际商品展示效果</p>
                    <div class="row">
                        {% for category in categories[:8] %}
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100 product-card">
                                <img src="{{ url_for('main.generate_product_default_image', category_id=category.id) }}" 
                                     class="card-img-top" alt="{{ category.name }}" 
                                     style="height: 200px; object-fit: cover;">
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">
                                        示例{{ category.name }}商品
                                    </h6>
                                    <p class="card-text text-muted small flex-grow-1">
                                        这是一个{{ category.name }}分类的示例商品，展示默认图片效果。
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center mt-auto">
                                        <span class="text-danger fw-bold">¥99.00</span>
                                        <small class="text-muted">
                                            <i class="fas fa-eye"></i> 123
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 商品默认图片系统说明</h6>
                <ul class="mb-0">
                    <li><strong>智能分类识别</strong>：根据商品分类自动生成对应的默认图片</li>
                    <li><strong>个性化颜色</strong>：每个分类都有独特的渐变色彩，基于分类ID生成</li>
                    <li><strong>图标映射</strong>：支持30+种分类图标，可在数据库中自定义</li>
                    <li><strong>多格式支持</strong>：优先生成PNG格式，备用SVG格式</li>
                    <li><strong>响应式设计</strong>：适配不同尺寸的展示需求</li>
                    <li><strong>性能优化</strong>：动态生成，无需存储大量静态文件</li>
                    <li><strong>品牌一致性</strong>：与用户头像系统保持统一的设计风格</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.product-card {
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
