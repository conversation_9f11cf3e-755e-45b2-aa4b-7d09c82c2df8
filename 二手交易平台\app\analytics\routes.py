#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据统计与分析路由
"""

from flask import render_template, jsonify, request
from flask_login import login_required, current_user
from app.analytics import bp
from app.models import User, Product, Order, Category, ProductStatus, OrderStatus
from app import db
from sqlalchemy import func, desc, extract
from datetime import datetime, timedelta
import json

@bp.route('/dashboard')
@login_required
def dashboard():
    """用户数据仪表板"""
    if current_user.is_admin():
        return render_template('analytics/admin_dashboard.html', title='数据分析')
    else:
        return render_template('analytics/user_dashboard.html', title='我的数据')

@bp.route('/api/user_stats')
@login_required
def user_stats():
    """用户统计数据API"""
    # 我的商品统计
    my_products = Product.query.filter_by(seller_id=current_user.id).count()
    active_products = Product.query.filter_by(
        seller_id=current_user.id, 
        status=ProductStatus.ACTIVE
    ).count()
    sold_products = Product.query.filter_by(
        seller_id=current_user.id, 
        status=ProductStatus.SOLD
    ).count()
    
    # 我的订单统计
    buy_orders = Order.query.filter_by(buyer_id=current_user.id).count()
    sell_orders = Order.query.filter_by(seller_id=current_user.id).count()
    completed_orders = Order.query.filter_by(
        seller_id=current_user.id,
        status=OrderStatus.COMPLETED
    ).count()
    
    # 收入统计
    total_income = db.session.query(func.sum(Order.total_amount)).filter_by(
        seller_id=current_user.id,
        status=OrderStatus.COMPLETED
    ).scalar() or 0
    
    # 最近30天的销售趋势
    thirty_days_ago = datetime.now() - timedelta(days=30)
    daily_sales = db.session.query(
        func.date(Order.completed_at).label('date'),
        func.count(Order.id).label('count'),
        func.sum(Order.total_amount).label('amount')
    ).filter(
        Order.seller_id == current_user.id,
        Order.status == OrderStatus.COMPLETED,
        Order.completed_at >= thirty_days_ago
    ).group_by(func.date(Order.completed_at)).all()
    
    # 商品分类统计
    category_stats = db.session.query(
        Category.name,
        func.count(Product.id).label('count')
    ).join(Product).filter(
        Product.seller_id == current_user.id
    ).group_by(Category.id, Category.name).all()
    
    return jsonify({
        'products': {
            'total': my_products,
            'active': active_products,
            'sold': sold_products
        },
        'orders': {
            'buy': buy_orders,
            'sell': sell_orders,
            'completed': completed_orders
        },
        'income': {
            'total': float(total_income)
        },
        'daily_sales': [
            {
                'date': sale.date.isoformat(),
                'count': sale.count,
                'amount': float(sale.amount)
            } for sale in daily_sales
        ],
        'category_stats': [
            {
                'name': stat.name,
                'count': stat.count
            } for stat in category_stats
        ]
    })

@bp.route('/api/admin_stats')
@login_required
def admin_stats():
    """管理员统计数据API"""
    if not current_user.is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    
    # 基础统计
    total_users = User.query.count()
    total_products = Product.query.count()
    total_orders = Order.query.count()
    total_revenue = db.session.query(func.sum(Order.total_amount)).filter_by(
        status=OrderStatus.COMPLETED
    ).scalar() or 0
    
    # 最近7天的数据
    seven_days_ago = datetime.now() - timedelta(days=7)
    new_users_week = User.query.filter(User.created_at >= seven_days_ago).count()
    new_products_week = Product.query.filter(Product.created_at >= seven_days_ago).count()
    new_orders_week = Order.query.filter(Order.created_at >= seven_days_ago).count()
    
    # 月度趋势数据
    monthly_data = []
    for i in range(12):
        month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        
        users_count = User.query.filter(
            User.created_at >= month_start,
            User.created_at <= month_end
        ).count()
        
        products_count = Product.query.filter(
            Product.created_at >= month_start,
            Product.created_at <= month_end
        ).count()
        
        orders_count = Order.query.filter(
            Order.created_at >= month_start,
            Order.created_at <= month_end
        ).count()
        
        revenue = db.session.query(func.sum(Order.total_amount)).filter(
            Order.completed_at >= month_start,
            Order.completed_at <= month_end,
            Order.status == OrderStatus.COMPLETED
        ).scalar() or 0
        
        monthly_data.append({
            'month': month_start.strftime('%Y-%m'),
            'users': users_count,
            'products': products_count,
            'orders': orders_count,
            'revenue': float(revenue)
        })
    
    monthly_data.reverse()
    
    # 商品状态分布
    product_status_stats = db.session.query(
        Product.status,
        func.count(Product.id).label('count')
    ).group_by(Product.status).all()
    
    # 订单状态分布
    order_status_stats = db.session.query(
        Order.status,
        func.count(Order.id).label('count')
    ).group_by(Order.status).all()
    
    # 热门分类
    popular_categories = db.session.query(
        Category.name,
        func.count(Product.id).label('product_count'),
        func.count(Order.id).label('order_count')
    ).join(Product).outerjoin(Order).group_by(
        Category.id, Category.name
    ).order_by(desc('product_count')).limit(10).all()
    
    return jsonify({
        'overview': {
            'total_users': total_users,
            'total_products': total_products,
            'total_orders': total_orders,
            'total_revenue': float(total_revenue),
            'new_users_week': new_users_week,
            'new_products_week': new_products_week,
            'new_orders_week': new_orders_week
        },
        'monthly_trends': monthly_data,
        'product_status': [
            {
                'status': stat.status.value,
                'count': stat.count
            } for stat in product_status_stats
        ],
        'order_status': [
            {
                'status': stat.status.value,
                'count': stat.count
            } for stat in order_status_stats
        ],
        'popular_categories': [
            {
                'name': cat.name,
                'product_count': cat.product_count,
                'order_count': cat.order_count or 0
            } for cat in popular_categories
        ]
    })

@bp.route('/reports')
@login_required
def reports():
    """数据报表页面"""
    if not current_user.is_admin():
        return redirect(url_for('analytics.dashboard'))
    
    return render_template('analytics/reports.html', title='数据报表')

@bp.route('/api/export_data')
@login_required
def export_data():
    """导出数据API"""
    if not current_user.is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    
    data_type = request.args.get('type', 'users')
    
    if data_type == 'users':
        users = User.query.all()
        data = [
            {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role.value,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat()
            } for user in users
        ]
    elif data_type == 'products':
        products = Product.query.all()
        data = [
            {
                'id': product.id,
                'title': product.title,
                'price': float(product.price),
                'status': product.status.value,
                'seller': product.seller.username,
                'category': product.category.name,
                'created_at': product.created_at.isoformat()
            } for product in products
        ]
    elif data_type == 'orders':
        orders = Order.query.all()
        data = [
            {
                'id': order.id,
                'order_no': order.order_no,
                'total_amount': float(order.total_amount),
                'status': order.status.value,
                'buyer': order.buyer.username,
                'seller': order.seller.username,
                'product': order.product.title,
                'created_at': order.created_at.isoformat()
            } for order in orders
        ]
    else:
        return jsonify({'error': 'Invalid data type'}), 400
    
    return jsonify({'data': data})
