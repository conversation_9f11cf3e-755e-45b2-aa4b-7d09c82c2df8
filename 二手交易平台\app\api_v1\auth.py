#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端认证API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import User, EmailVerificationCode
from app import db
from app.utils.jwt_utils import generate_tokens, jwt_required, refresh_access_token, verify_token
from app.utils.api_response import (
    success_response, error_response, validation_error_response,
    unauthorized_response, not_found_response
)
from app.auth.email import send_email
from app.utils.datetime_utils import local_now
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import timedelta
import re
import base64
import os
from PIL import Image
from io import BytesIO


def validate_email(email):
    """验证邮箱格式"""
    pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
    return re.match(pattern, email) is not None


def validate_password(password):
    """验证密码强度"""
    if len(password) < 8:
        return False, "密码长度至少8位"
    if not re.search(r'[A-Za-z]', password):
        return False, "密码必须包含字母"
    if not re.search(r'\d', password):
        return False, "密码必须包含数字"
    return True, ""


@bp.route('/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 验证必填字段
        required_fields = ['username', 'email', 'password', 'emailCode']
        for field in required_fields:
            if not data.get(field):
                return validation_error_response({field: f'{field}不能为空'})
        
        username = data['username'].strip()
        email = data['email'].strip().lower()
        password = data['password']
        email_code = data['emailCode']
        nickname = data.get('nickname', username)
        
        # 验证用户名
        if len(username) < 3 or len(username) > 20:
            return validation_error_response({'username': '用户名长度必须在3-20位之间'})
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return validation_error_response({'username': '用户名只能包含字母、数字和下划线'})
        
        # 验证邮箱
        if not validate_email(email):
            return validation_error_response({'email': '邮箱格式不正确'})
        
        # 验证密码
        is_valid, msg = validate_password(password)
        if not is_valid:
            return validation_error_response({'password': msg})
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return error_response('BIZ_002', '用户名已存在')
        
        # 检查邮箱是否已存在
        if User.query.filter_by(email=email).first():
            return error_response('BIZ_003', '邮箱已被注册')
        
        # 验证邮箱验证码
        verification = EmailVerificationCode.query.filter_by(
            email=email,
            code=email_code,
            is_used=False
        ).first()
        
        if not verification or verification.is_expired():
            return error_response('VALID_005', '验证码无效或已过期')
        
        # 创建用户
        user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(password),
            nickname=nickname,
            is_active=True,
            created_at=local_now()
        )
        
        db.session.add(user)
        
        # 标记验证码为已使用
        verification.is_used = True
        
        db.session.commit()
        
        # 生成JWT令牌
        access_token, refresh_token = generate_tokens(user.id)
        
        return success_response({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'nickname': user.nickname,
                'avatar': user.avatar_url(),
                'role': user.role.value
            },
            'token': access_token,
            'refreshToken': refresh_token
        }, '注册成功', 201)
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"注册失败: {str(e)}")
        return error_response('SYS_001', '注册失败，请稍后重试', http_code=500)


@bp.route('/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 验证必填字段
        if not data.get('username') or not data.get('password'):
            return validation_error_response({
                'username': '用户名不能为空' if not data.get('username') else '',
                'password': '密码不能为空' if not data.get('password') else ''
            })
        
        username = data['username'].strip()
        password = data['password']
        
        # 查找用户（支持用户名或邮箱登录）
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()
        
        if not user or not check_password_hash(user.password_hash, password):
            return error_response('AUTH_003', '用户名或密码错误', http_code=401)
        
        if not user.is_active:
            return error_response('AUTH_005', '账户已被禁用', http_code=403)
        
        # 更新最后登录时间
        user.last_login_at = local_now()
        db.session.commit()
        
        # 生成JWT令牌
        access_token, refresh_token = generate_tokens(user.id)
        
        return success_response({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'nickname': user.nickname,
                'avatar': user.avatar_url(),
                'role': user.role.value
            },
            'token': access_token,
            'refreshToken': refresh_token
        }, '登录成功')
        
    except Exception as e:
        current_app.logger.error(f"登录失败: {str(e)}")
        return error_response('SYS_001', '登录失败，请稍后重试', http_code=500)


@bp.route('/auth/refresh', methods=['POST'])
def refresh():
    """刷新访问令牌"""
    try:
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        refresh_token = data.get('refreshToken')

        if not refresh_token:
            return validation_error_response({'refreshToken': '刷新令牌不能为空'})

        access_token = refresh_access_token(refresh_token)
        if not access_token:
            return error_response('AUTH_002', '刷新令牌无效或已过期', http_code=401)

        return success_response({
            'token': access_token
        }, '令牌刷新成功')

    except Exception as e:
        current_app.logger.error(f"令牌刷新失败: {str(e)}")
        return error_response('SYS_001', '令牌刷新失败', http_code=500)


@bp.route('/auth/profile', methods=['GET'])
@jwt_required
def get_profile():
    """获取用户信息"""
    try:
        user = request.current_user
        
        return success_response({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'nickname': user.nickname,
            'bio': user.bio,
            'avatar': user.avatar_url(),
            'role': user.role.value,
            'isActive': user.is_active,
            'createdAt': user.created_at.isoformat() + 'Z',
            'lastLoginAt': user.last_login_at.isoformat() + 'Z' if user.last_login_at else None
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户信息失败: {str(e)}")
        return error_response('SYS_001', '获取用户信息失败', http_code=500)


@bp.route('/auth/profile', methods=['PUT'])
@jwt_required
def update_profile():
    """更新用户信息"""
    try:
        user = request.current_user
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 可更新的字段
        if 'nickname' in data:
            nickname = data['nickname'].strip()
            if len(nickname) > 50:
                return validation_error_response({'nickname': '昵称长度不能超过50个字符'})
            user.nickname = nickname
        
        if 'bio' in data:
            bio = data['bio'].strip()
            if len(bio) > 200:
                return validation_error_response({'bio': '个人简介长度不能超过200个字符'})
            user.bio = bio
        
        # 处理头像上传
        if 'avatar' in data and data['avatar']:
            try:
                # 解析base64图片
                avatar_data = data['avatar']
                if avatar_data.startswith('data:image'):
                    # 移除data:image/jpeg;base64,前缀
                    avatar_data = avatar_data.split(',')[1]
                
                image_data = base64.b64decode(avatar_data)
                
                # 验证图片
                image = Image.open(BytesIO(image_data))
                if image.format.lower() not in ['jpeg', 'jpg', 'png', 'gif']:
                    return validation_error_response({'avatar': '不支持的图片格式'})
                
                # 保存头像
                filename = f"avatar_{user.id}_{local_now().strftime('%Y%m%d_%H%M%S')}.jpg"
                avatar_dir = os.path.join(current_app.static_folder, 'uploads', 'avatars')
                os.makedirs(avatar_dir, exist_ok=True)
                
                # 调整图片大小
                image = image.convert('RGB')
                image.thumbnail((200, 200), Image.Resampling.LANCZOS)
                
                avatar_path = os.path.join(avatar_dir, filename)
                image.save(avatar_path, 'JPEG', quality=85)
                
                user.avatar = filename
                
            except Exception as e:
                return validation_error_response({'avatar': '头像上传失败'})
        
        user.updated_at = local_now()
        db.session.commit()
        
        return success_response({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'nickname': user.nickname,
            'bio': user.bio,
            'avatar': user.avatar_url(),
            'role': user.role.value
        }, '用户信息更新成功')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户信息失败: {str(e)}")
        return error_response('SYS_001', '更新用户信息失败', http_code=500)


@bp.route('/auth/send-email-code', methods=['POST'])
def send_email_code():
    """发送邮箱验证码"""
    try:
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        email = data.get('email', '').strip().lower()
        code_type = data.get('type', 'register')  # register, reset_password

        if not email:
            return validation_error_response({'email': '邮箱不能为空'})

        if not validate_email(email):
            return validation_error_response({'email': '邮箱格式不正确'})

        # 检查邮箱是否已注册（注册时检查）
        if code_type == 'register':
            if User.query.filter_by(email=email).first():
                return error_response('BIZ_003', '邮箱已被注册')
        elif code_type == 'reset_password':
            if not User.query.filter_by(email=email).first():
                return error_response('BIZ_004', '邮箱未注册')

        # 检查发送频率限制（1分钟内只能发送一次）
        recent_code = EmailVerificationCode.query.filter_by(email=email).filter(
            EmailVerificationCode.created_at > local_now() - timedelta(minutes=1)
        ).first()

        if recent_code:
            return error_response('BIZ_005', '验证码发送过于频繁，请稍后再试')

        # 生成验证码
        import random
        code = ''.join([str(random.randint(0, 9)) for _ in range(6)])

        # 保存验证码
        verification = EmailVerificationCode(
            email=email,
            code=code,
            created_at=local_now()
        )
        db.session.add(verification)
        db.session.commit()

        # 发送邮件
        try:
            if code_type == 'register':
                subject = '二手交易平台 - 注册验证码'
                content = f'您的注册验证码是：{code}，有效期10分钟。'
            else:
                subject = '二手交易平台 - 密码重置验证码'
                content = f'您的密码重置验证码是：{code}，有效期10分钟。'

            send_email(
                subject=subject,
                sender=current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>'),
                recipients=[email],
                text_body=content,
                html_body=f'<p>{content}</p>'
            )

            return success_response(None, '验证码发送成功')

        except Exception as e:
            current_app.logger.error(f"发送邮件失败: {str(e)}")
            return error_response('SYS_002', '验证码发送失败，请稍后重试')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"发送验证码失败: {str(e)}")
        return error_response('SYS_001', '发送验证码失败', http_code=500)


@bp.route('/auth/reset-password', methods=['POST'])
def reset_password():
    """重置密码"""
    try:
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 验证必填字段
        required_fields = ['email', 'emailCode', 'newPassword']
        for field in required_fields:
            if not data.get(field):
                return validation_error_response({field: f'{field}不能为空'})

        email = data['email'].strip().lower()
        email_code = data['emailCode']
        new_password = data['newPassword']

        # 验证邮箱
        if not validate_email(email):
            return validation_error_response({'email': '邮箱格式不正确'})

        # 验证密码
        is_valid, msg = validate_password(new_password)
        if not is_valid:
            return validation_error_response({'newPassword': msg})

        # 查找用户
        user = User.query.filter_by(email=email).first()
        if not user:
            return error_response('BIZ_004', '邮箱未注册')

        # 验证邮箱验证码
        verification = EmailVerificationCode.query.filter_by(
            email=email,
            code=email_code,
            is_used=False
        ).first()

        if not verification or verification.is_expired():
            return error_response('VALID_005', '验证码无效或已过期')

        # 更新密码
        user.password_hash = generate_password_hash(new_password)
        user.updated_at = local_now()

        # 标记验证码为已使用
        verification.is_used = True

        db.session.commit()

        return success_response(None, '密码重置成功')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"重置密码失败: {str(e)}")
        return error_response('SYS_001', '重置密码失败', http_code=500)


@bp.route('/auth/change-password', methods=['POST'])
@jwt_required
def change_password():
    """修改密码"""
    try:
        user = request.current_user
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 验证必填字段
        required_fields = ['oldPassword', 'newPassword']
        for field in required_fields:
            if not data.get(field):
                return validation_error_response({field: f'{field}不能为空'})

        old_password = data['oldPassword']
        new_password = data['newPassword']

        # 验证旧密码
        if not check_password_hash(user.password_hash, old_password):
            return error_response('AUTH_003', '原密码错误')

        # 验证新密码
        is_valid, msg = validate_password(new_password)
        if not is_valid:
            return validation_error_response({'newPassword': msg})

        # 检查新密码是否与旧密码相同
        if check_password_hash(user.password_hash, new_password):
            return validation_error_response({'newPassword': '新密码不能与原密码相同'})

        # 更新密码
        user.password_hash = generate_password_hash(new_password)
        user.updated_at = local_now()
        db.session.commit()

        return success_response(None, '密码修改成功')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修改密码失败: {str(e)}")
        return error_response('SYS_001', '修改密码失败', http_code=500)


@bp.route('/auth/logout', methods=['POST'])
@jwt_required
def logout():
    """用户登出"""
    try:
        # 获取当前token
        auth_header = request.headers.get('Authorization')
        if auth_header:
            token = auth_header.split(' ')[1]  # Bearer <token>

            # 将token加入黑名单
            from app.utils.token_blacklist import blacklist_token
            blacklist_token(token)

        return success_response(None, '登出成功')

    except Exception as e:
        current_app.logger.error(f"登出失败: {str(e)}")
        return error_response('SYS_001', '登出失败', http_code=500)
