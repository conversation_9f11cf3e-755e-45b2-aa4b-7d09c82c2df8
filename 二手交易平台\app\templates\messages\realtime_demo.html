{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-broadcast-tower"></i> 消息中心实时更新演示</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>实时更新功能已启用！</strong>
                        <ul class="mb-0 mt-2">
                            <li>每3秒自动检查新消息</li>
                            <li>新消息到达时会有高亮动画</li>
                            <li>未读消息数量实时更新</li>
                            <li>页面获得焦点时立即刷新</li>
                        </ul>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>当前状态</h6>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>更新间隔</span>
                                    <span class="badge bg-primary">3秒</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>最后更新</span>
                                    <span class="badge bg-success" id="lastUpdate">-</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>对话总数</span>
                                    <span class="badge bg-info" id="totalConversations">-</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>未读消息</span>
                                    <span class="badge bg-danger" id="unreadMessages">-</span>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>功能特性</h6>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <i class="fas fa-sync-alt text-primary"></i>
                                    自动刷新对话列表
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-bell text-warning"></i>
                                    新消息高亮提示
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-sort text-info"></i>
                                    智能排序更新
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-eye text-success"></i>
                                    页面焦点检测
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <a href="{{ url_for('messages.index') }}" class="btn btn-primary">
                            <i class="fas fa-comments"></i> 进入消息中心
                        </a>
                        <button class="btn btn-secondary" onclick="testUpdate()">
                            <i class="fas fa-refresh"></i> 手动测试更新
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let updateCount = 0;
    
    function updateStatus() {
        $.ajax({
            url: '/messages/api/conversations',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    updateCount++;
                    $('#lastUpdate').text(new Date().toLocaleTimeString());
                    $('#totalConversations').text(response.total_count);
                    
                    // 计算未读消息总数
                    let totalUnread = 0;
                    response.conversations.forEach(function(conv) {
                        totalUnread += conv.unread_count;
                    });
                    $('#unreadMessages').text(totalUnread);
                    
                    console.log(`更新 #${updateCount}:`, response);
                }
            },
            error: function() {
                $('#lastUpdate').text('更新失败').removeClass('bg-success').addClass('bg-danger');
            }
        });
    }
    
    // 立即更新一次
    updateStatus();
    
    // 每3秒更新一次
    setInterval(updateStatus, 3000);
    
    // 全局测试函数
    window.testUpdate = function() {
        updateStatus();
        showAlert('手动更新完成', 'success');
    };
});

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
