#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端文件上传API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import ProductImage
from app import db
from app.utils.jwt_utils import jwt_required
from app.utils.api_response import (
    success_response, error_response, validation_error_response
)
from app.utils.datetime_utils import local_now
import base64
import os
import uuid
from PIL import Image
from io import BytesIO
import mimetypes


def validate_image(image_data, max_size=5*1024*1024):
    """验证图片"""
    try:
        # 解析base64图片
        if image_data.startswith('data:image'):
            # 提取MIME类型
            header, image_data = image_data.split(',', 1)
            mime_type = header.split(':')[1].split(';')[0]
        else:
            mime_type = 'image/jpeg'  # 默认类型
        
        # 检查MIME类型
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        if mime_type not in allowed_types:
            return None, '不支持的图片格式，仅支持 JPEG、PNG、GIF'
        
        # 解码base64
        image_bytes = base64.b64decode(image_data)
        
        # 检查文件大小
        if len(image_bytes) > max_size:
            return None, f'图片大小不能超过{max_size // (1024*1024)}MB'
        
        # 验证图片格式
        image = Image.open(BytesIO(image_bytes))
        if image.format.lower() not in ['jpeg', 'jpg', 'png', 'gif']:
            return None, '无效的图片格式'
        
        return image, None
        
    except Exception as e:
        return None, f'图片验证失败: {str(e)}'


def save_image(image, upload_type, user_id, max_width=800, max_height=800, quality=85):
    """保存图片"""
    try:
        # 生成文件名
        file_extension = 'jpg'
        if upload_type == 'avatar':
            filename = f"avatar_{user_id}_{uuid.uuid4().hex[:8]}.{file_extension}"
            upload_dir = os.path.join(current_app.static_folder, 'uploads', 'avatars')
        elif upload_type == 'product':
            filename = f"product_{uuid.uuid4().hex[:12]}.{file_extension}"
            upload_dir = os.path.join(current_app.static_folder, 'uploads', 'products')
        else:
            filename = f"file_{uuid.uuid4().hex[:12]}.{file_extension}"
            upload_dir = os.path.join(current_app.static_folder, 'uploads', 'files')
        
        # 创建目录
        os.makedirs(upload_dir, exist_ok=True)
        
        # 处理图片
        if image.mode in ('RGBA', 'LA', 'P'):
            # 转换为RGB模式
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 调整图片大小
        if upload_type == 'avatar':
            # 头像裁剪为正方形
            size = min(image.size)
            left = (image.width - size) // 2
            top = (image.height - size) // 2
            image = image.crop((left, top, left + size, top + size))
            image.thumbnail((200, 200), Image.Resampling.LANCZOS)
        else:
            # 商品图片保持比例缩放
            image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
        
        # 保存图片
        file_path = os.path.join(upload_dir, filename)
        image.save(file_path, 'JPEG', quality=quality, optimize=True)
        
        return filename, None
        
    except Exception as e:
        return None, f'图片保存失败: {str(e)}'


@bp.route('/upload/avatar', methods=['POST'])
@jwt_required
def upload_avatar():
    """上传头像"""
    try:
        user = request.current_user
        
        # 检查请求数据
        if request.content_type and 'multipart/form-data' in request.content_type:
            # 处理表单上传
            if 'file' not in request.files:
                return validation_error_response({'file': '请选择要上传的文件'})
            
            file = request.files['file']
            if file.filename == '':
                return validation_error_response({'file': '请选择要上传的文件'})
            
            # 检查文件类型
            if not file.content_type.startswith('image/'):
                return validation_error_response({'file': '只能上传图片文件'})
            
            # 读取文件内容并转换为base64
            file_content = file.read()
            image_data = base64.b64encode(file_content).decode('utf-8')
            
        else:
            # 处理JSON上传
            data = request.get_json()
            if not data or not data.get('image'):
                return validation_error_response({'image': '图片数据不能为空'})
            
            image_data = data['image']
        
        # 验证图片
        image, error = validate_image(image_data, max_size=2*1024*1024)  # 头像限制2MB
        if error:
            return validation_error_response({'image': error})
        
        # 保存图片
        filename, error = save_image(image, 'avatar', user.id)
        if error:
            return error_response('SYS_003', error, http_code=500)
        
        # 删除旧头像
        if user.avatar:
            old_avatar_path = os.path.join(
                current_app.static_folder, 
                'uploads', 
                'avatars', 
                user.avatar
            )
            if os.path.exists(old_avatar_path):
                try:
                    os.remove(old_avatar_path)
                except:
                    pass  # 忽略删除失败
        
        # 更新用户头像
        user.avatar = filename
        user.updated_at = local_now()
        db.session.commit()
        
        return success_response({
            'filename': filename,
            'url': f'/static/uploads/avatars/{filename}',
            'avatarUrl': user.avatar_url()
        }, '头像上传成功')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"上传头像失败: {str(e)}")
        return error_response('SYS_001', '上传头像失败', http_code=500)


@bp.route('/upload/product-images', methods=['POST'])
@jwt_required
def upload_product_images():
    """上传商品图片"""
    try:
        user = request.current_user
        data = request.get_json()
        
        if not data or not data.get('images'):
            return validation_error_response({'images': '图片数据不能为空'})
        
        images_data = data['images']
        if not isinstance(images_data, list):
            return validation_error_response({'images': '图片数据格式不正确'})
        
        if len(images_data) > 5:
            return validation_error_response({'images': '最多只能上传5张图片'})
        
        uploaded_images = []
        
        for i, image_data in enumerate(images_data):
            if not image_data.get('data'):
                continue
            
            # 验证图片
            image, error = validate_image(image_data['data'])
            if error:
                return validation_error_response({'images': f'第{i+1}张图片: {error}'})
            
            # 保存图片
            filename, error = save_image(image, 'product', user.id)
            if error:
                # 清理已上传的图片
                for uploaded in uploaded_images:
                    try:
                        os.remove(os.path.join(
                            current_app.static_folder, 
                            'uploads', 
                            'products', 
                            uploaded['filename']
                        ))
                    except:
                        pass
                
                return error_response('SYS_003', f'第{i+1}张图片保存失败: {error}', http_code=500)
            
            uploaded_images.append({
                'filename': filename,
                'url': f'/static/uploads/products/{filename}',
                'isMain': image_data.get('isMain', False)
            })
        
        if not uploaded_images:
            return validation_error_response({'images': '没有有效的图片数据'})
        
        return success_response({
            'images': uploaded_images
        }, f'成功上传{len(uploaded_images)}张图片', 201)
        
    except Exception as e:
        current_app.logger.error(f"上传商品图片失败: {str(e)}")
        return error_response('SYS_001', '上传商品图片失败', http_code=500)


@bp.route('/upload/image', methods=['POST'])
@jwt_required
def upload_image():
    """通用图片上传"""
    try:
        user = request.current_user
        
        # 检查请求数据
        if request.content_type and 'multipart/form-data' in request.content_type:
            # 处理表单上传
            if 'file' not in request.files:
                return validation_error_response({'file': '请选择要上传的文件'})
            
            file = request.files['file']
            if file.filename == '':
                return validation_error_response({'file': '请选择要上传的文件'})
            
            # 检查文件类型
            if not file.content_type.startswith('image/'):
                return validation_error_response({'file': '只能上传图片文件'})
            
            # 读取文件内容并转换为base64
            file_content = file.read()
            image_data = base64.b64encode(file_content).decode('utf-8')
            
        else:
            # 处理JSON上传
            data = request.get_json()
            if not data or not data.get('image'):
                return validation_error_response({'image': '图片数据不能为空'})
            
            image_data = data['image']
        
        # 验证图片
        image, error = validate_image(image_data)
        if error:
            return validation_error_response({'image': error})
        
        # 保存图片
        filename, error = save_image(image, 'file', user.id)
        if error:
            return error_response('SYS_003', error, http_code=500)
        
        return success_response({
            'filename': filename,
            'url': f'/static/uploads/files/{filename}'
        }, '图片上传成功')
        
    except Exception as e:
        current_app.logger.error(f"上传图片失败: {str(e)}")
        return error_response('SYS_001', '上传图片失败', http_code=500)
