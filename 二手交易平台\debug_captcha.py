#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试图形验证码问题
"""

from app import create_app
from flask import session

def test_captcha():
    """测试图形验证码功能"""
    app = create_app()
    
    with app.test_client() as client:
        with client.session_transaction() as sess:
            print("测试图形验证码功能...")
            
            # 1. 访问注册页面
            response = client.get('/auth/register')
            print(f"注册页面状态码: {response.status_code}")
            
            # 2. 获取验证码图片
            response = client.get('/auth/captcha')
            print(f"验证码图片状态码: {response.status_code}")
            print(f"验证码图片类型: {response.headers.get('Content-Type')}")
            
        # 3. 检查session中的验证码
        with client.session_transaction() as sess:
            captcha_code = sess.get('captcha')
            print(f"Session中的验证码: {captcha_code}")
            
            if captcha_code:
                print(f"验证码长度: {len(captcha_code)}")
                print(f"验证码类型: {type(captcha_code)}")
                print(f"验证码内容: '{captcha_code}'")
            else:
                print("❌ Session中没有验证码")
                
        # 4. 测试验证码验证逻辑
        if captcha_code:
            # 模拟表单提交
            form_data = {
                'username': 'testuser',
                'email': '<EMAIL>',
                'phone': '13800138000',
                'email_code': '123456',
                'password': 'password123',
                'password2': 'password123',
                'captcha': captcha_code,  # 使用正确的验证码
                'csrf_token': 'test'
            }
            
            print(f"\n测试验证码验证逻辑...")
            print(f"提交的验证码: '{captcha_code}'")
            print(f"Session中的验证码: '{sess.get('captcha', '')}'")
            print(f"验证码比较结果: {captcha_code.upper() == sess.get('captcha', '').upper()}")

if __name__ == '__main__':
    test_captcha()
