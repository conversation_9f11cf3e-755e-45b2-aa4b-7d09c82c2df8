{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-shopping-cart"></i> 我的订单</h2>
</div>

<!-- 订单类型和状态筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">订单类型</label>
                <div class="btn-group d-block" role="group">
                    <a href="{{ url_for('orders.my_orders', type='buy', status=status_filter) }}" 
                       class="btn {% if order_type == 'buy' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        我买的
                    </a>
                    <a href="{{ url_for('orders.my_orders', type='sell', status=status_filter) }}" 
                       class="btn {% if order_type == 'sell' %}btn-success{% else %}btn-outline-success{% endif %}">
                        我卖的
                    </a>
                </div>
            </div>
            <div class="col-md-6">
                <label class="form-label">订单状态</label>
                <div class="btn-group d-block" role="group">
                    <a href="{{ url_for('orders.my_orders', type=order_type, status='all') }}" 
                       class="btn btn-sm {% if status_filter == 'all' %}btn-secondary{% else %}btn-outline-secondary{% endif %}">
                        全部
                    </a>
                    <a href="{{ url_for('orders.my_orders', type=order_type, status='pending') }}" 
                       class="btn btn-sm {% if status_filter == 'pending' %}btn-warning{% else %}btn-outline-warning{% endif %}">
                        待付款
                    </a>
                    <a href="{{ url_for('orders.my_orders', type=order_type, status='paid') }}" 
                       class="btn btn-sm {% if status_filter == 'paid' %}btn-info{% else %}btn-outline-info{% endif %}">
                        已付款
                    </a>
                    <a href="{{ url_for('orders.my_orders', type=order_type, status='shipped') }}" 
                       class="btn btn-sm {% if status_filter == 'shipped' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        已发货
                    </a>
                    <a href="{{ url_for('orders.my_orders', type=order_type, status='completed') }}" 
                       class="btn btn-sm {% if status_filter == 'completed' %}btn-success{% else %}btn-outline-success{% endif %}">
                        已完成
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 订单列表 -->
{% if orders.items %}
<div class="row">
    {% for order in orders.items %}
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <strong>订单号: {{ order.order_no }}</strong>
                    <span class="ms-3 text-muted">{{ moment(order.created_at).format('YYYY-MM-DD HH:mm') }}</span>
                </div>
                <div>
                    {% if order.status.value == 'pending' %}
                    <span class="badge bg-warning">待付款</span>
                    {% elif order.status.value == 'paid' %}
                    <span class="badge bg-info">已付款</span>
                    {% elif order.status.value == 'shipped' %}
                    <span class="badge bg-primary">已发货</span>
                    {% elif order.status.value == 'delivered' %}
                    <span class="badge bg-success">已送达</span>
                    {% elif order.status.value == 'completed' %}
                    <span class="badge bg-success">已完成</span>
                    {% elif order.status.value == 'cancelled' %}
                    <span class="badge bg-secondary">已取消</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <img src="{{ order.product.get_main_image_url() }}"
                             class="img-fluid rounded" alt="{{ order.product.title }}"
                             style="height: 100px; object-fit: cover;">
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">
                            <a href="{{ url_for('products.detail', id=order.product.id) }}" class="text-decoration-none">
                                {{ order.product.title }}
                            </a>
                        </h6>
                        <p class="text-muted mb-2">数量: {{ order.quantity }}</p>
                        <p class="text-muted mb-0">
                            {% if order_type == 'buy' %}
                            卖家: {{ order.seller.nickname or order.seller.username }}
                            {% else %}
                            买家: {{ order.buyer.nickname or order.buyer.username }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="h5 text-danger mb-0">¥{{ "%.2f"|format(order.total_amount) }}</div>
                        <small class="text-muted">总金额</small>
                    </div>
                    <div class="col-md-2 text-end">
                        <div class="btn-group-vertical btn-group-sm">
                            <a href="{{ url_for('orders.detail', id=order.id) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>
                            
                            {% if order_type == 'buy' %}
                                {% if order.status.value == 'pending' %}
                                <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder({{ order.id }})">
                                    <i class="fas fa-times"></i> 取消订单
                                </button>
                                {% elif order.status.value == 'completed' and not order.review %}
                                <a href="{{ url_for('orders.review', id=order.id) }}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-star"></i> 评价
                                </a>
                                {% endif %}
                            {% else %}
                                {% if order.status.value in ['paid', 'shipped'] %}
                                <a href="{{ url_for('orders.update', id=order.id) }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-edit"></i> 更新状态
                                </a>
                                {% endif %}
                            {% endif %}
                            
                            <a href="{{ url_for('orders.messages', product_id=order.product.id) }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-comment"></i> 消息
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 订单备注 -->
                {% if order.buyer_note or order.seller_note %}
                <hr>
                <div class="row">
                    {% if order.buyer_note %}
                    <div class="col-md-6">
                        <small class="text-muted">买家留言:</small>
                        <p class="small">{{ order.buyer_note }}</p>
                    </div>
                    {% endif %}
                    {% if order.seller_note %}
                    <div class="col-md-6">
                        <small class="text-muted">卖家备注:</small>
                        <p class="small">{{ order.seller_note }}</p>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 分页 -->
{% if orders.pages > 1 %}
<nav aria-label="订单分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if orders.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('orders.my_orders', page=orders.prev_num, type=order_type, status=status_filter) }}">
                上一页
            </a>
        </li>
        {% endif %}
        
        {% for page_num in orders.iter_pages() %}
            {% if page_num %}
                {% if page_num != orders.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('orders.my_orders', page=page_num, type=order_type, status=status_filter) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if orders.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('orders.my_orders', page=orders.next_num, type=order_type, status=status_filter) }}">
                下一页
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- 空状态 -->
<div class="empty-state">
    <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
    <h4 class="text-muted">
        {% if order_type == 'buy' %}
        还没有购买记录
        {% else %}
        还没有销售记录
        {% endif %}
    </h4>
    <p class="text-muted">
        {% if order_type == 'buy' %}
        去逛逛，找找心仪的商品吧！
        {% else %}
        发布商品开始销售吧！
        {% endif %}
    </p>
    {% if order_type == 'buy' %}
    <a href="{{ url_for('main.search') }}" class="btn btn-primary">
        <i class="fas fa-search"></i> 去购物
    </a>
    {% else %}
    <a href="{{ url_for('products.publish') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 发布商品
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function cancelOrder(orderId) {
    if (confirm('确定要取消这个订单吗？')) {
        $.ajax({
            url: '/orders/cancel/' + orderId,
            method: 'POST',
            success: function(response) {
                location.reload();
            },
            error: function() {
                alert('取消失败，请重试');
            }
        });
    }
}
</script>
{% endblock %}
