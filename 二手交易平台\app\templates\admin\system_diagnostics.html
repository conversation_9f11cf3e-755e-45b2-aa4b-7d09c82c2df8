{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-stethoscope"></i> 系统诊断</h2>
        <div class="btn-group">
            <button class="btn btn-primary" onclick="runAllDiagnostics()">
                <i class="fas fa-play"></i> 运行全部诊断
            </button>
            <button class="btn btn-outline-secondary" onclick="refreshDiagnostics()">
                <i class="fas fa-sync"></i> 刷新
            </button>
        </div>
    </div>

    <!-- 诊断状态概览 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">数据一致性</h5>
                    <div class="h2 mb-0" id="consistency-status">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <small class="text-muted">检查中...</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">性能状态</h5>
                    <div class="h2 mb-0" id="performance-status">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <small class="text-muted">检查中...</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">系统健康</h5>
                    <div class="h2 mb-0" id="system-health">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <small class="text-muted">检查中...</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">在线用户</h5>
                    <div class="h2 mb-0" id="online-users-count">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <small class="text-muted">统计中...</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细诊断结果 -->
    <div class="row">
        <!-- 数据一致性检查 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-check-double"></i> 数据一致性检查
                        <span class="badge bg-secondary ms-2" id="consistency-badge">等待中</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="consistency-results">
                        <div class="text-center text-muted">
                            <i class="fas fa-clock"></i> 等待检查...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能监控 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tachometer-alt"></i> 性能监控
                        <span class="badge bg-secondary ms-2" id="performance-badge">等待中</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="performance-results">
                        <div class="text-center text-muted">
                            <i class="fas fa-clock"></i> 等待检查...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统状态详情 -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-server"></i> 系统状态详情
                        <span class="badge bg-secondary ms-2" id="system-badge">等待中</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="system-details">
                        <div class="text-center text-muted">
                            <i class="fas fa-clock"></i> 等待检查...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 诊断日志 -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> 诊断日志
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearDiagnosticLog()">
                            <i class="fas fa-trash"></i> 清空日志
                        </button>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="diagnostic-log" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em;">
                        <!-- 诊断日志将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let diagnosticLog = [];

function logMessage(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    diagnosticLog.push(logEntry);
    
    const logElement = document.getElementById('diagnostic-log');
    const messageClass = type === 'error' ? 'text-danger' : type === 'warning' ? 'text-warning' : 'text-info';
    logElement.innerHTML += `<div class="${messageClass}">${logEntry}</div>`;
    logElement.scrollTop = logElement.scrollHeight;
}

function clearDiagnosticLog() {
    diagnosticLog = [];
    document.getElementById('diagnostic-log').innerHTML = '';
    logMessage('诊断日志已清空');
}

function updateStatusBadge(elementId, status, text) {
    const badge = document.getElementById(elementId);
    badge.className = `badge ms-2 ${status === 'success' ? 'bg-success' : status === 'error' ? 'bg-danger' : status === 'warning' ? 'bg-warning' : 'bg-secondary'}`;
    badge.textContent = text;
}

function runDataConsistencyCheck() {
    logMessage('开始数据一致性检查...');
    updateStatusBadge('consistency-badge', 'info', '检查中');
    
    fetch('/admin_panel/api/data_consistency_check')
        .then(response => response.json())
        .then(data => {
            const summary = data.summary;
            const passed = summary.passed;
            const failed = summary.failed;
            const warnings = summary.warnings;
            
            // 更新状态显示
            document.getElementById('consistency-status').innerHTML = 
                `<span class="text-success">${passed}</span>/<span class="text-danger">${failed}</span>/<span class="text-warning">${warnings}</span>`;
            
            // 更新徽章
            const status = failed > 0 ? 'error' : warnings > 0 ? 'warning' : 'success';
            const statusText = failed > 0 ? '有问题' : warnings > 0 ? '有警告' : '正常';
            updateStatusBadge('consistency-badge', status, statusText);
            
            // 显示详细结果
            let resultsHtml = '<div class="row">';
            for (const [checkName, result] of Object.entries(data.checks)) {
                const statusIcon = result.status === 'passed' ? 'fa-check text-success' : 
                                 result.status === 'warning' ? 'fa-exclamation-triangle text-warning' : 
                                 'fa-times text-danger';
                
                resultsHtml += `
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas ${statusIcon} me-2"></i>
                            <span>${checkName}</span>
                        </div>
                        <small class="text-muted">${result.message}</small>
                    </div>
                `;
            }
            resultsHtml += '</div>';
            
            document.getElementById('consistency-results').innerHTML = resultsHtml;
            logMessage(`数据一致性检查完成: 通过${passed}, 失败${failed}, 警告${warnings}`);
        })
        .catch(error => {
            logMessage(`数据一致性检查失败: ${error}`, 'error');
            updateStatusBadge('consistency-badge', 'error', '检查失败');
            document.getElementById('consistency-status').innerHTML = '<span class="text-danger">错误</span>';
        });
}

function runPerformanceCheck() {
    logMessage('开始性能监控检查...');
    updateStatusBadge('performance-badge', 'info', '检查中');
    
    fetch('/admin_panel/api/performance_report')
        .then(response => response.json())
        .then(data => {
            const summary = data.performance_summary;
            
            // 更新性能状态显示
            const errorRate = (summary.overall_error_rate * 100).toFixed(1);
            document.getElementById('performance-status').innerHTML = 
                `<span class="${errorRate > 5 ? 'text-danger' : errorRate > 1 ? 'text-warning' : 'text-success'}">${errorRate}%</span>`;
            
            // 更新徽章
            const status = errorRate > 5 ? 'error' : errorRate > 1 ? 'warning' : 'success';
            const statusText = errorRate > 5 ? '性能差' : errorRate > 1 ? '一般' : '良好';
            updateStatusBadge('performance-badge', status, statusText);
            
            // 显示性能详情
            let performanceHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>总调用次数:</strong> ${summary.total_calls}<br>
                        <strong>错误次数:</strong> ${summary.total_errors}<br>
                        <strong>错误率:</strong> ${errorRate}%
                    </div>
                    <div class="col-md-6">
                        <strong>最慢函数:</strong> ${summary.slowest_function ? summary.slowest_function.name : 'N/A'}<br>
                        <strong>最多调用:</strong> ${summary.most_called_function ? summary.most_called_function.name : 'N/A'}
                    </div>
                </div>
            `;
            
            if (data.cache_stats) {
                performanceHtml += `
                    <hr>
                    <div class="row">
                        <div class="col-md-12">
                            <strong>缓存状态:</strong> 
                            活跃项目 ${data.cache_stats.active_items}, 
                            过期项目 ${data.cache_stats.expired_items}
                        </div>
                    </div>
                `;
            }
            
            document.getElementById('performance-results').innerHTML = performanceHtml;
            logMessage(`性能检查完成: 错误率${errorRate}%`);
        })
        .catch(error => {
            logMessage(`性能检查失败: ${error}`, 'error');
            updateStatusBadge('performance-badge', 'error', '检查失败');
            document.getElementById('performance-status').innerHTML = '<span class="text-danger">错误</span>';
        });
}

function runSystemStatusCheck() {
    logMessage('开始系统状态检查...');
    updateStatusBadge('system-badge', 'info', '检查中');
    
    fetch('/admin_panel/api/system_status')
        .then(response => response.json())
        .then(data => {
            // 更新在线用户数
            document.getElementById('online-users-count').textContent = data.online_users || 0;
            
            // 更新系统健康状态
            const healthScore = data.health_score ? data.health_score.overall_score : 0;
            document.getElementById('system-health').innerHTML = 
                `<span class="${healthScore > 80 ? 'text-success' : healthScore > 60 ? 'text-warning' : 'text-danger'}">${healthScore.toFixed(0)}</span>`;
            
            // 显示系统详情
            const systemHtml = `
                <div class="row">
                    <div class="col-md-3">
                        <strong>CPU使用率:</strong><br>
                        <span class="h5">${data.cpu_usage || 'N/A'}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>内存使用率:</strong><br>
                        <span class="h5">${data.memory_usage || 'N/A'}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>磁盘使用率:</strong><br>
                        <span class="h5">${data.disk_usage || 'N/A'}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>服务器状态:</strong><br>
                        <span class="h5 text-success">${data.server_status || 'N/A'}</span>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <strong>平台:</strong> ${data.platform || 'N/A'}<br>
                        <strong>主机名:</strong> ${data.hostname || 'N/A'}
                    </div>
                    <div class="col-md-6">
                        <strong>进程数:</strong> ${data.processes || 'N/A'}<br>
                        <strong>数据库状态:</strong> <span class="text-success">${data.db_status || 'N/A'}</span>
                    </div>
                </div>
            `;
            
            document.getElementById('system-details').innerHTML = systemHtml;
            updateStatusBadge('system-badge', 'success', '正常');
            logMessage('系统状态检查完成');
        })
        .catch(error => {
            logMessage(`系统状态检查失败: ${error}`, 'error');
            updateStatusBadge('system-badge', 'error', '检查失败');
            document.getElementById('system-health').innerHTML = '<span class="text-danger">错误</span>';
        });
}

function runAllDiagnostics() {
    logMessage('开始运行全部诊断检查...');
    runDataConsistencyCheck();
    runPerformanceCheck();
    runSystemStatusCheck();
}

function refreshDiagnostics() {
    logMessage('刷新诊断数据...');
    runAllDiagnostics();
}

// 页面加载完成后自动运行诊断
document.addEventListener('DOMContentLoaded', function() {
    logMessage('系统诊断页面已加载');
    setTimeout(runAllDiagnostics, 1000);
    
    // 每30秒自动刷新一次
    setInterval(runSystemStatusCheck, 30000);
});
</script>
{% endblock %}
