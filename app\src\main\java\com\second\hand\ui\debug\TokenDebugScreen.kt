package com.second.hand.ui.debug

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.second.hand.data.auth.TokenState
import com.second.hand.ui.auth.AuthViewModel
import com.second.hand.ui.components.TokenRefreshIndicator
import com.second.hand.ui.components.TokenHealthIndicator
import com.second.hand.ui.components.UserStateIndicator
import java.text.SimpleDateFormat
import java.util.*

/**
 * Token调试界面
 * 用于监控和测试Token管理功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TokenDebugScreen(
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val tokenState by authViewModel.getTokenState().collectAsState()
    val authUiState by authViewModel.uiState.collectAsState()
    val refreshStatus by authViewModel.getRefreshStatus().collectAsState()
    val refreshMessage by authViewModel.getRefreshUserMessage().collectAsState()
    val tokenHealthStatus = authViewModel.getTokenHealthStatus()
    val healthDescription = authViewModel.getTokenHealthDescription()
    val userState by authViewModel.getUserState().collectAsState()
    val currentUser by authViewModel.getCurrentUser().collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "🔐 Token 状态监控",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        // Token刷新状态指示器
        TokenRefreshIndicator(
            refreshStatus = refreshStatus,
            userMessage = refreshMessage,
            tokenHealthStatus = tokenHealthStatus,
            onDismiss = { authViewModel.clearRefreshMessage() }
        )

        // Token健康状态
        TokenHealthIndicator(
            tokenHealthStatus = tokenHealthStatus,
            healthDescription = healthDescription
        )

        // 用户状态指示器
        UserStateIndicator(
            userState = userState,
            showDetails = true
        )

        // 登录状态卡片
        LoginStatusCard(authUiState.isLoggedIn, authUiState.currentUser?.username)

        // Token状态卡片
        TokenStatusCard(tokenState)

        // 操作按钮
        ActionButtons(authViewModel)

        // Token详细信息
        TokenDetailsCard(tokenState)
    }
}

@Composable
private fun LoginStatusCard(isLoggedIn: Boolean, username: String?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isLoggedIn) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "登录状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = if (isLoggedIn) "✅ 已登录" else "❌ 未登录",
                style = MaterialTheme.typography.bodyLarge
            )
            if (isLoggedIn && username != null) {
                Text(
                    text = "用户: $username",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
private fun TokenStatusCard(tokenState: TokenState) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Token 状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            StatusRow("Access Token", tokenState.hasAccessToken)
            StatusRow("Refresh Token", tokenState.hasRefreshToken)
            StatusRow("Token 有效", tokenState.isValid)
            StatusRow("需要刷新", tokenState.shouldRefresh)
        }
    }
}

@Composable
private fun StatusRow(label: String, status: Boolean) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = if (status) "✅" else "❌",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
private fun ActionButtons(authViewModel: AuthViewModel) {
    val userState by authViewModel.getUserState().collectAsState()
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "操作",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { authViewModel.refreshTokenState() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("刷新状态")
                }
                
                Button(
                    onClick = { authViewModel.checkLoginStatus() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("检查登录")
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { authViewModel.manualRefreshToken() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("手动刷新Token")
                }

                Button(
                    onClick = { authViewModel.clearRefreshMessage() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("清除消息")
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { authViewModel.syncUserInfo() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("同步用户信息")
                }

                Button(
                    onClick = { /* TODO: 实现用户信息编辑 */ },
                    modifier = Modifier.weight(1f),
                    enabled = userState.isLoggedIn
                ) {
                    Text("编辑资料")
                }
            }
        }
    }
}

@Composable
private fun TokenDetailsCard(tokenState: TokenState) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Token 详细信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            if (tokenState.expiryTime > 0) {
                val expiryDate = Date(tokenState.expiryTime)
                val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                
                DetailRow("过期时间", formatter.format(expiryDate))
                DetailRow("剩余时间", formatRemainingTime(tokenState.remainingTime))
            } else {
                Text(
                    text = "无Token信息",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun DetailRow(label: String, value: String) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer(modifier = Modifier.height(4.dp))
    }
}

private fun formatRemainingTime(remainingTimeMs: Long): String {
    if (remainingTimeMs <= 0) return "已过期"
    
    val seconds = remainingTimeMs / 1000
    val minutes = seconds / 60
    val hours = minutes / 60
    val days = hours / 24
    
    return when {
        days > 0 -> "${days}天 ${hours % 24}小时"
        hours > 0 -> "${hours}小时 ${minutes % 60}分钟"
        minutes > 0 -> "${minutes}分钟 ${seconds % 60}秒"
        else -> "${seconds}秒"
    }
}
