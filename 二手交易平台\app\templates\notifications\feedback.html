{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-comment-dots"></i> 意见反馈</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.type.label(class="form-label") }}
                        {{ form.type(class="form-select" + (" is-invalid" if form.type.errors else "")) }}
                        {% if form.type.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}
                        {{ form.content(class="form-control" + (" is-invalid" if form.content.errors else ""), rows="8") }}
                        {% if form.content.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.content.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">请详细描述您遇到的问题或建议</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('notifications.my_feedbacks') }}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> 我的反馈
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}