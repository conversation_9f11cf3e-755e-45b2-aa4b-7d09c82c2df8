{"info": {"name": "二手交易平台移动端API", "description": "二手交易平台移动端API完整测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000/api/v1", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}, {"key": "orderId", "value": "", "type": "string"}], "item": [{"name": "认证API", "item": [{"name": "发送邮箱验证码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"type\": \"register\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/send-email-code", "host": ["{{baseUrl}}"], "path": ["auth", "send-email-code"]}}}, {"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Test123456\",\n  \"nickname\": \"测试用户\",\n  \"emailCode\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success) {", "        pm.collectionVariables.set('token', response.data.token);", "        pm.collectionVariables.set('refreshToken', response.data.refreshToken);", "        pm.collectionVariables.set('userId', response.data.user.id);", "    }", "}"]}}]}, {"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"Test123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        pm.collectionVariables.set('token', response.data.token);", "        pm.collectionVariables.set('refreshToken', response.data.refreshToken);", "        pm.collectionVariables.set('userId', response.data.user.id);", "    }", "}"]}}]}, {"name": "获取用户信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/auth/profile", "host": ["{{baseUrl}}"], "path": ["auth", "profile"]}}}, {"name": "更新用户信息", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nickname\": \"更新后的昵称\",\n  \"bio\": \"这是我的个人简介\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/profile", "host": ["{{baseUrl}}"], "path": ["auth", "profile"]}}}, {"name": "刷新令牌", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        pm.collectionVariables.set('token', response.data.token);", "    }", "}"]}}]}, {"name": "用户登出", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}}]}, {"name": "分类API", "item": [{"name": "获取分类列表", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/categories", "host": ["{{baseUrl}}"], "path": ["categories"]}}}, {"name": "获取分类详情", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/categories/1", "host": ["{{baseUrl}}"], "path": ["categories", "1"]}}}]}, {"name": "商品API", "item": [{"name": "获取商品列表", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/products?page=1&limit=20&status=active", "host": ["{{baseUrl}}"], "path": ["products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "status", "value": "active"}]}}}, {"name": "发布商品", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"测试商品\",\n  \"description\": \"这是一个测试商品的描述\",\n  \"price\": 99.99,\n  \"originalPrice\": 199.99,\n  \"condition\": \"九成新\",\n  \"location\": \"北京市\",\n  \"quantity\": 1,\n  \"categoryId\": 1,\n  \"images\": [\n    {\n      \"data\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==\",\n      \"isMain\": true\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/products", "host": ["{{baseUrl}}"], "path": ["products"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success) {", "        pm.collectionVariables.set('productId', response.data.id);", "    }", "}"]}}]}, {"name": "获取商品详情", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}}, {"name": "收藏商品", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/products/{{productId}}/favorite", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "favorite"]}}}, {"name": "获取我的商品", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/products/my?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["products", "my"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "获取我的收藏", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/products/favorites?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["products", "favorites"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}]}