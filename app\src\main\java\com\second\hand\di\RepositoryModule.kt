package com.second.hand.di

import com.second.hand.data.api.ApiService
import com.second.hand.data.auth.TokenManager
import com.second.hand.data.repository.AuthRepository
import com.second.hand.data.repository.TestRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Repository模块依赖注入配置
 * 提供Repository相关依赖
 */
@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    @Provides
    @Singleton
    fun provideTestRepository(apiService: ApiService): TestRepository {
        return TestRepository(apiService)
    }

    @Provides
    @Singleton
    fun provideAuthRepository(
        apiService: ApiService,
        tokenManager: TokenManager
    ): AuthRepository {
        return AuthRepository(apiService, tokenManager)
    }
}
