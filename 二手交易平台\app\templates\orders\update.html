{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- 页面标题 -->
                <div class="mb-4">
                    <h2><i class="fas fa-edit text-primary"></i> 更新订单状态</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('orders.my_orders') }}">我的订单</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('orders.detail', id=order.id) }}">订单详情</a></li>
                            <li class="breadcrumb-item active">更新状态</li>
                        </ol>
                    </nav>
                </div>

                <!-- 订单信息卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-receipt"></i> 订单信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>订单号：</strong></td>
                                        <td><code>{{ order.order_no }}</code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建时间：</strong></td>
                                        <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>订单金额：</strong></td>
                                        <td><span class="text-danger fw-bold">¥{{ "%.2f"|format(order.total_amount) }}</span></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>买家：</strong></td>
                                        <td>{{ order.buyer.nickname or order.buyer.username }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>当前状态：</strong></td>
                                        <td>
                                            {% if order.status.value == 'pending' %}
                                                <span class="badge bg-warning">待付款</span>
                                            {% elif order.status.value == 'paid' %}
                                                <span class="badge bg-info">已付款</span>
                                            {% elif order.status.value == 'shipped' %}
                                                <span class="badge bg-primary">已发货</span>
                                            {% elif order.status.value == 'delivered' %}
                                                <span class="badge bg-success">已送达</span>
                                            {% elif order.status.value == 'completed' %}
                                                <span class="badge bg-success">已完成</span>
                                            {% elif order.status.value == 'cancelled' %}
                                                <span class="badge bg-danger">已取消</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-box"></i> 商品信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <img src="{{ order.product.get_main_image_url() }}"
                                     class="img-fluid rounded" alt="{{ order.product.title }}"
                                     style="height: 150px; object-fit: cover;">
                            </div>
                            <div class="col-md-9">
                                <h5>{{ order.product.title }}</h5>
                                <p class="text-muted">{{ order.product.description[:200] }}{% if order.product.description|length > 200 %}...{% endif %}</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">单价：</small>
                                        <span class="fw-bold">¥{{ "%.2f"|format(order.unit_price) }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">数量：</small>
                                        <span class="fw-bold">{{ order.quantity }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 更新状态表单 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-sync-alt"></i> 更新订单状态</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <div class="mb-3">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.status.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> 
                                    请根据实际情况选择合适的订单状态
                                </div>
                            </div>

                            <div class="mb-3">
                                {{ form.seller_note.label(class="form-label") }}
                                {{ form.seller_note(class="form-control" + (" is-invalid" if form.seller_note.errors else ""), rows="4", placeholder="可以添加一些备注信息，如物流单号、发货时间等...") }}
                                {% if form.seller_note.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.seller_note.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> 
                                    备注信息将显示给买家，可以包含物流信息等
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('orders.detail', id=order.id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> 返回订单详情
                                </a>
                                <div>
                                    <a href="{{ url_for('orders.my_orders', type='sell') }}" class="btn btn-outline-info me-2">
                                        <i class="fas fa-list"></i> 我的销售
                                    </a>
                                    {{ form.submit(class="btn btn-primary") }}
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 状态说明 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-question-circle"></i> 状态说明</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <span class="badge bg-warning me-2">待付款</span>
                                        买家尚未付款
                                    </li>
                                    <li class="mb-2">
                                        <span class="badge bg-info me-2">已付款</span>
                                        买家已付款，等待发货
                                    </li>
                                    <li class="mb-2">
                                        <span class="badge bg-primary me-2">已发货</span>
                                        商品已发出，等待买家确认收货
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <span class="badge bg-success me-2">已送达</span>
                                        商品已送达，等待买家确认
                                    </li>
                                    <li class="mb-2">
                                        <span class="badge bg-success me-2">已完成</span>
                                        交易已完成
                                    </li>
                                    <li class="mb-2">
                                        <span class="badge bg-danger me-2">已取消</span>
                                        订单已取消
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.badge {
    font-size: 0.875em;
}

.form-text {
    font-size: 0.875em;
}

.btn {
    border-radius: 0.375rem;
}

.btn i {
    margin-right: 0.25rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 状态选择提示
    const statusSelect = document.getElementById('status');
    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            const selectedValue = this.value;
            const noteField = document.getElementById('seller_note');
            
            // 根据选择的状态给出提示
            switch(selectedValue) {
                case 'shipped':
                    noteField.placeholder = '请填写物流公司和快递单号，方便买家查询...';
                    break;
                case 'delivered':
                    noteField.placeholder = '商品已送达，请买家确认收货...';
                    break;
                case 'cancelled':
                    noteField.placeholder = '请说明取消原因...';
                    break;
                default:
                    noteField.placeholder = '可以添加一些备注信息...';
            }
        });
    }
});
</script>
{% endblock %}
