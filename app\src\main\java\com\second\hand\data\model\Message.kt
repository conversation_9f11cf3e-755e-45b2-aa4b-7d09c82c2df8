package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 消息数据模型
 * 对应Flask后端的Message模型
 */
@Entity(tableName = "messages")
data class Message(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    // 外键
    @SerializedName("sender_id")
    var senderId: Int = 0,

    @SerializedName("receiver_id")
    var receiverId: Int = 0,

    @SerializedName("product_id")
    var productId: Int? = null,

    // 消息内容
    @SerializedName("content")
    var content: String = "",

    @SerializedName("is_read")
    var isRead: Boolean = false,

    // 逻辑删除字段
    @SerializedName("deleted_by_sender")
    var deletedBySender: Boolean = false,

    @SerializedName("deleted_by_receiver")
    var deletedByReceiver: Boolean = false,

    @SerializedName("sender_deleted_at")
    var senderDeletedAt: String? = null,

    @SerializedName("receiver_deleted_at")
    var receiverDeletedAt: String? = null,

    // 时间戳
    @SerializedName("created_at")
    var createdAt: String = "",
    
    // 关联数据（API返回时可能包含，不存储到数据库）
    @Ignore
    @SerializedName("sender")
    val sender: User? = null,

    @Ignore
    @SerializedName("receiver")
    val receiver: User? = null,

    @Ignore
    @SerializedName("product")
    val product: Product? = null
) {
    /**
     * 检查消息是否被指定用户删除
     */
    fun isDeletedByUser(userId: Int): Boolean {
        return when (userId) {
            senderId -> deletedBySender
            receiverId -> deletedByReceiver
            else -> false
        }
    }
    
    /**
     * 获取用户删除消息的时间
     */
    fun getDeletedAtForUser(userId: Int): String? {
        return when (userId) {
            senderId -> senderDeletedAt
            receiverId -> receiverDeletedAt
            else -> null
        }
    }
    
    /**
     * 检查是否为发送的消息
     */
    fun isSentByUser(userId: Int): Boolean {
        return senderId == userId
    }
    
    /**
     * 获取对话对方的用户ID
     */
    fun getOtherUserId(currentUserId: Int): Int {
        return if (senderId == currentUserId) receiverId else senderId
    }
    
    /**
     * 获取对话对方的用户信息
     */
    fun getOtherUser(currentUserId: Int): User? {
        return if (senderId == currentUserId) receiver else sender
    }
}
