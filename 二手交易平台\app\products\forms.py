#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品管理表单
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed, FileRequired
from wtforms import StringField, TextAreaField, DecimalField, SelectField, SubmitField, MultipleFileField, IntegerField
from wtforms.validators import DataRequired, Length, NumberRange, ValidationError
from app.models import Category

class ProductForm(FlaskForm):
    """商品发布/编辑表单"""
    title = StringField('商品标题', validators=[
        DataRequired(message='请输入商品标题'),
        Length(min=5, max=200, message='标题长度应在5-200个字符之间')
    ])
    
    description = TextAreaField('商品描述', validators=[
        DataRequired(message='请输入商品描述'),
        Length(min=10, max=2000, message='描述长度应在10-2000个字符之间')
    ])
    
    price = DecimalField('价格', validators=[
        DataRequired(message='请输入价格'),
        NumberRange(min=0.01, max=999999.99, message='价格应在0.01-999999.99之间')
    ], places=2)
    
    original_price = DecimalField('原价', validators=[
        NumberRange(min=0.01, max=999999.99, message='原价应在0.01-999999.99之间')
    ], places=2)
    
    category_id = SelectField('商品分类', coerce=int, validators=[
        DataRequired(message='请选择商品分类')
    ])
    
    condition = SelectField('新旧程度', choices=[
        ('全新', '全新'),
        ('几乎全新', '几乎全新'),
        ('轻微使用痕迹', '轻微使用痕迹'),
        ('明显使用痕迹', '明显使用痕迹'),
        ('重度使用痕迹', '重度使用痕迹')
    ], validators=[DataRequired(message='请选择新旧程度')])
    
    location = StringField('所在地', validators=[
        Length(max=200, message='所在地长度不能超过200个字符')
    ])

    quantity = IntegerField('商品数量', validators=[
        DataRequired(message='请输入商品数量'),
        NumberRange(min=1, max=9999, message='商品数量应在1-9999之间')
    ], default=1)

    images = MultipleFileField('商品图片', validators=[
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], message='只支持jpg、jpeg、png、gif格式的图片')
    ])
    
    submit = SubmitField('发布商品')
    
    def __init__(self, *args, **kwargs):
        super(ProductForm, self).__init__(*args, **kwargs)
        # 动态加载分类选项
        self.category_id.choices = [(c.id, c.name) for c in Category.query.filter_by(is_active=True).all()]
    
    def validate_original_price(self, original_price):
        """验证原价不能低于现价"""
        if original_price.data and self.price.data:
            if original_price.data < self.price.data:
                raise ValidationError('原价不能低于现价')

class EditProductForm(ProductForm):
    """编辑商品表单"""
    submit = SubmitField('保存修改')
    
    def __init__(self, *args, **kwargs):
        super(EditProductForm, self).__init__(*args, **kwargs)
        # 编辑时图片不是必需的
        self.images.validators = [
            FileAllowed(['jpg', 'jpeg', 'png', 'gif'], message='只支持jpg、jpeg、png、gif格式的图片')
        ]

class CategoryForm(FlaskForm):
    """分类管理表单"""
    name = StringField('分类名称', validators=[
        DataRequired(message='请输入分类名称'),
        Length(min=2, max=80, message='分类名称长度应在2-80个字符之间')
    ])
    
    description = TextAreaField('分类描述', validators=[
        Length(max=500, message='分类描述长度不能超过500个字符')
    ])
    
    icon = StringField('图标', validators=[
        Length(max=100, message='图标长度不能超过100个字符')
    ])
    
    submit = SubmitField('保存分类')
