package com.second.hand.ui.category

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.second.hand.ui.navigation.NavigationDestinations

/**
 * 分类页面Screen - 商品分类浏览
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryScreen(
    navController: NavController
) {
    var selectedCategory by remember { mutableStateOf<CategoryItem?>(null) }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = {
                Text(
                    text = if (selectedCategory == null) "商品分类" else selectedCategory!!.name,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                if (selectedCategory != null) {
                    IconButton(onClick = { selectedCategory = null }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            },
            actions = {
                IconButton(
                    onClick = {
                        navController.navigate(NavigationDestinations.SEARCH)
                    }
                ) {
                    Icon(Icons.Default.Search, contentDescription = "搜索")
                }
            }
        )

        // 主要内容
        if (selectedCategory == null) {
            // 显示分类列表
            CategoryListContent()
        } else {
            // 显示选中分类的商品
            CategoryProductsContent(
                category = selectedCategory!!,
                navController = navController
            )
        }
    }
}

@Composable
private fun CategoryListContent() {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(getAllCategories()) { category ->
            CategoryCard(
                category = category,
                onClick = { /* TODO: 处理分类点击 */ }
            )
        }
    }
}

@Composable
private fun CategoryProductsContent(
    category: CategoryItem,
    navController: NavController
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            Text(
                text = "共找到 ${category.productCount} 件商品",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // TODO: 这里应该显示该分类下的实际商品列表
        items(getProductsByCategory(category.id)) { product ->
            ProductListItem(
                product = product,
                onClick = {
                    navController.navigate(
                        NavigationDestinations.productDetail(product.id)
                    )
                }
            )
        }
    }
}

@Composable
private fun CategoryCard(
    category: CategoryItem,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = category.icon,
                style = MaterialTheme.typography.displaySmall
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = category.name,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "${category.productCount} 件商品",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ProductListItem(
    product: CategoryProduct,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 商品图片占位符
            Card(
                modifier = Modifier.size(80.dp),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "📷",
                        style = MaterialTheme.typography.headlineMedium
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = product.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = product.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "¥${product.price}",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Bold
                    )

                    Text(
                        text = product.location,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

// 数据类
data class CategoryItem(
    val id: String,
    val name: String,
    val icon: String,
    val productCount: Int
)

data class CategoryProduct(
    val id: String,
    val name: String,
    val description: String,
    val price: String,
    val location: String,
    val imageUrl: String? = null
)

// 示例数据
private fun getAllCategories() = listOf(
    CategoryItem("1", "数码产品", "📱", 1234),
    CategoryItem("2", "服装配饰", "👕", 856),
    CategoryItem("3", "家居用品", "🏠", 642),
    CategoryItem("4", "图书文具", "📚", 389),
    CategoryItem("5", "运动户外", "⚽", 567),
    CategoryItem("6", "美妆护肤", "💄", 423),
    CategoryItem("7", "母婴用品", "🍼", 298),
    CategoryItem("8", "食品饮料", "🍎", 156),
    CategoryItem("9", "汽车用品", "🚗", 234),
    CategoryItem("10", "宠物用品", "🐕", 178),
    CategoryItem("11", "乐器设备", "🎸", 89),
    CategoryItem("12", "其他物品", "📦", 345)
)

private fun getProductsByCategory(categoryId: String) = listOf(
    CategoryProduct(
        id = "1",
        name = "iPhone 14 Pro Max 256GB",
        description = "9成新，无磕碰，原装配件齐全",
        price = "7999",
        location = "北京朝阳区"
    ),
    CategoryProduct(
        id = "2",
        name = "MacBook Pro 13寸 M2芯片",
        description = "2022款，几乎全新，保修期内",
        price = "9999",
        location = "上海浦东新区"
    ),
    CategoryProduct(
        id = "3",
        name = "iPad Air 第5代 64GB",
        description = "轻微使用痕迹，功能完好",
        price = "3999",
        location = "广州天河区"
    ),
    CategoryProduct(
        id = "4",
        name = "AirPods Pro 第2代",
        description = "全新未拆封，正品保证",
        price = "1899",
        location = "深圳南山区"
    ),
    CategoryProduct(
        id = "5",
        name = "Apple Watch Series 8",
        description = "45mm GPS版本，8成新",
        price = "2599",
        location = "杭州西湖区"
    )
)
