package com.second.hand.ui.product

import androidx.lifecycle.viewModelScope
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.model.Category
import com.second.hand.data.model.Product
import com.second.hand.data.repository.CategoryRepository
import com.second.hand.data.repository.ProductRepository
import com.second.hand.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 商品列表ViewModel
 * 管理商品列表的数据和状态
 */
@HiltViewModel
class ProductListViewModel @Inject constructor(
    private val productRepository: ProductRepository,
    private val categoryRepository: CategoryRepository
) : BaseViewModel() {

    // UI状态
    private val _uiState = MutableStateFlow(ProductListUiState())
    val uiState: StateFlow<ProductListUiState> = _uiState.asStateFlow()

    // 商品列表
    private val _products = MutableStateFlow<List<Product>>(emptyList())
    val products: StateFlow<List<Product>> = _products.asStateFlow()

    // 分类列表
    private val _categories = MutableStateFlow<List<Category>>(emptyList())
    val categories: StateFlow<List<Category>> = _categories.asStateFlow()

    // 搜索状态
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    // 筛选状态
    private val _filterState = MutableStateFlow(ProductFilterState())
    val filterState: StateFlow<ProductFilterState> = _filterState.asStateFlow()

    // 分页状态
    private var currentPage = 1
    private var hasMorePages = true
    private var isLoadingMore = false

    init {
        loadCategories()
        loadProducts()
    }

    /**
     * 加载商品列表
     */
    fun loadProducts(refresh: Boolean = false) {
        if (refresh) {
            currentPage = 1
            hasMorePages = true
            _products.value = emptyList()
        }

        if (isLoadingMore) return

        launchSafely {
            if (refresh) {
                setLoading(true)
            } else {
                isLoadingMore = true
                _uiState.value = _uiState.value.copy(isLoadingMore = true)
            }

            clearError()

            val filterState = _filterState.value
            val searchQuery = _searchQuery.value

            val result = if (searchQuery.isNotBlank()) {
                productRepository.searchProducts(
                    query = searchQuery,
                    page = currentPage,
                    limit = 20,
                    categoryId = filterState.selectedCategoryId,
                    minPrice = filterState.minPrice,
                    maxPrice = filterState.maxPrice,
                    condition = filterState.condition,
                    location = filterState.location,
                    sort = filterState.sortBy,
                    order = filterState.sortOrder
                )
            } else {
                productRepository.getProducts(
                    page = currentPage,
                    limit = 20,
                    categoryId = filterState.selectedCategoryId,
                    status = "active",
                    minPrice = filterState.minPrice,
                    maxPrice = filterState.maxPrice,
                    condition = filterState.condition,
                    location = filterState.location,
                    sort = filterState.sortBy,
                    order = filterState.sortOrder
                )
            }

            when (result) {
                is NetworkResult.Success -> {
                    val pagedResponse = result.data
                    val newProducts = pagedResponse.items

                    if (refresh) {
                        _products.value = newProducts
                    } else {
                        _products.value = _products.value + newProducts
                    }

                    hasMorePages = pagedResponse.hasNext
                    currentPage++

                    _uiState.value = _uiState.value.copy(
                        isEmpty = _products.value.isEmpty(),
                        hasMorePages = hasMorePages
                    )
                }
                is NetworkResult.Error -> {
                    setError(result.message)
                }
                is NetworkResult.Loading -> {
                    // 已在上面处理
                }
            }

            setLoading(false)
            isLoadingMore = false
            _uiState.value = _uiState.value.copy(isLoadingMore = false)
        }
    }

    /**
     * 加载更多商品
     */
    fun loadMoreProducts() {
        if (hasMorePages && !isLoadingMore) {
            loadProducts(refresh = false)
        }
    }

    /**
     * 刷新商品列表
     */
    fun refreshProducts() {
        loadProducts(refresh = true)
    }

    /**
     * 搜索商品
     */
    fun searchProducts(query: String) {
        _searchQuery.value = query
        loadProducts(refresh = true)
    }

    /**
     * 清除搜索
     */
    fun clearSearch() {
        _searchQuery.value = ""
        loadProducts(refresh = true)
    }

    /**
     * 应用筛选
     */
    fun applyFilter(filterState: ProductFilterState) {
        _filterState.value = filterState
        loadProducts(refresh = true)
    }

    /**
     * 清除筛选
     */
    fun clearFilter() {
        _filterState.value = ProductFilterState()
        loadProducts(refresh = true)
    }

    /**
     * 收藏/取消收藏商品
     */
    fun toggleFavorite(productId: Int) {
        launchSafely {
            when (val result = productRepository.toggleFavorite(productId)) {
                is NetworkResult.Success -> {
                    val isFavorited = result.data
                    // 更新本地商品列表中的收藏状态
                    _products.value = _products.value.map { product ->
                        if (product.id == productId) {
                            product.copy(isFavorited = isFavorited)
                        } else {
                            product
                        }
                    }
                }
                is NetworkResult.Error -> {
                    setError(result.message)
                }
                is NetworkResult.Loading -> {
                    // 不处理
                }
            }
        }
    }

    /**
     * 加载分类列表
     */
    private fun loadCategories() {
        launchSafely {
            when (val result = categoryRepository.getCategories()) {
                is NetworkResult.Success -> {
                    _categories.value = result.data
                }
                is NetworkResult.Error -> {
                    // 分类加载失败不影响主要功能
                    println("加载分类失败: ${result.message}")
                }
                is NetworkResult.Loading -> {
                    // 不处理
                }
            }
        }
    }

    /**
     * 增加商品浏览量
     */
    fun incrementViewCount(productId: Int) {
        launchSafely {
            productRepository.incrementViewCount(productId)
        }
    }
}

/**
 * 商品列表UI状态
 */
data class ProductListUiState(
    val isEmpty: Boolean = false,
    val isLoadingMore: Boolean = false,
    val hasMorePages: Boolean = true
)

/**
 * 商品筛选状态
 */
data class ProductFilterState(
    val selectedCategoryId: Int? = null,
    val minPrice: Double? = null,
    val maxPrice: Double? = null,
    val condition: String? = null,
    val location: String? = null,
    val sortBy: String = "created_at",
    val sortOrder: String = "desc"
)
