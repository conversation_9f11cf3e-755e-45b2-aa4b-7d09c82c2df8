package com.second.hand.data.api

import com.second.hand.data.model.*
import com.second.hand.data.model.auth.*
import retrofit2.Response
import retrofit2.http.*

/**
 * API服务接口定义
 * 定义所有与Flask后端通信的API接口
 * 对应Flask后端的 /api/v1/ 接口
 */
interface ApiService {

    // ==================== 认证模块 ====================

    /**
     * 用户注册
     * POST /api/v1/auth/register
     */
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequest): Response<BaseResponse<LoginResponse>>

    /**
     * 用户登录
     * POST /api/v1/auth/login
     */
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<BaseResponse<LoginResponse>>

    /**
     * 用户登出
     * POST /api/v1/auth/logout
     */
    @POST("auth/logout")
    suspend fun logout(): Response<BaseResponse<Any>>

    /**
     * 获取用户信息
     * GET /api/v1/auth/profile
     */
    @GET("auth/profile")
    suspend fun getProfile(): Response<BaseResponse<User>>

    /**
     * 更新用户信息
     * PUT /api/v1/auth/profile
     */
    @PUT("auth/profile")
    suspend fun updateProfile(@Body user: User): Response<BaseResponse<User>>

    /**
     * 刷新Token
     * POST /api/v1/auth/refresh
     */
    @POST("auth/refresh")
    suspend fun refreshToken(): Response<BaseResponse<LoginResponse>>

    // ==================== 商品模块 ====================

    /**
     * 获取商品列表
     * GET /api/v1/products
     */
    @GET("products")
    suspend fun getProducts(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20,
        @Query("category") categoryId: Int? = null,
        @Query("status") status: String = "active",
        @Query("search") search: String? = null,
        @Query("sort") sort: String = "created_at",
        @Query("order") order: String = "desc",
        @Query("minPrice") minPrice: Double? = null,
        @Query("maxPrice") maxPrice: Double? = null,
        @Query("condition") condition: String? = null,
        @Query("location") location: String? = null
    ): Response<BaseResponse<PagedResponse<Product>>>

    /**
     * 获取商品详情
     * GET /api/v1/products/{id}
     */
    @GET("products/{id}")
    suspend fun getProduct(@Path("id") id: Int): Response<BaseResponse<Product>>

    /**
     * 发布商品
     * POST /api/v1/products
     */
    @POST("products")
    suspend fun createProduct(@Body product: Product): Response<BaseResponse<Product>>

    /**
     * 更新商品
     * PUT /api/v1/products/{id}
     */
    @PUT("products/{id}")
    suspend fun updateProduct(
        @Path("id") id: Int,
        @Body product: Product
    ): Response<BaseResponse<Product>>

    /**
     * 删除商品
     * DELETE /api/v1/products/{id}
     */
    @DELETE("products/{id}")
    suspend fun deleteProduct(@Path("id") id: Int): Response<BaseResponse<Any>>

    /**
     * 收藏/取消收藏商品
     * POST /api/v1/products/{id}/favorite
     */
    @POST("products/{id}/favorite")
    suspend fun toggleFavorite(@Path("id") id: Int): Response<BaseResponse<Map<String, Any>>>

    /**
     * 搜索商品
     * GET /api/v1/search
     */
    @GET("search")
    suspend fun searchProducts(
        @Query("q") query: String,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20,
        @Query("category") categoryId: Int? = null,
        @Query("minPrice") minPrice: Double? = null,
        @Query("maxPrice") maxPrice: Double? = null,
        @Query("condition") condition: String? = null,
        @Query("location") location: String? = null,
        @Query("sort") sort: String = "created_at",
        @Query("order") order: String = "desc"
    ): Response<BaseResponse<PagedResponse<Product>>>

    /**
     * 获取用户商品
     * GET /api/v1/users/{userId}/products
     */
    @GET("users/{userId}/products")
    suspend fun getUserProducts(
        @Path("userId") userId: Int,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20,
        @Query("status") status: String? = null
    ): Response<BaseResponse<PagedResponse<Product>>>

    /**
     * 增加商品浏览量
     * POST /api/v1/products/{id}/view
     */
    @POST("products/{id}/view")
    suspend fun incrementViewCount(@Path("id") id: Int): Response<BaseResponse<Unit>>

    // ==================== 分类模块 ====================

    /**
     * 获取分类列表
     * GET /api/v1/categories
     */
    @GET("categories")
    suspend fun getCategories(): Response<BaseResponse<List<Category>>>

    /**
     * 获取分类详情
     * GET /api/v1/categories/{id}
     */
    @GET("categories/{id}")
    suspend fun getCategory(@Path("id") id: Int): Response<BaseResponse<Category>>

    // ==================== 订单模块 ====================

    /**
     * 获取订单列表
     * GET /api/v1/orders
     */
    @GET("orders")
    suspend fun getOrders(
        @Query("page") page: Int = 1,
        @Query("per_page") perPage: Int = 10,
        @Query("status") status: String? = null
    ): Response<BaseResponse<PagedResponse<Order>>>

    /**
     * 获取订单详情
     * GET /api/v1/orders/{id}
     */
    @GET("orders/{id}")
    suspend fun getOrder(@Path("id") id: Int): Response<BaseResponse<Order>>

    /**
     * 创建订单
     * POST /api/v1/orders
     */
    @POST("orders")
    suspend fun createOrder(@Body order: Order): Response<BaseResponse<Order>>

    /**
     * 更新订单状态
     * PUT /api/v1/orders/{id}/status
     */
    @PUT("orders/{id}/status")
    suspend fun updateOrderStatus(
        @Path("id") id: Int,
        @Body statusUpdate: Map<String, String>
    ): Response<BaseResponse<Order>>

    // ==================== 消息模块 ====================

    /**
     * 获取对话列表
     * GET /api/v1/messages/conversations
     */
    @GET("messages/conversations")
    suspend fun getConversations(): Response<BaseResponse<List<Conversation>>>

    /**
     * 获取与指定用户的消息
     * GET /api/v1/messages/{user_id}
     */
    @GET("messages/{user_id}")
    suspend fun getMessages(
        @Path("user_id") userId: Int,
        @Query("page") page: Int = 1,
        @Query("per_page") perPage: Int = 20
    ): Response<BaseResponse<PagedResponse<Message>>>

    /**
     * 发送消息
     * POST /api/v1/messages
     */
    @POST("messages")
    suspend fun sendMessage(@Body message: Message): Response<BaseResponse<Message>>

    // ==================== 通知模块 ====================

    /**
     * 获取通知列表
     * GET /api/v1/notifications
     */
    @GET("notifications")
    suspend fun getNotifications(
        @Query("page") page: Int = 1,
        @Query("per_page") perPage: Int = 20
    ): Response<BaseResponse<PagedResponse<Notification>>>

    /**
     * 标记通知已读
     * PUT /api/v1/notifications/{id}/read
     */
    @PUT("notifications/{id}/read")
    suspend fun markNotificationRead(@Path("id") id: Int): Response<BaseResponse<Any>>

    // ==================== 文件上传模块 ====================

    /**
     * 上传图片
     * POST /api/v1/upload/image
     */
    @POST("upload/image")
    suspend fun uploadImage(@Body imageData: Map<String, String>): Response<BaseResponse<Map<String, String>>>
}
