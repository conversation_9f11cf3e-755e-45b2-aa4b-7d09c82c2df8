package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import com.second.hand.data.model.enums.ProductStatus
import java.math.BigDecimal

/**
 * 商品数据模型
 * 对应Flask后端的Product模型
 */
@Entity(tableName = "products")
data class Product(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    @SerializedName("title")
    var title: String = "",

    @SerializedName("description")
    var description: String = "",

    @SerializedName("price")
    var price: Double = 0.0,

    @SerializedName("original_price")
    var originalPrice: Double? = null,

    // 商品状态
    @SerializedName("status")
    var status: ProductStatus = ProductStatus.PENDING,

    @SerializedName("condition")
    var condition: String? = null,

    @SerializedName("location")
    var location: String? = null,

    @SerializedName("quantity")
    var quantity: Int = 1,

    @SerializedName("sold_quantity")
    var soldQuantity: Int = 0,

    // 外键
    @SerializedName("seller_id")
    var sellerId: Int = 0,

    @SerializedName("category_id")
    var categoryId: Int = 0,

    // 时间戳
    @SerializedName("created_at")
    var createdAt: String = "",

    @SerializedName("updated_at")
    var updatedAt: String? = null,

    // 统计信息
    @SerializedName("view_count")
    var viewCount: Int = 0,

    @SerializedName("favorite_count")
    var favoriteCount: Int = 0,

    // 管理员标记
    @SerializedName("is_featured")
    var isFeatured: Boolean = false,
    
    // 关联数据（API返回时可能包含，不存储到数据库）
    @Ignore
    @SerializedName("seller")
    val seller: User? = null,

    @Ignore
    @SerializedName("category")
    val category: Category? = null,

    @Ignore
    @SerializedName("images")
    val images: List<ProductImage>? = null,

    @SerializedName("main_image")
    var mainImage: String? = null,

    // 收藏状态（API返回字段）
    @SerializedName("is_favorited")
    var isFavorited: Boolean = false
) {
    /**
     * 检查是否已售完
     */
    fun isSoldOut(): Boolean {
        return soldQuantity >= quantity
    }
    
    /**
     * 获取剩余数量
     */
    fun getRemainingQuantity(): Int {
        return maxOf(0, quantity - soldQuantity)
    }
    
    /**
     * 获取状态显示文本
     */
    fun getStatusDisplay(): String {
        return when {
            isSoldOut() -> "已售出"
            status == ProductStatus.ACTIVE -> "在售"
            status == ProductStatus.INACTIVE -> "已下架"
            status == ProductStatus.PENDING -> "待审核"
            else -> "未知状态"
        }
    }
    
    /**
     * 获取主图片URL
     */
    fun getMainImageUrl(baseUrl: String): String {
        return if (!mainImage.isNullOrBlank()) {
            if (mainImage == "default-product.png") {
                "$baseUrl/product_default_image/$categoryId"
            } else {
                "$baseUrl/static/uploads/products/$mainImage"
            }
        } else {
            "$baseUrl/static/images/default-product.png"
        }
    }
    
    /**
     * 获取所有图片URL列表
     */
    fun getAllImageUrls(baseUrl: String): List<String> {
        return images?.map { image ->
            "$baseUrl/static/uploads/products/${image.filename}"
        } ?: listOf(getMainImageUrl(baseUrl))
    }
    
    /**
     * 格式化价格显示
     */
    fun getFormattedPrice(): String {
        return "¥%.2f".format(price)
    }
    
    /**
     * 获取原价格式化显示
     */
    fun getFormattedOriginalPrice(): String? {
        return originalPrice?.let { "¥%.2f".format(it) }
    }
    
    /**
     * 计算折扣百分比
     */
    fun getDiscountPercentage(): Int? {
        return originalPrice?.let { original ->
            if (original > price) {
                ((original - price) / original * 100).toInt()
            } else null
        }
    }
}
