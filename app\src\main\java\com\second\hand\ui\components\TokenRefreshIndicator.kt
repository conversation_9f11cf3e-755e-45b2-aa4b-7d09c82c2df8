package com.second.hand.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.second.hand.data.auth.RefreshStatus
import com.second.hand.data.auth.TokenHealthStatus
import kotlinx.coroutines.delay

/**
 * Token刷新状态指示器
 * 显示Token刷新的实时状态和用户反馈
 */
@Composable
fun TokenRefreshIndicator(
    refreshStatus: RefreshStatus,
    userMessage: String?,
    tokenHealthStatus: TokenHealthStatus,
    onDismiss: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = refreshStatus != RefreshStatus.IDLE || userMessage != null,
        enter = slideInVertically() + fadeIn(),
        exit = slideOutVertically() + fadeOut(),
        modifier = modifier
    ) {
        RefreshStatusCard(
            refreshStatus = refreshStatus,
            userMessage = userMessage,
            tokenHealthStatus = tokenHealthStatus,
            onDismiss = onDismiss
        )
    }
}

@Composable
private fun RefreshStatusCard(
    refreshStatus: RefreshStatus,
    userMessage: String?,
    tokenHealthStatus: TokenHealthStatus,
    onDismiss: () -> Unit
) {
    val (containerColor, contentColor, iconColor) = when (refreshStatus) {
        RefreshStatus.REFRESHING -> Triple(
            MaterialTheme.colorScheme.primaryContainer,
            MaterialTheme.colorScheme.onPrimaryContainer,
            MaterialTheme.colorScheme.primary
        )
        RefreshStatus.SUCCESS -> Triple(
            Color(0xFF4CAF50).copy(alpha = 0.1f),
            Color(0xFF2E7D32),
            Color(0xFF4CAF50)
        )
        RefreshStatus.FAILED -> Triple(
            MaterialTheme.colorScheme.errorContainer,
            MaterialTheme.colorScheme.onErrorContainer,
            MaterialTheme.colorScheme.error
        )
        RefreshStatus.IDLE -> Triple(
            MaterialTheme.colorScheme.surfaceVariant,
            MaterialTheme.colorScheme.onSurfaceVariant,
            MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = containerColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 状态图标
            when (refreshStatus) {
                RefreshStatus.REFRESHING -> {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        strokeWidth = 2.dp,
                        color = iconColor
                    )
                }
                RefreshStatus.SUCCESS -> {
                    Icon(
                        Icons.Default.Refresh,
                        contentDescription = "刷新成功",
                        tint = iconColor,
                        modifier = Modifier.size(20.dp)
                    )
                }
                RefreshStatus.FAILED -> {
                    Icon(
                        Icons.Default.Warning,
                        contentDescription = "刷新失败",
                        tint = iconColor,
                        modifier = Modifier.size(20.dp)
                    )
                }
                RefreshStatus.IDLE -> {
                    Icon(
                        Icons.Default.Refresh,
                        contentDescription = "空闲",
                        tint = iconColor,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            // 状态文本
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = getStatusTitle(refreshStatus, tokenHealthStatus),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = contentColor
                )
                
                userMessage?.let { message ->
                    Text(
                        text = message,
                        style = MaterialTheme.typography.bodySmall,
                        color = contentColor.copy(alpha = 0.8f)
                    )
                }
            }
            
            // 关闭按钮
            if (refreshStatus != RefreshStatus.REFRESHING) {
                TextButton(
                    onClick = onDismiss,
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = "关闭",
                        style = MaterialTheme.typography.labelSmall,
                        color = contentColor
                    )
                }
            }
        }
    }
}

/**
 * 获取状态标题
 */
private fun getStatusTitle(refreshStatus: RefreshStatus, tokenHealthStatus: TokenHealthStatus): String {
    return when (refreshStatus) {
        RefreshStatus.REFRESHING -> "正在刷新登录状态..."
        RefreshStatus.SUCCESS -> "登录状态刷新成功"
        RefreshStatus.FAILED -> "登录状态刷新失败"
        RefreshStatus.IDLE -> when (tokenHealthStatus) {
            TokenHealthStatus.HEALTHY -> "登录状态正常"
            TokenHealthStatus.NEEDS_REFRESH -> "登录状态即将过期"
            TokenHealthStatus.EXPIRED -> "登录已过期"
            TokenHealthStatus.NO_TOKEN -> "未登录"
            TokenHealthStatus.REFRESH_FAILED -> "登录状态异常"
        }
    }
}

/**
 * 简化的Token健康状态指示器
 */
@Composable
fun TokenHealthIndicator(
    tokenHealthStatus: TokenHealthStatus,
    healthDescription: String,
    modifier: Modifier = Modifier
) {
    val (color, icon) = when (tokenHealthStatus) {
        TokenHealthStatus.HEALTHY -> Color(0xFF4CAF50) to "✅"
        TokenHealthStatus.NEEDS_REFRESH -> Color(0xFFFF9800) to "🔄"
        TokenHealthStatus.EXPIRED -> Color(0xFFF44336) to "⏰"
        TokenHealthStatus.NO_TOKEN -> Color(0xFF9E9E9E) to "🔒"
        TokenHealthStatus.REFRESH_FAILED -> Color(0xFFF44336) to "❌"
    }
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = icon,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = healthDescription,
            style = MaterialTheme.typography.bodySmall,
            color = color
        )
    }
}

/**
 * 自动消失的Token刷新提示
 */
@Composable
fun AutoDismissTokenRefreshIndicator(
    refreshStatus: RefreshStatus,
    userMessage: String?,
    tokenHealthStatus: TokenHealthStatus,
    onDismiss: () -> Unit,
    autoDismissDelay: Long = 3000L,
    modifier: Modifier = Modifier
) {
    var shouldShow by remember(refreshStatus, userMessage) { 
        mutableStateOf(refreshStatus != RefreshStatus.IDLE || userMessage != null) 
    }
    
    // 自动消失逻辑
    LaunchedEffect(refreshStatus, userMessage) {
        if (refreshStatus == RefreshStatus.SUCCESS || refreshStatus == RefreshStatus.FAILED) {
            delay(autoDismissDelay)
            shouldShow = false
            onDismiss()
        }
    }
    
    AnimatedVisibility(
        visible = shouldShow,
        enter = slideInVertically() + fadeIn(),
        exit = slideOutVertically() + fadeOut(),
        modifier = modifier
    ) {
        TokenRefreshIndicator(
            refreshStatus = refreshStatus,
            userMessage = userMessage,
            tokenHealthStatus = tokenHealthStatus,
            onDismiss = {
                shouldShow = false
                onDismiss()
            }
        )
    }
}
