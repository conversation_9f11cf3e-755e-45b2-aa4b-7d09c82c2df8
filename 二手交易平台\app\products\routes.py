#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品管理路由
"""

import os
import uuid
from flask import render_template, redirect, url_for, flash, request, current_app, abort, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
from app.products import bp
from app.products.forms import ProductForm, EditProductForm
from app.models import Product, ProductImage, Category, ProductStatus, Favorite
from app.utils.file_utils import allowed_file, save_uploaded_file, generate_unique_filename
from sqlalchemy import desc

@bp.route('/publish', methods=['GET', 'POST'])
@login_required
def publish():
    """发布商品"""
    form = ProductForm()
    
    if form.validate_on_submit():
        product = Product(
            title=form.title.data,
            description=form.description.data,
            price=form.price.data,
            original_price=form.original_price.data,
            category_id=form.category_id.data,
            condition=form.condition.data,
            location=form.location.data,
            quantity=form.quantity.data,
            seller_id=current_user.id,
            status=ProductStatus.ACTIVE  # 直接上架，无需审核
        )
        
        db.session.add(product)
        db.session.flush()  # 获取product.id
        
        # 处理图片上传
        if form.images.data:
            upload_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'products')
            if not os.path.exists(upload_dir):
                os.makedirs(upload_dir)
            
            for i, image_file in enumerate(form.images.data):
                if image_file and allowed_file(image_file.filename):
                    filename = secure_filename(image_file.filename)
                    # 安全地获取文件扩展名
                    if '.' in filename:
                        ext = filename.rsplit('.', 1)[1].lower()
                    else:
                        ext = 'jpg'  # 默认扩展名
                    filename = f"{uuid.uuid4().hex}.{ext}"
                    
                    image_path = os.path.join(upload_dir, filename)
                    image_file.save(image_path)
                    
                    product_image = ProductImage(
                        filename=filename,
                        product_id=product.id,
                        is_main=(i == 0),  # 第一张图片设为主图
                        sort_order=i
                    )
                    db.session.add(product_image)
        
        db.session.commit()
        flash('商品发布成功，已上架销售！', 'success')
        return redirect(url_for('products.my_products'))
    
    return render_template('products/publish.html', title='发布商品', form=form)

@bp.route('/detail/<int:id>')
def detail(id):
    """商品详情"""
    product = Product.query.get_or_404(id)

    # 只有在售商品或商品所有者/管理员才能查看
    if product.status != ProductStatus.ACTIVE:
        if not current_user.is_authenticated or \
           (current_user.id != product.seller_id and not current_user.is_admin()):
            # 返回更友好的错误信息
            return render_template('errors/product_unavailable.html',
                                 title='商品不可用',
                                 product=product), 404
    
    # 浏览量将通过前端异步更新，不在这里处理以提高页面加载速度
    
    # 检查是否已收藏
    is_favorited = False
    if current_user.is_authenticated:
        is_favorited = Favorite.query.filter_by(
            user_id=current_user.id,
            product_id=product.id
        ).first() is not None
    
    # 获取相关商品
    related_products = Product.query.filter(
        Product.category_id == product.category_id,
        Product.id != product.id,
        Product.status == ProductStatus.ACTIVE
    ).limit(4).all()
    
    return render_template('products/detail.html',
                         title=product.title,
                         product=product,
                         is_favorited=is_favorited,
                         related_products=related_products)

@bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑商品"""
    product = Product.query.get_or_404(id)
    
    # 只有商品所有者或管理员才能编辑
    if current_user.id != product.seller_id and not current_user.is_admin():
        abort(403)
    
    form = EditProductForm()
    
    if form.validate_on_submit():
        product.title = form.title.data
        product.description = form.description.data
        product.price = form.price.data
        product.original_price = form.original_price.data
        product.category_id = form.category_id.data
        product.condition = form.condition.data
        product.location = form.location.data
        
        # 如果是普通用户编辑，重新设为待审核状态
        if not current_user.is_admin():
            product.status = ProductStatus.PENDING
        
        # 处理新上传的图片
        if form.images.data and form.images.data[0]:
            upload_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'products')
            if not os.path.exists(upload_dir):
                os.makedirs(upload_dir)
            
            # 获取当前图片数量
            current_image_count = product.images.count()
            
            for i, image_file in enumerate(form.images.data):
                if image_file and allowed_file(image_file.filename):
                    filename = secure_filename(image_file.filename)
                    # 安全地获取文件扩展名
                    if '.' in filename:
                        ext = filename.rsplit('.', 1)[1].lower()
                    else:
                        ext = 'jpg'  # 默认扩展名
                    filename = f"{uuid.uuid4().hex}.{ext}"
                    
                    image_path = os.path.join(upload_dir, filename)
                    image_file.save(image_path)
                    
                    product_image = ProductImage(
                        filename=filename,
                        product_id=product.id,
                        is_main=(current_image_count == 0 and i == 0),
                        sort_order=current_image_count + i
                    )
                    db.session.add(product_image)
        
        db.session.commit()
        flash('商品信息已更新', 'success')
        return redirect(url_for('products.detail', id=product.id))
    
    elif request.method == 'GET':
        form.title.data = product.title
        form.description.data = product.description
        form.price.data = product.price
        form.original_price.data = product.original_price
        form.category_id.data = product.category_id
        form.condition.data = product.condition
        form.location.data = product.location
    
    return render_template('products/edit.html', title='编辑商品', form=form, product=product)

@bp.route('/my_products')
@login_required
def my_products():
    """我的商品"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')

    query = Product.query.filter_by(seller_id=current_user.id)

    if status_filter != 'all':
        if status_filter == 'active':
            query = query.filter_by(status=ProductStatus.ACTIVE)
        elif status_filter == 'pending':
            query = query.filter_by(status=ProductStatus.PENDING)
        elif status_filter == 'sold':
            query = query.filter_by(status=ProductStatus.SOLD)
        elif status_filter == 'inactive':
            query = query.filter_by(status=ProductStatus.INACTIVE)

    products = query.order_by(desc(Product.created_at)).paginate(
        page=page,
        per_page=current_app.config['PRODUCTS_PER_PAGE'],
        error_out=False
    )

    return render_template('products/my_products.html',
                         title='我的商品',
                         products=products,
                         status_filter=status_filter)

@bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    """删除商品"""
    try:
        product = Product.query.get_or_404(id)
        product_title = product.title

        # 只有商品所有者或管理员才能删除
        if current_user.id != product.seller_id and not current_user.is_admin():
            abort(403)

        # 检查是否有相关订单
        from app.models import Order
        orders = Order.query.filter_by(product_id=id).count()
        if orders > 0:
            flash('该商品有相关订单，无法删除', 'error')
            return redirect(url_for('products.my_products'))

        # 删除相关的收藏记录
        from app.models import Favorite, Message
        Favorite.query.filter_by(product_id=id).delete()

        # 删除相关的消息记录（逻辑删除）
        messages = Message.query.filter_by(product_id=id).all()
        for message in messages:
            message.deleted_by_sender = True
            message.deleted_by_receiver = True

        # 删除商品图片文件
        upload_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'products')
        for image in product.images:
            image_path = os.path.join(upload_dir, image.filename)
            if os.path.exists(image_path):
                try:
                    os.remove(image_path)
                except OSError as e:
                    print(f"删除图片文件失败: {image_path}, 错误: {e}")

        # 删除商品（级联删除会自动删除相关的ProductImage记录）
        db.session.delete(product)
        db.session.commit()

        flash(f'商品"{product_title}"已删除', 'success')
        return redirect(url_for('products.my_products'))

    except Exception as e:
        db.session.rollback()
        print(f"删除商品失败: {str(e)}")
        import traceback
        traceback.print_exc()
        flash(f'删除失败: {str(e)}', 'error')
        return redirect(url_for('products.my_products'))

@bp.route('/toggle_favorite/<int:id>', methods=['POST'])
@login_required
def toggle_favorite(id):
    """切换收藏状态"""
    try:
        product = Product.query.get_or_404(id)

        # 使用数据库锁防止并发问题
        favorite = Favorite.query.filter_by(
            user_id=current_user.id,
            product_id=product.id
        ).first()

        if favorite:
            # 取消收藏
            db.session.delete(favorite)
            # 确保收藏数不会小于0
            if product.favorite_count > 0:
                product.favorite_count -= 1
            is_favorited = False
            message = '已取消收藏'
        else:
            # 添加收藏
            favorite = Favorite(user_id=current_user.id, product_id=product.id)
            db.session.add(favorite)
            product.favorite_count += 1
            is_favorited = True
            message = '已添加收藏'

        db.session.commit()

        return jsonify({
            'success': True,
            'is_favorited': is_favorited,
            'favorite_count': product.favorite_count,
            'message': message
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '操作失败，请重试'
        }), 500

@bp.route('/favorites')
@login_required
def favorites():
    """我的收藏"""
    page = request.args.get('page', 1, type=int)

    favorites = db.session.query(Product).join(Favorite).filter(
        Favorite.user_id == current_user.id,
        Product.status == ProductStatus.ACTIVE
    ).order_by(desc(Favorite.created_at)).paginate(
        page=page,
        per_page=current_app.config['PRODUCTS_PER_PAGE'],
        error_out=False
    )

    return render_template('products/favorites.html',
                         title='我的收藏',
                         products=favorites)

@bp.route('/delete_image/<int:image_id>', methods=['POST'])
@login_required
def delete_image(image_id):
    """删除商品图片"""
    image = ProductImage.query.get_or_404(image_id)
    product = image.product

    # 只有商品所有者或管理员才能删除图片
    if current_user.id != product.seller_id and not current_user.is_admin():
        abort(403)

    # 删除文件
    upload_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'products')
    image_path = os.path.join(upload_dir, image.filename)
    if os.path.exists(image_path):
        os.remove(image_path)

    # 如果删除的是主图，将第一张图片设为主图
    if image.is_main:
        next_image = ProductImage.query.filter(
            ProductImage.product_id == product.id,
            ProductImage.id != image.id
        ).first()
        if next_image:
            next_image.is_main = True

    db.session.delete(image)
    db.session.commit()

    return jsonify({'success': True, 'message': '图片已删除'})


