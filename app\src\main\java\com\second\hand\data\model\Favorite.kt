package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 收藏数据模型
 * 对应Flask后端的Favorite模型
 */
@Entity(tableName = "favorites")
data class Favorite(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    // 外键
    @SerializedName("user_id")
    var userId: Int = 0,

    @SerializedName("product_id")
    var productId: Int = 0,

    // 时间戳
    @SerializedName("created_at")
    var createdAt: String = "",
    
    // 关联数据（API返回时可能包含，不存储到数据库）
    @Ignore
    @SerializedName("user")
    val user: User? = null,

    @Ignore
    @SerializedName("product")
    val product: Product? = null
)
