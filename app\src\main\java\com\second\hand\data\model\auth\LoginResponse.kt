package com.second.hand.data.model.auth

import com.google.gson.annotations.SerializedName
import com.second.hand.data.model.User

/**
 * 登录响应数据模型
 */
data class LoginResponse(
    @SerializedName("token")
    val token: String = "",

    @SerializedName("refreshToken")
    val refreshToken: String = "",

    @SerializedName("token_type")
    val tokenType: String = "Bearer",

    @SerializedName("expires_in")
    val expiresIn: Long = 0,

    @SerializedName("user")
    val user: User = User()
)
