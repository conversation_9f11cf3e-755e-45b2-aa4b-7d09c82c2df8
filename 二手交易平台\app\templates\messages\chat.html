{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <!-- 聊天头部 -->
    <div class="card mb-3">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="{{ url_for('messages.index') }}" class="btn btn-sm btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <img src="{{ other_user.get_avatar_url() }}" 
                         class="rounded-circle me-3" 
                         width="40" height="40" alt="头像">
                    <div>
                        <h6 class="mb-0">{{ other_user.nickname or other_user.username }}</h6>
                        {% if product %}
                        <small class="text-muted">关于商品: {{ product.title }}</small>
                        {% endif %}
                    </div>
                </div>
                {% if product and product.status.value == 'active' %}
                <a href="{{ url_for('products.detail', id=product.id) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="fas fa-eye me-1"></i>查看商品
                </a>
                {% elif product %}
                <span class="text-muted small">
                    <i class="fas fa-info-circle me-1"></i>商品已下架或不可用
                </span>
                {% else %}
                <span class="text-muted small">
                    <i class="fas fa-info-circle me-1"></i>商品信息不可用
                </span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 聊天区域 -->
    <div class="row">
        <div class="col-12">
            <div class="card chat-container">
                <div class="card-body p-0">
                    <!-- 消息列表 -->
                    <div class="chat-messages" id="chatMessages">
                        {% for message in messages %}
                        <div class="message-item {{ 'sent' if message.sender_id == current_user.id else 'received' }}" data-message-id="{{ message.id }}">
                            <div class="message-content">
                                <div class="message-text">{{ message.content|e }}</div>
                                <div class="message-time">
                                    {{ moment(message.created_at).format('HH:mm') }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="chat-input-area">
                        <div class="input-group">
                            <input type="text" class="form-control" id="messageInput" 
                                   placeholder="输入消息..." maxlength="500">
                            <button class="btn btn-primary" type="button" id="sendButton">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-container {
    height: 70vh;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f8f9fa;
}

.message-item {
    margin-bottom: 15px;
    display: flex;
}

.message-item.sent {
    justify-content: flex-end;
}

.message-item.received {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    position: relative;
}

.message-text {
    padding: 10px 15px;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
}

.message-item.sent .message-text {
    background-color: #007bff;
    color: white;
    border-bottom-right-radius: 5px;
}

.message-item.received .message-text {
    background-color: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 5px;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 5px;
    text-align: center;
}

.chat-input-area {
    padding: 20px;
    background-color: white;
    border-top: 1px solid #e9ecef;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 消息动画 */
.message-item {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 输入框焦点效果 */
#messageInput:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 发送按钮效果 */
#sendButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}
</style>

{% block scripts %}
<script>
$(document).ready(function() {
    const chatMessages = $('#chatMessages');
    const messageInput = $('#messageInput');
    const sendButton = $('#sendButton');
    
    // 滚动到底部
    function scrollToBottom() {
        chatMessages.scrollTop(chatMessages[0].scrollHeight);
    }
    
    // 页面加载时滚动到底部
    scrollToBottom();

    // 页面加载时刷新未读消息计数（因为消息已被标记为已读）
    if (typeof refreshNotifications === 'function') {
        refreshNotifications();
    }

    // 发送消息
    function sendMessage() {
        const content = messageInput.val().trim();
        if (!content) return;
        
        const data = {
            receiver_id: {{ other_user.id }},
            content: content,
            {% if product %}product_id: {{ product.id }}{% else %}product_id: null{% endif %}
        };
        
        // 禁用输入和按钮
        messageInput.prop('disabled', true);
        sendButton.prop('disabled', true);
        
        $.ajax({
            url: '/messages/send',
            method: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
            },
            data: JSON.stringify(data),
            success: function(response) {
                if (response.success) {
                    // 转义HTML内容防止XSS攻击
                    const escapedContent = $('<div>').text(content).html();
                    // 添加消息到界面
                    const messageHtml = `
                        <div class="message-item sent" data-message-id="${response.message ? response.message.id : ''}">
                            <div class="message-content">
                                <div class="message-text">${escapedContent}</div>
                                <div class="message-time">${moment().format('HH:mm')}</div>
                            </div>
                        </div>
                    `;
                    chatMessages.append(messageHtml);
                    messageInput.val('');
                    scrollToBottom();
                } else {
                    showAlert('发送失败：' + response.message, 'error');
                }
            },
            error: function() {
                showAlert('发送失败，请重试', 'error');
            },
            complete: function() {
                // 重新启用输入和按钮
                messageInput.prop('disabled', false);
                sendButton.prop('disabled', false);
                messageInput.focus();
            }
        });
    }
    
    // 点击发送按钮
    sendButton.click(sendMessage);
    
    // 回车发送
    messageInput.keypress(function(e) {
        if (e.which === 13) {
            sendMessage();
        }
    });
    
    // 自动聚焦输入框
    messageInput.focus();
    
    // 定期检查新消息（每2秒）
    setInterval(function() {
        loadNewMessages();
    }, 500);
    
    // 加载新消息
    function loadNewMessages() {
        // 获取当前页面上所有消息的ID
        const existingMessageIds = new Set();
        $('.message-item').each(function() {
            const messageId = $(this).data('message-id');
            if (messageId) {
                existingMessageIds.add(parseInt(messageId));
            }
        });

        $.ajax({
            url: '/messages/api/messages/{{ other_user.id }}{% if product %}/{{ product.id }}{% endif %}',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    // 只添加不存在的新消息，且不是当前用户发送的
                    const newMessages = response.messages.filter(msg =>
                        !existingMessageIds.has(msg.id) && msg.sender_id !== {{ current_user.id }}
                    );

                    newMessages.forEach(function(message) {
                        // 转义HTML内容防止XSS攻击
                        const escapedContent = $('<div>').text(message.content).html();
                        const messageHtml = `
                            <div class="message-item received" data-message-id="${message.id}">
                                <div class="message-content">
                                    <div class="message-text">${escapedContent}</div>
                                    <div class="message-time">${moment(message.created_at).format('HH:mm')}</div>
                                </div>
                            </div>
                        `;
                        chatMessages.append(messageHtml);
                    });

                    if (newMessages.length > 0) {
                        scrollToBottom();
                    }
                }
            },
            error: function() {
                console.log('检查新消息失败');
            }
        });
    }
});
</script>
{% endblock %}
{% endblock %}
