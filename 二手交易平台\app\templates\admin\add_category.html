{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus"></i> 添加分类</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% for error in form.name.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                        {% for error in form.description.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.icon.label(class="form-label") }}
                        {{ form.icon(class="form-control", placeholder="例如: fas fa-mobile-alt") }}
                        {% for error in form.icon.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <div class="form-text">
                            请输入FontAwesome图标类名，例如：fas fa-mobile-alt
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存分类
                        </button>
                        <a href="{{ url_for('admin_panel.categories') }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">图标预览</h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i id="icon-preview" class="fas fa-tag fa-3x text-muted"></i>
                    <p class="mt-2 text-muted">输入图标类名查看预览</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">常用图标</h6>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-4 text-center">
                        <i class="fas fa-mobile-alt fa-2x text-primary"></i>
                        <small class="d-block">电子产品</small>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-tshirt fa-2x text-success"></i>
                        <small class="d-block">服装</small>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-home fa-2x text-warning"></i>
                        <small class="d-block">家居</small>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-book fa-2x text-info"></i>
                        <small class="d-block">图书</small>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-running fa-2x text-danger"></i>
                        <small class="d-block">运动</small>
                    </div>
                    <div class="col-4 text-center">
                        <i class="fas fa-car fa-2x text-secondary"></i>
                        <small class="d-block">汽车</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('icon').addEventListener('input', function() {
    const iconClass = this.value.trim();
    const preview = document.getElementById('icon-preview');
    
    if (iconClass) {
        preview.className = iconClass + ' fa-3x text-primary';
    } else {
        preview.className = 'fas fa-tag fa-3x text-muted';
    }
});
</script>
{% endblock %}
