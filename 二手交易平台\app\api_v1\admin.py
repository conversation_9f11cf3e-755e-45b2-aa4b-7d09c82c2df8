#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端管理员API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import User, Product, Order, Category, Review, Notification, ProductStatus, OrderStatus, UserRole
from app import db
from app.utils.jwt_utils import jwt_required, admin_required
from app.utils.api_response import (
    success_response, error_response, validation_error_response,
    paginated_response, not_found_response
)
from app.utils.datetime_utils import local_now
from app.utils.notification_utils import create_notification
from sqlalchemy import func, desc, and_
from datetime import datetime, timedelta


def format_datetime_safe(dt):
    """安全地格式化日期时间"""
    if dt is None:
        return None
    try:
        # 如果是字符串，先解析为datetime对象
        if isinstance(dt, str):
            # 尝试解析包含微秒的格式
            try:
                dt = datetime.strptime(dt, '%Y-%m-%dT%H:%M:%S.%f')
            except ValueError:
                try:
                    dt = datetime.strptime(dt, '%Y-%m-%dT%H:%M:%S')
                except ValueError:
                    # 如果都解析失败，返回原字符串
                    return dt

        # 格式化为标准ISO格式
        return dt.strftime('%Y-%m-%dT%H:%M:%S') + 'Z'
    except Exception as e:
        current_app.logger.error(f"日期格式化失败: {str(e)}, 原始值: {dt}")
        return str(dt) if dt else None


def serialize_user_admin(user):
    """序列化用户数据（管理员视图）"""
    return {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'nickname': user.nickname,
        'role': user.role.value,
        'isActive': user.is_active,
        'avatar': user.avatar_url(),
        'createdAt': format_datetime_safe(user.created_at),
        'lastLoginAt': format_datetime_safe(user.last_login_at),
        'updatedAt': format_datetime_safe(user.updated_at),
        'productCount': user.products.count(),
        'orderCount': user.buyer_orders.count() + user.seller_orders.count()
    }


def serialize_product_admin(product):
    """序列化商品数据（管理员视图）"""
    return {
        'id': product.id,
        'title': product.title,
        'price': float(product.price),
        'status': product.status.value,
        'condition': product.condition,
        'quantity': product.quantity,
        'soldQuantity': product.sold_quantity,
        'viewCount': product.view_count,
        'favoriteCount': product.favorite_count,
        'seller': {
            'id': product.seller.id,
            'username': product.seller.username,
            'nickname': product.seller.nickname
        } if product.seller else None,
        'category': {
            'id': product.category.id,
            'name': product.category.name
        } if product.category else None,
        'createdAt': format_datetime_safe(product.created_at),
        'updatedAt': format_datetime_safe(product.updated_at)
    }


@bp.route('/admin/dashboard', methods=['GET'])
@admin_required
def admin_dashboard():
    """管理员仪表板统计"""
    try:
        # 基础统计
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        total_products = Product.query.count()
        pending_products = Product.query.filter_by(status=ProductStatus.PENDING).count()
        active_products = Product.query.filter_by(status=ProductStatus.ACTIVE).count()
        total_orders = Order.query.count()
        completed_orders = Order.query.filter_by(status=OrderStatus.COMPLETED).count()
        
        # 今日统计
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        new_users_today = User.query.filter(User.created_at >= today_start).count()
        new_products_today = Product.query.filter(Product.created_at >= today_start).count()
        new_orders_today = Order.query.filter(Order.created_at >= today_start).count()
        
        # 本周统计
        week_start = today_start - timedelta(days=today_start.weekday())
        new_users_week = User.query.filter(User.created_at >= week_start).count()
        new_products_week = Product.query.filter(Product.created_at >= week_start).count()
        new_orders_week = Order.query.filter(Order.created_at >= week_start).count()
        
        # 收入统计
        total_revenue = db.session.query(func.sum(Order.total_price)).filter(
            Order.status == OrderStatus.COMPLETED
        ).scalar() or 0
        
        today_revenue = db.session.query(func.sum(Order.total_price)).filter(
            Order.status == OrderStatus.COMPLETED,
            Order.created_at >= today_start
        ).scalar() or 0
        
        return success_response({
            'overview': {
                'totalUsers': total_users,
                'activeUsers': active_users,
                'totalProducts': total_products,
                'pendingProducts': pending_products,
                'activeProducts': active_products,
                'totalOrders': total_orders,
                'completedOrders': completed_orders,
                'totalRevenue': float(total_revenue),
                'todayRevenue': float(today_revenue)
            },
            'today': {
                'newUsers': new_users_today,
                'newProducts': new_products_today,
                'newOrders': new_orders_today,
                'revenue': float(today_revenue)
            },
            'thisWeek': {
                'newUsers': new_users_week,
                'newProducts': new_products_week,
                'newOrders': new_orders_week
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取管理员仪表板数据失败: {str(e)}")
        return error_response('SYS_001', '获取仪表板数据失败', http_code=500)


@bp.route('/admin/users', methods=['GET'])
@admin_required
def get_users():
    """获取用户列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        search = request.args.get('search', '').strip()
        role = request.args.get('role', 'all')
        status = request.args.get('status', 'all')
        
        # 构建查询
        query = User.query
        
        # 搜索
        if search:
            query = query.filter(
                db.or_(
                    User.username.contains(search),
                    User.email.contains(search),
                    User.nickname.contains(search)
                )
            )
        
        # 角色筛选
        if role != 'all':
            try:
                role_enum = UserRole(role)
                query = query.filter(User.role == role_enum)
            except ValueError:
                return validation_error_response({'role': '无效的用户角色'})
        
        # 状态筛选
        if status == 'active':
            query = query.filter(User.is_active == True)
        elif status == 'inactive':
            query = query.filter(User.is_active == False)
        
        # 排序
        query = query.order_by(desc(User.created_at))
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )
        
        # 序列化数据
        users = [serialize_user_admin(user) for user in pagination.items]
        
        return paginated_response(users, pagination)
        
    except Exception as e:
        current_app.logger.error(f"获取用户列表失败: {str(e)}")
        return error_response('SYS_001', '获取用户列表失败', http_code=500)


@bp.route('/admin/users/<int:user_id>', methods=['PUT'])
@admin_required
def update_user(user_id):
    """更新用户信息"""
    try:
        user = User.query.get(user_id)
        if not user:
            return not_found_response('用户不存在')
        
        data = request.get_json()
        
        # 更新用户信息
        if 'isActive' in data:
            user.is_active = bool(data['isActive'])
        
        if 'role' in data:
            try:
                role_enum = UserRole(data['role'])
                user.role = role_enum
            except ValueError:
                return validation_error_response({'role': '无效的用户角色'})
        
        if 'nickname' in data:
            nickname = data['nickname'].strip()
            if len(nickname) > 50:
                return validation_error_response({'nickname': '昵称长度不能超过50个字符'})
            user.nickname = nickname
        
        user.updated_at = local_now()
        db.session.commit()
        
        # 发送通知给用户
        if 'isActive' in data:
            if data['isActive']:
                create_notification(
                    user_id=user.id,
                    title='账户状态更新',
                    content='您的账户已被激活',
                    type='system'
                )
            else:
                create_notification(
                    user_id=user.id,
                    title='账户状态更新',
                    content='您的账户已被禁用',
                    type='system'
                )
            db.session.commit()
        
        return success_response(
            serialize_user_admin(user),
            '用户信息更新成功'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户信息失败: {str(e)}")
        return error_response('SYS_001', '更新用户信息失败', http_code=500)


@bp.route('/admin/products', methods=['GET'])
@admin_required
def get_admin_products():
    """获取商品列表（管理员）"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        search = request.args.get('search', '').strip()
        status = request.args.get('status', 'all')
        category_id = request.args.get('category', type=int)
        
        # 构建查询
        query = Product.query
        
        # 搜索
        if search:
            query = query.filter(
                db.or_(
                    Product.title.contains(search),
                    Product.description.contains(search)
                )
            )
        
        # 状态筛选
        if status != 'all':
            try:
                status_enum = ProductStatus(status)
                query = query.filter(Product.status == status_enum)
            except ValueError:
                return validation_error_response({'status': '无效的商品状态'})
        
        # 分类筛选
        if category_id:
            query = query.filter(Product.category_id == category_id)
        
        # 排序
        query = query.order_by(desc(Product.created_at))
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )
        
        # 序列化数据
        products = [serialize_product_admin(product) for product in pagination.items]
        
        return paginated_response(products, pagination)
        
    except Exception as e:
        current_app.logger.error(f"获取商品列表失败: {str(e)}")
        return error_response('SYS_001', '获取商品列表失败', http_code=500)


@bp.route('/admin/products/<int:product_id>/audit', methods=['PUT'])
@admin_required
def audit_product(product_id):
    """商品审核"""
    try:
        product = Product.query.get(product_id)
        if not product:
            return not_found_response('商品不存在')

        data = request.get_json()
        action = data.get('action')  # approve, reject
        reason = data.get('reason', '').strip()

        if action not in ['approve', 'reject']:
            return validation_error_response({'action': '无效的审核操作'})

        if action == 'approve':
            product.status = ProductStatus.ACTIVE
            notification_title = '商品审核通过'
            notification_content = f'您的商品"{product.title}"已审核通过，现在可以正常展示'
        else:  # reject
            product.status = ProductStatus.INACTIVE
            notification_title = '商品审核未通过'
            notification_content = f'您的商品"{product.title}"审核未通过'
            if reason:
                notification_content += f'，原因：{reason}'

        product.updated_at = local_now()

        # 发送通知给商品发布者
        create_notification(
            user_id=product.seller_id,
            title=notification_title,
            content=notification_content,
            type='product',
            related_id=product.id
        )

        db.session.commit()

        return success_response(
            serialize_product_admin(product),
            f'商品审核完成：{action}'
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"商品审核失败: {str(e)}")
        return error_response('SYS_001', '商品审核失败', http_code=500)


@bp.route('/admin/orders', methods=['GET'])
@admin_required
def get_admin_orders():
    """获取订单列表（管理员）"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        status = request.args.get('status', 'all')
        search = request.args.get('search', '').strip()

        # 构建查询
        query = Order.query

        # 状态筛选
        if status != 'all':
            try:
                status_enum = OrderStatus(status)
                query = query.filter(Order.status == status_enum)
            except ValueError:
                return validation_error_response({'status': '无效的订单状态'})

        # 搜索（按订单号）
        if search:
            query = query.filter(Order.order_number.contains(search))

        # 排序
        query = query.order_by(desc(Order.created_at))

        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )

        # 序列化数据
        orders = []
        for order in pagination.items:
            order_data = {
                'id': order.id,
                'orderNumber': order.order_number,
                'quantity': order.quantity,
                'totalPrice': float(order.total_price),
                'status': order.status.value,
                'createdAt': format_datetime_safe(order.created_at),
                'buyer': {
                    'id': order.buyer.id,
                    'username': order.buyer.username,
                    'nickname': order.buyer.nickname
                } if order.buyer else None,
                'seller': {
                    'id': order.seller.id,
                    'username': order.seller.username,
                    'nickname': order.seller.nickname
                } if order.seller else None,
                'product': {
                    'id': order.product.id,
                    'title': order.product.title,
                    'price': float(order.product.price)
                } if order.product else None
            }
            orders.append(order_data)

        return paginated_response(orders, pagination)

    except Exception as e:
        current_app.logger.error(f"获取订单列表失败: {str(e)}")
        return error_response('SYS_001', '获取订单列表失败', http_code=500)


@bp.route('/admin/categories', methods=['GET'])
@admin_required
def get_admin_categories():
    """获取分类列表（管理员）"""
    try:
        categories = Category.query.order_by(Category.id).all()

        categories_data = []
        for category in categories:
            # 统计该分类的商品数量
            product_count = Product.query.filter_by(category_id=category.id).count()

            categories_data.append({
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'icon': category.icon,
                'color': category.color or category.get_color(),  # 使用数据库字段或生成的颜色
                'productCount': product_count,
                'createdAt': format_datetime_safe(category.created_at)
            })

        return success_response(categories_data)

    except Exception as e:
        current_app.logger.error(f"获取分类列表失败: {str(e)}")
        return error_response('SYS_001', '获取分类列表失败', http_code=500)


@bp.route('/admin/categories', methods=['POST'])
@admin_required
def create_category():
    """创建分类"""
    try:
        data = request.get_json()

        # 验证数据
        name = data.get('name', '').strip()
        if not name:
            return validation_error_response({'name': '分类名称不能为空'})

        if len(name) > 50:
            return validation_error_response({'name': '分类名称不能超过50个字符'})

        # 检查名称是否已存在
        if Category.query.filter_by(name=name).first():
            return error_response('BIZ_002', '分类名称已存在')

        description = data.get('description', '').strip()
        if len(description) > 200:
            return validation_error_response({'description': '分类描述不能超过200个字符'})

        icon = data.get('icon', '').strip()
        color = data.get('color', '').strip()

        # 创建分类
        category = Category(
            name=name,
            description=description,
            icon=icon,
            color=color,
            created_at=local_now()
        )

        db.session.add(category)
        db.session.commit()

        return success_response({
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'icon': category.icon,
            'color': category.color or category.get_color(),  # 使用数据库字段或生成的颜色
            'productCount': 0,
            'createdAt': format_datetime_safe(category.created_at)
        }, '分类创建成功', 201)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建分类失败: {str(e)}")
        return error_response('SYS_001', '创建分类失败', http_code=500)


@bp.route('/admin/statistics', methods=['GET'])
@admin_required
def get_statistics():
    """获取统计数据"""
    try:
        # 获取时间范围参数
        days = request.args.get('days', 30, type=int)
        start_date = datetime.now() - timedelta(days=days)

        # 用户注册趋势
        user_stats = db.session.query(
            func.date(User.created_at).label('date'),
            func.count(User.id).label('count')
        ).filter(
            User.created_at >= start_date
        ).group_by(
            func.date(User.created_at)
        ).order_by('date').all()

        # 商品发布趋势
        product_stats = db.session.query(
            func.date(Product.created_at).label('date'),
            func.count(Product.id).label('count')
        ).filter(
            Product.created_at >= start_date
        ).group_by(
            func.date(Product.created_at)
        ).order_by('date').all()

        # 订单创建趋势
        order_stats = db.session.query(
            func.date(Order.created_at).label('date'),
            func.count(Order.id).label('count'),
            func.sum(Order.total_price).label('revenue')
        ).filter(
            Order.created_at >= start_date
        ).group_by(
            func.date(Order.created_at)
        ).order_by('date').all()

        # 分类统计
        category_stats = db.session.query(
            Category.name,
            func.count(Product.id).label('count')
        ).outerjoin(Product).group_by(Category.id, Category.name).all()

        return success_response({
            'userTrend': [
                {
                    'date': stat.date.isoformat(),
                    'count': stat.count
                }
                for stat in user_stats
            ],
            'productTrend': [
                {
                    'date': stat.date.isoformat(),
                    'count': stat.count
                }
                for stat in product_stats
            ],
            'orderTrend': [
                {
                    'date': stat.date.isoformat(),
                    'count': stat.count,
                    'revenue': float(stat.revenue or 0)
                }
                for stat in order_stats
            ],
            'categoryStats': [
                {
                    'name': stat.name,
                    'count': stat.count
                }
                for stat in category_stats
            ]
        })

    except Exception as e:
        current_app.logger.error(f"获取统计数据失败: {str(e)}")
        return error_response('SYS_001', '获取统计数据失败', http_code=500)
