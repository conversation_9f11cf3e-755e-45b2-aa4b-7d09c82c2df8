package com.second.hand.data.model

import com.google.gson.annotations.SerializedName

/**
 * 对话数据模型
 * 用于消息列表页面显示对话信息
 */
data class Conversation(
    @SerializedName("other_user")
    val otherUser: User,
    
    @SerializedName("last_message")
    val lastMessage: Message? = null,
    
    @SerializedName("unread_count")
    val unreadCount: Int = 0,
    
    @SerializedName("product")
    val product: Product? = null
) {
    /**
     * 获取对话显示名称
     */
    fun getDisplayName(): String {
        return otherUser.getDisplayName()
    }
    
    /**
     * 获取最后一条消息内容
     */
    fun getLastMessageContent(): String {
        return lastMessage?.content ?: "暂无消息"
    }
    
    /**
     * 获取最后一条消息时间
     */
    fun getLastMessageTime(): String {
        return lastMessage?.createdAt ?: ""
    }
    
    /**
     * 是否有未读消息
     */
    fun hasUnreadMessages(): Boolean {
        return unreadCount > 0
    }
    
    /**
     * 获取未读消息数量显示文本
     */
    fun getUnreadCountDisplay(): String {
        return when {
            unreadCount == 0 -> ""
            unreadCount > 99 -> "99+"
            else -> unreadCount.toString()
        }
    }
}
