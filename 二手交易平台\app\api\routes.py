#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由
"""

from flask import jsonify, request
from flask_login import login_required, current_user
from app.api import bp
from app.models import Product, User, Order, Notification
from app import db

@bp.route('/products/<int:id>/favorite', methods=['POST'])
@login_required
def toggle_favorite(id):
    """切换商品收藏状态"""
    try:
        product = Product.query.get_or_404(id)

        from app.models import Favorite
        favorite = Favorite.query.filter_by(
            user_id=current_user.id,
            product_id=product.id
        ).first()

        if favorite:
            # 取消收藏
            db.session.delete(favorite)
            # 确保收藏数不会小于0
            if product.favorite_count > 0:
                product.favorite_count -= 1
            is_favorited = False
            message = '已取消收藏'
        else:
            # 添加收藏
            favorite = Favorite(user_id=current_user.id, product_id=product.id)
            db.session.add(favorite)
            product.favorite_count += 1
            is_favorited = True
            message = '已添加收藏'

        db.session.commit()

        return jsonify({
            'success': True,
            'is_favorited': is_favorited,
            'favorite_count': product.favorite_count,
            'message': message
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '操作失败，请重试'
        }), 500

@bp.route('/notifications')
@login_required
def get_notifications():
    """获取用户通知"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    notifications = Notification.query.filter_by(user_id=current_user.id)\
        .order_by(Notification.created_at.desc())\
        .paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'notifications': [{
            'id': n.id,
            'title': n.title,
            'content': n.content,
            'type': n.type,
            'is_read': n.is_read,
            'created_at': n.created_at.isoformat(),
            'related_id': n.related_id
        } for n in notifications.items],
        'has_next': notifications.has_next,
        'has_prev': notifications.has_prev,
        'page': notifications.page,
        'pages': notifications.pages,
        'total': notifications.total
    })

@bp.route('/notifications/<int:id>/read', methods=['POST'])
@login_required
def mark_notification_read(id):
    """标记通知为已读"""
    try:
        notification = Notification.query.filter_by(
            id=id,
            user_id=current_user.id
        ).first()

        if not notification:
            return jsonify({'success': False, 'message': '通知不存在或无权限访问'}), 404

        if notification.is_read:
            return jsonify({'success': True, 'message': '通知已经是已读状态'})

        notification.is_read = True
        db.session.commit()

        return jsonify({'success': True, 'message': '通知已标记为已读'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '操作失败，请重试'}), 500

@bp.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """标记所有通知为已读"""
    try:
        Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).update({'is_read': True})

        db.session.commit()

        return jsonify({'success': True, 'message': '所有通知已标记为已读'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '操作失败，请重试'}), 500

@bp.route('/notifications/<int:id>/unread', methods=['POST'])
@login_required
def mark_notification_unread(id):
    """标记通知为未读"""
    try:
        notification = Notification.query.filter_by(
            id=id,
            user_id=current_user.id
        ).first_or_404()

        notification.is_read = False
        db.session.commit()

        return jsonify({'success': True, 'message': '通知已标记为未读'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '操作失败，请重试'}), 500

@bp.route('/notifications/<int:id>', methods=['DELETE'])
@login_required
def delete_notification(id):
    """删除通知"""
    try:
        notification = Notification.query.filter_by(
            id=id,
            user_id=current_user.id
        ).first_or_404()

        db.session.delete(notification)
        db.session.commit()

        return jsonify({'success': True, 'message': '通知已删除'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '删除失败，请重试'}), 500

@bp.route('/messages/unread_count')
@login_required
def get_unread_message_count():
    """获取未读消息数量"""
    try:
        from app.models import Message
        count = Message.query.filter(
            Message.receiver_id == current_user.id,
            Message.is_read == False,
            Message.deleted_by_receiver == False  # 排除接收方已删除的消息
        ).count()

        return jsonify({'success': True, 'count': count})
    except Exception as e:
        return jsonify({'success': False, 'count': 0})

@bp.route('/user/heartbeat', methods=['POST'])
@login_required
def user_heartbeat():
    """用户心跳API，用于更新在线状态"""
    try:
        from datetime import datetime
        current_user.last_seen = datetime.now()
        db.session.commit()
        return jsonify({'success': True, 'timestamp': current_user.last_seen.strftime('%Y-%m-%d %H:%M:%S')})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '更新失败'})

@bp.route('/user/offline', methods=['POST'])
def user_offline():
    """用户离线API"""
    try:
        from flask_login import current_user
        if current_user.is_authenticated:
            from datetime import datetime, timedelta
            # 将最后活跃时间设为15分钟前，确保立即从在线列表中移除
            current_user.last_seen = datetime.now() - timedelta(minutes=15)
            db.session.commit()
            return jsonify({'success': True})
        else:
            return jsonify({'success': True, 'message': '用户未登录'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/search/suggestions')
def search_suggestions():
    """搜索建议"""
    query = request.args.get('q', '').strip()
    
    if not query or len(query) < 2:
        return jsonify({'suggestions': []})
    
    # 搜索商品标题
    products = Product.query.filter(
        Product.title.contains(query),
        Product.status == 'active'
    ).limit(5).all()
    
    suggestions = [product.title for product in products]
    
    return jsonify({'suggestions': suggestions})

@bp.route('/stats/dashboard')
@login_required
def dashboard_stats():
    """用户仪表板统计"""
    if not current_user.is_admin():
        # 普通用户统计
        my_products = Product.query.filter_by(seller_id=current_user.id).count()
        my_orders_buy = Order.query.filter_by(buyer_id=current_user.id).count()
        my_orders_sell = Order.query.filter_by(seller_id=current_user.id).count()
        unread_notifications = Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).count()
        
        return jsonify({
            'my_products': my_products,
            'my_orders_buy': my_orders_buy,
            'my_orders_sell': my_orders_sell,
            'unread_notifications': unread_notifications
        })
    else:
        # 管理员统计
        from app.models import ProductStatus, OrderStatus
        total_users = User.query.count()
        total_products = Product.query.count()
        pending_products = Product.query.filter_by(status=ProductStatus.PENDING).count()
        total_orders = Order.query.count()
        
        return jsonify({
            'total_users': total_users,
            'total_products': total_products,
            'pending_products': pending_products,
            'total_orders': total_orders
        })

@bp.route('/products/<int:product_id>/view', methods=['POST'])
def increment_product_view(product_id):
    """异步增加商品浏览量 - 优化版本"""
    try:
        from app.models import Product
        import logging

        # 设置日志记录
        logger = logging.getLogger(__name__)

        # 检查商品是否存在
        product = Product.query.get(product_id)
        if not product:
            logger.warning(f"尝试访问不存在的商品ID: {product_id}")
            return jsonify({'success': False, 'message': '商品不存在'})

        # 每次访问都增加浏览量，无任何限制
        logger.info(f"用户访问商品 {product_id}，增加浏览量")

        # 使用原子操作更新浏览量
        try:
            # 使用数据库级别的原子更新，避免并发问题
            result = db.session.execute(
                "UPDATE products SET view_count = view_count + 1 WHERE id = :id",
                {'id': product_id}
            )

            if result.rowcount == 0:
                logger.error(f"更新商品 {product_id} 浏览量失败：商品不存在")
                return jsonify({'success': False, 'message': '更新失败：商品不存在'})

            db.session.commit()

            # 获取更新后的浏览量
            updated_product = Product.query.get(product_id)

            logger.info(f"成功更新商品 {product_id} 浏览量至 {updated_product.view_count}")

            return jsonify({
                'success': True,
                'view_count': updated_product.view_count,
                'message': '浏览量已更新'
            })

        except Exception as db_error:
            db.session.rollback()
            logger.error(f"数据库更新商品 {product_id} 浏览量失败: {str(db_error)}")
            raise db_error

    except Exception as e:
        db.session.rollback()
        logger.error(f"更新商品 {product_id} 浏览量时发生错误: {str(e)}")
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})
