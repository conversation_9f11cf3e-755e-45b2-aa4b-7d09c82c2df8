#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sqlite3
import os

def check_database_schema():
    """检查数据库表结构"""
    db_path = 'app/app.db'
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 数据库表结构检查 ===\n")
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("数据库中的表:")
        for table in tables:
            table_name = table[0]
            print(f"\n📋 表名: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print("  字段结构:")
            for col in columns:
                print(f"    - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")
        
        # 检查分类数据
        print("\n=== 分类数据 ===")
        cursor.execute("SELECT * FROM categories LIMIT 5")
        categories = cursor.fetchall()
        for cat in categories:
            print(f"  - {cat}")
        
        # 检查商品数据
        print("\n=== 商品数据 ===")
        cursor.execute("SELECT COUNT(*) FROM products")
        product_count = cursor.fetchone()[0]
        print(f"商品总数: {product_count}")
        
        if product_count > 0:
            cursor.execute("SELECT * FROM products LIMIT 3")
            products = cursor.fetchall()
            for prod in products:
                print(f"  - {prod}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

if __name__ == '__main__':
    check_database_schema()
