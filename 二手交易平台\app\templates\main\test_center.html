{% extends "base.html" %}

{% block content %}
<div class="container">
    <h2 class="mb-4">头像居中测试</h2>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">中文字符居中效果测试</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">网格线帮助观察字符是否完美居中</p>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h6>不同Y偏移值的"系"字头像</h6>
                            <div class="d-flex flex-wrap gap-3 mb-4">
                                {% for offset in [-10, -5, 0, 5, 10, 15] %}
                                <div class="text-center">
                                    <img src="{{ url_for('main.test_center_avatar', char='系', y_offset=offset) }}" 
                                         class="rounded-circle" 
                                         style="width: 100px; height: 100px;" 
                                         alt="系">
                                    <div class="small mt-1">Y偏移: {{ offset }}</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h6>不同中文字符的居中效果（Y偏移=10）</h6>
                            <div class="d-flex flex-wrap gap-3 mb-4">
                                {% for char in ['系', '管', '理', '员', '张', '李', '王', '陈'] %}
                                <div class="text-center">
                                    <img src="{{ url_for('main.test_center_avatar', char=char, y_offset=10) }}" 
                                         class="rounded-circle" 
                                         style="width: 80px; height: 80px;" 
                                         alt="{{ char }}">
                                    <div class="small mt-1">{{ char }}</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h6>英文字符对比（Y偏移=0）</h6>
                            <div class="d-flex flex-wrap gap-3 mb-4">
                                {% for char in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'] %}
                                <div class="text-center">
                                    <img src="{{ url_for('main.test_center_avatar', char=char, y_offset=0) }}" 
                                         class="rounded-circle" 
                                         style="width: 80px; height: 80px;" 
                                         alt="{{ char }}">
                                    <div class="small mt-1">{{ char }}</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">实时调整工具</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="testChar" class="form-label">测试字符</label>
                                <input type="text" class="form-control" id="testChar" value="系" maxlength="1">
                            </div>
                            <div class="mb-3">
                                <label for="yOffset" class="form-label">Y偏移值: <span id="offsetValue">10</span></label>
                                <input type="range" class="form-range" id="yOffset" min="-20" max="20" value="10">
                            </div>
                            <button class="btn btn-primary" onclick="updateTestAvatar()">更新头像</button>
                        </div>
                        <div class="col-md-6 text-center">
                            <div id="testAvatarContainer">
                                <img id="testAvatar" 
                                     src="{{ url_for('main.test_center_avatar', char='系', y_offset=10) }}" 
                                     class="rounded-circle" 
                                     style="width: 150px; height: 150px;" 
                                     alt="测试头像">
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">实时预览</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 居中优化说明</h6>
                <ul class="mb-0">
                    <li><strong>PIL方法</strong>：使用 <code>anchor='mm'</code> 实现中心锚点定位</li>
                    <li><strong>SVG方法</strong>：使用 <code>text-anchor="middle"</code> 和 <code>dominant-baseline="middle"</code></li>
                    <li><strong>Canvas方法</strong>：使用 <code>textAlign='center'</code> 和 <code>textBaseline='middle'</code></li>
                    <li><strong>中文优化</strong>：中文字符通常需要向上偏移5-10像素以获得更好的视觉居中效果</li>
                    <li><strong>网格线</strong>：帮助观察字符是否真正居中对齐</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function updateTestAvatar() {
    const char = document.getElementById('testChar').value || '系';
    const offset = document.getElementById('yOffset').value;
    const avatar = document.getElementById('testAvatar');
    
    avatar.src = `/test_center_avatar/${encodeURIComponent(char)}/${offset}`;
}

document.getElementById('yOffset').addEventListener('input', function() {
    document.getElementById('offsetValue').textContent = this.value;
});

document.getElementById('testChar').addEventListener('input', function() {
    if (this.value.length > 1) {
        this.value = this.value.charAt(0);
    }
});
</script>
{% endblock %}
