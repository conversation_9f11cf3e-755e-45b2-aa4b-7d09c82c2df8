{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>商品状态切换调试</h2>
    
    <div class="card">
        <div class="card-body">
            <h5>测试商品状态切换</h5>
            
            <div class="mb-3">
                <label for="productId" class="form-label">商品ID:</label>
                <input type="number" class="form-control" id="productId" value="1">
            </div>
            
            <div class="mb-3">
                <label for="newStatus" class="form-label">新状态:</label>
                <select class="form-control" id="newStatus">
                    <option value="active">在售</option>
                    <option value="inactive">已下架</option>
                    <option value="pending">待审核</option>
                    <option value="sold">已售出</option>
                </select>
            </div>
            
            <button class="btn btn-primary" onclick="testToggleStatus()">测试状态切换</button>
            <button class="btn btn-info" onclick="testWithJSON()">测试JSON格式</button>
            
            <div class="mt-3">
                <h6>调试信息:</h6>
                <pre id="debugInfo" class="bg-light p-3" style="min-height: 200px;"></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addDebugInfo(info) {
    const debugDiv = document.getElementById('debugInfo');
    const timestamp = new Date().toLocaleTimeString();
    debugDiv.textContent += `[${timestamp}] ${info}\n`;
}

function testToggleStatus() {
    const productId = document.getElementById('productId').value;
    const newStatus = document.getElementById('newStatus').value;
    
    addDebugInfo(`开始测试 - 商品ID: ${productId}, 新状态: ${newStatus}`);
    
    $.ajax({
        url: `/admin_panel/products/toggle_status/${productId}`,
        method: 'POST',
        data: { 
            status: newStatus,
            csrf_token: $('meta[name=csrf-token]').attr('content')
        },
        beforeSend: function(xhr, settings) {
            addDebugInfo(`发送请求 - URL: ${settings.url}`);
            addDebugInfo(`请求数据: ${JSON.stringify(settings.data)}`);
        },
        success: function(response) {
            addDebugInfo(`成功响应: ${JSON.stringify(response)}`);
        },
        error: function(xhr, status, error) {
            addDebugInfo(`错误响应 - 状态: ${xhr.status}`);
            addDebugInfo(`错误信息: ${error}`);
            addDebugInfo(`响应文本: ${xhr.responseText}`);
        }
    });
}

function testWithJSON() {
    const productId = document.getElementById('productId').value;
    const newStatus = document.getElementById('newStatus').value;
    
    addDebugInfo(`开始JSON测试 - 商品ID: ${productId}, 新状态: ${newStatus}`);
    
    $.ajax({
        url: `/admin_panel/products/toggle_status/${productId}`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ 
            status: newStatus
        }),
        beforeSend: function(xhr, settings) {
            addDebugInfo(`发送JSON请求 - URL: ${settings.url}`);
            addDebugInfo(`请求数据: ${settings.data}`);
        },
        success: function(response) {
            addDebugInfo(`JSON成功响应: ${JSON.stringify(response)}`);
        },
        error: function(xhr, status, error) {
            addDebugInfo(`JSON错误响应 - 状态: ${xhr.status}`);
            addDebugInfo(`错误信息: ${error}`);
            addDebugInfo(`响应文本: ${xhr.responseText}`);
        }
    });
}

// 清空调试信息
function clearDebug() {
    document.getElementById('debugInfo').textContent = '';
}

// 页面加载时清空调试信息
$(document).ready(function() {
    addDebugInfo('调试页面已加载');
    addDebugInfo(`CSRF Token: ${$('meta[name=csrf-token]').attr('content')}`);
});
</script>
{% endblock %}
