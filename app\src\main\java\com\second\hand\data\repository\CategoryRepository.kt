package com.second.hand.data.repository

import com.second.hand.data.api.ApiService
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.model.Category
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 分类数据仓库
 * 处理商品分类相关的数据操作
 */
@Singleton
class CategoryRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    /**
     * 获取分类列表
     */
    suspend fun getCategories(): NetworkResult<List<Category>> {
        return safeApiCall { apiService.getCategories() }
    }

    /**
     * 获取分类详情
     */
    suspend fun getCategory(categoryId: Int): NetworkResult<Category> {
        return safeApiCall { apiService.getCategory(categoryId) }
    }
}
