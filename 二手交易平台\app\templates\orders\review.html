{% extends "base.html" %}

{% block content %}
<div class="page-transition">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- 页面标题 -->
                <div class="mb-4">
                    <h2><i class="fas fa-star text-warning"></i> 评价订单</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('orders.my_orders') }}">我的订单</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('orders.detail', id=order.id) }}">订单详情</a></li>
                            <li class="breadcrumb-item active">评价订单</li>
                        </ol>
                    </nav>
                </div>

                <!-- 订单信息卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-receipt"></i> 订单信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>订单号：</strong></td>
                                        <td><code>{{ order.order_no }}</code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建时间：</strong></td>
                                        <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>订单金额：</strong></td>
                                        <td><span class="text-danger fw-bold">¥{{ "%.2f"|format(order.total_amount) }}</span></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>卖家：</strong></td>
                                        <td>{{ order.seller.nickname or order.seller.username }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>状态：</strong></td>
                                        <td><span class="badge bg-success">已完成</span></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-box"></i> 商品信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <img src="{{ order.product.get_main_image_url() }}"
                                     class="img-fluid rounded" alt="{{ order.product.title }}"
                                     style="height: 150px; object-fit: cover;">
                            </div>
                            <div class="col-md-9">
                                <h5>{{ order.product.title }}</h5>
                                <p class="text-muted">{{ order.product.description[:200] }}{% if order.product.description|length > 200 %}...{% endif %}</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">单价：</small>
                                        <span class="fw-bold">¥{{ "%.2f"|format(order.price) }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">数量：</small>
                                        <span class="fw-bold">{{ order.quantity }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评价表单 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-star text-warning"></i> 评价此次交易</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <div class="mb-4">
                                {{ form.rating.label(class="form-label") }}
                                <div class="rating-container">
                                    {% for value, label in form.rating.choices %}
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="rating" id="rating{{ value }}" value="{{ value }}" 
                                               {% if form.rating.data == value %}checked{% endif %}>
                                        <label class="form-check-label" for="rating{{ value }}">
                                            <span class="stars">
                                                {% for i in range(1, 6) %}
                                                    {% if i <= value %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="far fa-star text-muted"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </span>
                                            <span class="ms-2">{{ label }}</span>
                                        </label>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% if form.rating.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.rating.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-4">
                                {{ form.content.label(class="form-label") }}
                                {{ form.content(class="form-control" + (" is-invalid" if form.content.errors else ""), rows="5", placeholder="请分享您的购买体验，您的评价对其他买家很有帮助...") }}
                                {% if form.content.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.content.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> 
                                    评价内容将公开显示，请文明用语
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('orders.detail', id=order.id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> 返回订单详情
                                </a>
                                <div>
                                    <a href="{{ url_for('orders.my_orders') }}" class="btn btn-outline-info me-2">
                                        <i class="fas fa-list"></i> 我的订单
                                    </a>
                                    {{ form.submit(class="btn btn-warning") }}
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 评价说明 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-question-circle"></i> 评价说明</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        <strong>5星：</strong>非常满意，商品质量优秀
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        <strong>4星：</strong>满意，商品符合预期
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        <strong>3星：</strong>一般，商品基本可用
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        <strong>2星：</strong>不满意，商品有明显问题
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        <strong>1星：</strong>非常不满意，商品严重不符
                                    </li>
                                </ul>
                                <div class="alert alert-info small">
                                    <i class="fas fa-info-circle me-1"></i>
                                    您的评价将帮助其他买家做出更好的选择
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.rating-container .form-check {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.rating-container .form-check:hover {
    background-color: #f8f9fa;
}

.rating-container .form-check-input:checked + .form-check-label {
    font-weight: 600;
}

.stars {
    font-size: 1.1em;
}

.btn {
    border-radius: 0.375rem;
}

.btn i {
    margin-right: 0.25rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 评分选择交互
    const ratingInputs = document.querySelectorAll('input[name="rating"]');
    ratingInputs.forEach(input => {
        input.addEventListener('change', function() {
            // 移除所有选中状态的样式
            document.querySelectorAll('.rating-container .form-check').forEach(check => {
                check.classList.remove('bg-light');
            });
            
            // 为选中的评分添加样式
            if (this.checked) {
                this.closest('.form-check').classList.add('bg-light');
            }
        });
    });
    
    // 初始化已选中的评分样式
    const checkedRating = document.querySelector('input[name="rating"]:checked');
    if (checkedRating) {
        checkedRating.closest('.form-check').classList.add('bg-light');
    }
});
</script>
{% endblock %}
