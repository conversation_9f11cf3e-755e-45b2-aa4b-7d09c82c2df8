{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-shopping-cart"></i> 订单管理</h2>
</div>

<!-- 筛选器 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">订单状态</label>
                <select name="status" class="form-select" onchange="this.form.submit()">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>全部</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>待付款</option>
                    <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>已付款</option>
                    <option value="shipped" {% if status_filter == 'shipped' %}selected{% endif %}>已发货</option>
                    <option value="delivered" {% if status_filter == 'delivered' %}selected{% endif %}>已送达</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>已完成</option>
                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>已取消</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">时间范围</label>
                <select name="time_range" class="form-select" onchange="this.form.submit()">
                    <option value="all" {% if time_filter == 'all' %}selected{% endif %}>全部时间</option>
                    <option value="today" {% if time_filter == 'today' %}selected{% endif %}>今天</option>
                    <option value="week" {% if time_filter == 'week' %}selected{% endif %}>本周</option>
                    <option value="month" {% if time_filter == 'month' %}selected{% endif %}>本月</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">搜索</label>
                <div class="input-group">
                    <input type="text" name="search" class="form-control" placeholder="订单号、买家、卖家..." 
                           value="{{ search_query or '' }}">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 订单列表 -->
{% if orders.items %}
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>订单号</th>
                            <th>商品</th>
                            <th>买家</th>
                            <th>卖家</th>
                            <th>金额</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        <tr>
                            <td>
                                <code>{{ order.order_no }}</code>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ order.product.get_main_image_url() }}"
                                         class="rounded me-2" width="40" height="40" style="object-fit: cover;">
                                    <div>
                                        <div class="fw-bold">{{ order.product.title[:30] }}{% if order.product.title|length > 30 %}...{% endif %}</div>
                                        <small class="text-muted">数量: {{ order.quantity }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ order.buyer.nickname or order.buyer.username }}</div>
                                    <small class="text-muted">{{ order.buyer.email }}</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ order.seller.nickname or order.seller.username }}</div>
                                    <small class="text-muted">{{ order.seller.email }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold text-danger">¥{{ "%.2f"|format(order.total_amount) }}</span>
                            </td>
                            <td>
                                {% if order.status.value == 'pending' %}
                                    <span class="badge bg-warning">待付款</span>
                                {% elif order.status.value == 'paid' %}
                                    <span class="badge bg-info">已付款</span>
                                {% elif order.status.value == 'shipped' %}
                                    <span class="badge bg-primary">已发货</span>
                                {% elif order.status.value == 'delivered' %}
                                    <span class="badge bg-success">已送达</span>
                                {% elif order.status.value == 'completed' %}
                                    <span class="badge bg-success">已完成</span>
                                {% elif order.status.value == 'cancelled' %}
                                    <span class="badge bg-danger">已取消</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>{{ order.created_at.strftime('%Y-%m-%d') }}</div>
                                <small class="text-muted">{{ order.created_at.strftime('%H:%M') }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('admin_panel.order_detail', id=order.id) }}" 
                                       class="btn btn-outline-primary" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if order.status.value in ['pending', 'paid', 'shipped'] %}
                                    <a href="{{ url_for('admin_panel.edit_order', id=order.id) }}" 
                                       class="btn btn-outline-secondary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if orders.pages > 1 %}
    <nav aria-label="订单分页" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if orders.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin_panel.orders', page=orders.prev_num, status=status_filter, time_range=time_filter, search=search_query) }}">上一页</a>
                </li>
            {% endif %}
            
            {% for page_num in orders.iter_pages() %}
                {% if page_num %}
                    {% if page_num != orders.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin_panel.orders', page=page_num, status=status_filter, time_range=time_filter, search=search_query) }}">{{ page_num }}</a>
                        </li>
                    {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                    {% endif %}
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if orders.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin_panel.orders', page=orders.next_num, status=status_filter, time_range=time_filter, search=search_query) }}">下一页</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

{% else %}
    <div class="text-center py-5">
        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">暂无订单</h4>
        <p class="text-muted">还没有任何订单记录</p>
    </div>
{% endif %}

<!-- 统计信息 -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ total_orders or 0 }}</h5>
                <p class="card-text">总订单数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">¥{{ "%.2f"|format(total_amount or 0) }}</h5>
                <p class="card-text">总交易额</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ pending_orders or 0 }}</h5>
                <p class="card-text">待处理订单</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ completed_orders or 0 }}</h5>
                <p class="card-text">已完成订单</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}