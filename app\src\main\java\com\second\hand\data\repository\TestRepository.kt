package com.second.hand.data.repository

import com.second.hand.data.api.ApiService
import com.second.hand.data.api.NetworkResult
import com.second.hand.data.model.Category
import com.second.hand.data.model.Product
import com.second.hand.data.model.PagedResponse
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 测试Repository
 * 用于验证网络请求框架的功能
 */
@Singleton
class TestRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {
    
    /**
     * 测试获取分类列表
     */
    suspend fun getCategories(): NetworkResult<List<Category>> {
        return safeApiCallList {
            apiService.getCategories()
        }
    }
    
    /**
     * 测试获取商品列表
     */
    suspend fun getProducts(
        page: Int = 1,
        perPage: Int = 10,
        categoryId: Int? = null,
        search: String? = null
    ): NetworkResult<PagedResponse<Product>> {
        return safeApiCall {
            apiService.getProducts(page, perPage, categoryId, search = search ?: "")
        }
    }
    
    /**
     * 测试获取商品详情
     */
    suspend fun getProduct(id: Int): NetworkResult<Product> {
        return safeApiCall {
            apiService.getProduct(id)
        }
    }
    
    /**
     * 测试网络连接
     * 通过调用商品列表接口来验证网络连接（不需要认证）
     */
    suspend fun testConnection(): NetworkResult<Boolean> {
        return try {
            println("开始测试网络连接...")
            val result = getProducts(page = 1, perPage = 1)
            when (result) {
                is NetworkResult.Success -> {
                    println("网络连接测试成功")
                    NetworkResult.Success(true)
                }
                is NetworkResult.Error -> {
                    println("网络连接测试失败: ${result.message}")
                    NetworkResult.Error("连接测试失败: ${result.message}")
                }
                is NetworkResult.Loading -> {
                    println("网络连接测试超时")
                    NetworkResult.Error("连接测试超时")
                }
            }
        } catch (e: Exception) {
            println("网络连接测试异常: ${e.message}")
            e.printStackTrace()
            NetworkResult.Error("连接测试异常: ${e.message}")
        }
    }

    /**
     * 简单的连接测试 - 直接调用商品列表API
     */
    suspend fun simpleConnectionTest(): NetworkResult<String> {
        return try {
            println("执行简单连接测试...")
            val response = apiService.getProducts(page = 1, limit = 1)
            println("API调用完成，响应码: ${response.code()}")

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    NetworkResult.Success("连接成功 - 响应: ${body.message}")
                } else {
                    NetworkResult.Error("连接成功但响应体为空")
                }
            } else {
                val errorBody = response.errorBody()?.string()
                NetworkResult.Error("HTTP错误: ${response.code()}, 详情: $errorBody")
            }
        } catch (e: Exception) {
            println("简单连接测试异常: ${e.message}")
            e.printStackTrace()
            NetworkResult.Error("连接异常: ${e.message}")
        }
    }

    /**
     * 网络连通性测试
     */
    suspend fun networkConnectivityTest(): NetworkResult<String> {
        return try {
            println("开始网络连通性测试...")

            // 测试基本网络连接
            val url = java.net.URL("http://192.168.31.52:5000/")
            val connection = url.openConnection() as java.net.HttpURLConnection
            connection.connectTimeout = 10000
            connection.readTimeout = 10000
            connection.requestMethod = "GET"

            val responseCode = connection.responseCode
            val responseMessage = connection.responseMessage

            connection.disconnect()

            if (responseCode == 200) {
                NetworkResult.Success("网络连通性测试成功: $responseCode $responseMessage")
            } else {
                NetworkResult.Error("网络连通性测试失败: $responseCode $responseMessage")
            }
        } catch (e: Exception) {
            println("网络连通性测试异常: ${e.message}")
            e.printStackTrace()
            NetworkResult.Error("网络连通性测试异常: ${e.message}")
        }
    }
}
