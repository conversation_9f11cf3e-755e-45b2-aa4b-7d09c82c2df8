{% extends "base.html" %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h2 class="card-title mb-3">商品不可用</h2>
                    
                    {% if product %}
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">{{ product.title }}</h5>
                        <p class="mb-0">
                            {% if product.status.value == 'pending' %}
                            该商品正在审核中，暂时无法查看。
                            {% elif product.status.value == 'inactive' %}
                            该商品已被下架，暂时无法查看。
                            {% elif product.status.value == 'sold' %}
                            该商品已售出，暂时无法查看。
                            {% else %}
                            该商品当前不可用。
                            {% endif %}
                        </p>
                    </div>
                    {% else %}
                    <div class="alert alert-danger">
                        <p class="mb-0">抱歉，您要查看的商品不存在或已被删除。</p>
                    </div>
                    {% endif %}
                    
                    <div class="mt-4">
                        <a href="{{ url_for('main.index') }}" class="btn btn-primary me-2">
                            <i class="fas fa-home me-1"></i>返回首页
                        </a>
                        <a href="{{ url_for('main.search') }}" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>浏览商品
                        </a>
                    </div>
                    
                    {% if current_user.is_authenticated and product and current_user.id == product.seller_id %}
                    <div class="mt-3">
                        <hr>
                        <p class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            这是您发布的商品，您可以在"我的商品"中管理它。
                        </p>
                        <a href="{{ url_for('products.my_products') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-box me-1"></i>我的商品
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.alert {
    border-radius: 0.5rem;
}

.btn {
    border-radius: 0.375rem;
}
</style>
{% endblock %}
