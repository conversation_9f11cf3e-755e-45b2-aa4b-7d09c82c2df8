{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-comment-dots"></i> 反馈详情</h5>
                <div>
                    {% if feedback.status == 'pending' %}
                    <span class="badge bg-warning">待处理</span>
                    {% elif feedback.status == 'processing' %}
                    <span class="badge bg-info">处理中</span>
                    {% elif feedback.status == 'waiting_user' %}
                    <span class="badge bg-primary">等待用户回复</span>
                    {% elif feedback.status == 'resolved' %}
                    <span class="badge bg-success">已解决</span>
                    {% elif feedback.status == 'closed' %}
                    <span class="badge bg-secondary">已关闭</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <!-- 反馈标题 -->
                <div class="mb-4">
                    <h4>{{ feedback.title }}</h4>
                    <div class="text-muted small mb-3">
                        <span class="me-3">
                            <i class="fas fa-calendar"></i> {{ feedback.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </span>
                        <span class="me-3">
                            <i class="fas fa-tag"></i> 
                            {% if feedback.type == 'bug' %}问题反馈
                            {% elif feedback.type == 'suggestion' %}功能建议
                            {% elif feedback.type == 'complaint' %}投诉举报
                            {% elif feedback.type == 'contact' %}联系我们
                            {% else %}其他{% endif %}
                        </span>
                        <span>
                            <i class="fas fa-user"></i>
                            {% if feedback.user %}
                                {{ feedback.user.nickname or feedback.user.username }}
                            {% else %}
                                访客用户
                            {% endif %}
                        </span>
                    </div>
                </div>

                <!-- 反馈内容 -->
                <div class="mb-4">
                    <h6 class="text-primary">反馈内容：</h6>
                    <div class="border-start border-primary border-3 ps-3">
                        <p class="mb-0">{{ feedback.content|nl2br|safe }}</p>
                    </div>
                </div>

                <!-- 管理员回复 -->
                {% if feedback.admin_reply %}
                <div class="mt-4 p-4 bg-light rounded">
                    <h6 class="text-success mb-3">
                        <i class="fas fa-reply"></i> 管理员回复
                    </h6>
                    <p class="mb-2">{{ feedback.admin_reply|nl2br|safe }}</p>
                    {% if feedback.admin %}
                    <div class="text-muted small">
                        <i class="fas fa-user-shield"></i> {{ feedback.admin.nickname or feedback.admin.username }}
                        <span class="ms-2">
                            <i class="fas fa-clock"></i> {{ feedback.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </span>
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 尚未回复此反馈
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('admin_panel.feedbacks') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    <div>
                        <a href="{{ url_for('admin_panel.update_feedback_status', id=feedback.id) }}" class="btn btn-outline-info me-2">
                            <i class="fas fa-edit"></i> 更新状态
                        </a>
                        {% if feedback.status != 'resolved' %}
                        <a href="{{ url_for('admin_panel.reply_feedback', id=feedback.id) }}" class="btn btn-primary">
                            <i class="fas fa-reply"></i> 回复反馈
                        </a>
                        {% else %}
                        <a href="{{ url_for('admin_panel.reply_feedback', id=feedback.id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> 编辑回复
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- 用户信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user"></i> 用户信息</h6>
            </div>
            <div class="card-body">
                {% if feedback.user %}
                <div class="d-flex align-items-center mb-3">
                    <img src="{{ feedback.user.get_avatar_url() }}"
                         class="rounded-circle me-3"
                         width="48" height="48" alt="头像">
                    <div>
                        <h6 class="mb-0">{{ feedback.user.nickname or feedback.user.username }}</h6>
                        <small class="text-muted">@{{ feedback.user.username }}</small>
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-4"><strong>邮箱:</strong></div>
                    <div class="col-8">{{ feedback.user.email }}</div>
                </div>

                {% if feedback.user.phone %}
                <div class="row mb-2">
                    <div class="col-4"><strong>电话:</strong></div>
                    <div class="col-8">{{ feedback.user.phone }}</div>
                </div>
                {% endif %}

                <div class="row mb-2">
                    <div class="col-4"><strong>角色:</strong></div>
                    <div class="col-8">
                        {% if feedback.user.role.value == 'admin' %}
                        <span class="badge bg-danger">管理员</span>
                        {% else %}
                        <span class="badge bg-primary">用户</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-4"><strong>状态:</strong></div>
                    <div class="col-8">
                        {% if feedback.user.is_active %}
                        <span class="badge bg-success">正常</span>
                        {% else %}
                        <span class="badge bg-secondary">禁用</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-4"><strong>注册:</strong></div>
                    <div class="col-8">{{ feedback.user.created_at.strftime('%Y-%m-%d') }}</div>
                </div>
                {% else %}
                <!-- 访客用户信息 -->
                <div class="d-flex align-items-center mb-3">
                    <img src="/static/images/default-avatar.png"
                         class="rounded-circle me-3"
                         width="48" height="48" alt="头像">
                    <div>
                        <h6 class="mb-0">访客用户</h6>
                        <small class="text-muted">未注册用户</small>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    此反馈来自未注册的访客用户，联系信息请查看反馈内容。
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 反馈统计 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 反馈统计</h6>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-6"><strong>反馈类型:</strong></div>
                    <div class="col-6">
                        {% if feedback.type == 'bug' %}
                        <span class="badge bg-danger">Bug报告</span>
                        {% elif feedback.type == 'suggestion' %}
                        <span class="badge bg-info">建议</span>
                        {% elif feedback.type == 'complaint' %}
                        <span class="badge bg-warning">投诉</span>
                        {% elif feedback.type == 'contact' %}
                        <span class="badge bg-primary">联系我们</span>
                        {% else %}
                        <span class="badge bg-secondary">其他</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-6"><strong>处理状态:</strong></div>
                    <div class="col-6">
                        {% if feedback.status == 'pending' %}
                        <span class="badge bg-warning">待处理</span>
                        {% elif feedback.status == 'processing' %}
                        <span class="badge bg-info">处理中</span>
                        {% elif feedback.status == 'waiting_user' %}
                        <span class="badge bg-primary">等待用户回复</span>
                        {% elif feedback.status == 'resolved' %}
                        <span class="badge bg-success">已解决</span>
                        {% elif feedback.status == 'closed' %}
                        <span class="badge bg-secondary">已关闭</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-6"><strong>提交时间:</strong></div>
                    <div class="col-8">{{ feedback.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                
                {% if feedback.updated_at != feedback.created_at %}
                <div class="row">
                    <div class="col-6"><strong>更新时间:</strong></div>
                    <div class="col-8">{{ feedback.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
