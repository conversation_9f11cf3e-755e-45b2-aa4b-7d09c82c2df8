#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API响应工具模块
"""

from flask import jsonify
from datetime import datetime


def success_response(data=None, message="操作成功", code=200):
    """成功响应"""
    response = {
        'success': True,
        'message': message,
        'code': code,
        'timestamp': datetime.utcnow().isoformat() + 'Z'
    }
    
    if data is not None:
        response['data'] = data
    
    return jsonify(response), code


def error_response(error_code, message, details=None, http_code=400):
    """错误响应"""
    response = {
        'success': False,
        'error': {
            'code': error_code,
            'message': message
        },
        'timestamp': datetime.utcnow().isoformat() + 'Z'
    }
    
    if details:
        response['error']['details'] = details
    
    return jsonify(response), http_code


def paginated_response(items, pagination, message="获取成功"):
    """分页响应"""
    data = {
        'items': items,
        'pagination': {
            'page': pagination.page,
            'limit': pagination.per_page,
            'total': pagination.total,
            'totalPages': pagination.pages,
            'hasNext': pagination.has_next,
            'hasPrev': pagination.has_prev
        }
    }
    
    return success_response(data, message)


def validation_error_response(errors):
    """验证错误响应"""
    return error_response(
        'VALID_002',
        '数据验证失败',
        errors,
        422
    )


# 常用错误响应
def unauthorized_response(message="未授权访问"):
    return error_response('AUTH_001', message, http_code=401)


def forbidden_response(message="无权限访问"):
    return error_response('AUTH_004', message, http_code=403)


def not_found_response(message="资源不存在"):
    return error_response('BIZ_001', message, http_code=404)


def server_error_response(message="服务器内部错误"):
    return error_response('SYS_001', message, http_code=500)
