{% extends "base.html" %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt"></i> 管理员仪表板</h2>
    <div class="btn-group">
        <a href="{{ url_for('admin_panel.statistics') }}" class="btn btn-outline-primary">
            <i class="fas fa-chart-bar"></i> 详细统计
        </a>
        <a href="{{ url_for('admin_panel.system_monitor') }}" class="btn btn-outline-info">
            <i class="fas fa-server"></i> 系统监控
        </a>
        <a href="{{ url_for('admin_panel.system_diagnostics') }}" class="btn btn-outline-warning">
            <i class="fas fa-stethoscope"></i> 系统诊断
        </a>
        <a href="{{ url_for('admin_panel.system_config') }}" class="btn btn-outline-secondary">
            <i class="fas fa-cog"></i> 系统设置
        </a>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stat-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-uppercase mb-1">总用户数</h6>
                        <h2 class="mb-0">{{ total_users }}</h2>
                        <small class="opacity-75">本周新增: +{{ new_users_week }}</small>
                    </div>
                    <div class="opacity-75">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stat-card success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-uppercase mb-1">总商品数</h6>
                        <h2 class="mb-0">{{ total_products }}</h2>
                        <small class="opacity-75">本周新增: +{{ new_products_week }}</small>
                    </div>
                    <div class="opacity-75">
                        <i class="fas fa-box fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stat-card warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-uppercase mb-1">总订单数</h6>
                        <h2 class="mb-0">{{ total_orders }}</h2>
                        <small class="opacity-75">本周新增: +{{ new_orders_week }}</small>
                    </div>
                    <div class="opacity-75">
                        <i class="fas fa-shopping-cart fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stat-card info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-uppercase mb-1">待审核商品</h6>
                        <h2 class="mb-0">{{ pending_products }}</h2>
                        <small class="opacity-75">
                            {% if pending_products > 0 %}
                            <a href="{{ url_for('admin_panel.products', status='pending') }}" class="text-white">
                                <i class="fas fa-arrow-right"></i> 去处理
                            </a>
                            {% else %}
                            全部已处理
                            {% endif %}
                        </small>
                    </div>
                    <div class="opacity-75">
                        <i class="fas fa-clock fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统状态 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>系统状态</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="h2 mb-0 text-success" id="online-users">{{ online_users }}</div>
                        <small class="text-muted">在线用户</small>
                    </div>
                    <div class="col-3">
                        <div class="h2 mb-0 text-primary" id="total-users">{{ total_users }}</div>
                        <small class="text-muted">总用户数</small>
                    </div>
                    <div class="col-3">
                        <div class="h2 mb-0 text-warning" id="total-products">{{ total_products }}</div>
                        <small class="text-muted">总商品数</small>
                    </div>
                    <div class="col-3">
                        <div class="h2 mb-0 text-info" id="total-orders">{{ total_orders }}</div>
                        <small class="text-muted">总订单数</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容区域 -->
<div class="row">
    <!-- 最近活动 -->
    <div class="col-lg-8">
        <!-- 最近用户 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-user-plus"></i> 最近注册用户</h5>
                <a href="{{ url_for('admin_panel.users') }}" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if recent_users %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>用户</th>
                                <th>邮箱</th>
                                <th>注册时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in recent_users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="{{ user.get_avatar_url() }}" class="rounded-circle me-2" 
                                             width="32" height="32" alt="头像">
                                        <div>
                                            <div class="fw-bold">{{ user.nickname or user.username }}</div>
                                            <small class="text-muted">@{{ user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ user.email }}</td>
                                <td>{{ moment(user.created_at).fromNow() }}</td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">正常</span>
                                    {% else %}
                                    <span class="badge bg-danger">禁用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('admin_panel.edit_user', id=user.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无用户数据</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- 最近商品 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-box"></i> 最近发布商品</h5>
                <a href="{{ url_for('admin_panel.products') }}" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if recent_products %}
                <div class="row">
                    {% for product in recent_products %}
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <img src="{{ product.get_main_image_url() }}"
                                 class="rounded me-3" width="60" height="60" style="object-fit: cover;" alt="{{ product.title }}">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="{{ url_for('products.detail', id=product.id) }}" class="text-decoration-none">
                                        {{ product.title[:25] }}{% if product.title|length > 25 %}...{% endif %}
                                    </a>
                                </h6>
                                <p class="text-danger fw-bold mb-1">¥{{ "%.2f"|format(product.price) }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ product.seller.username }}</small>
                                    <small>
                                        {% if product.status.value == 'pending' %}
                                        <span class="badge bg-warning">待审核</span>
                                        {% elif product.status.value == 'active' %}
                                        <span class="badge bg-success">在售</span>
                                        {% elif product.status.value == 'sold' %}
                                        <span class="badge bg-secondary">已售</span>
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-box fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无商品数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 快速操作 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> 快速操作</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-warning" onclick="clearCache()">
                        <i class="fas fa-broom me-1"></i>清理缓存
                    </button>
                    <button class="btn btn-info" onclick="backupDatabase()">
                        <i class="fas fa-database me-1"></i>备份数据库
                    </button>
                    <button class="btn btn-success" onclick="testEmail()">
                        <i class="fas fa-envelope me-1"></i>测试邮件
                    </button>
                    <button class="btn btn-secondary" onclick="viewLogs()">
                        <i class="fas fa-file-alt me-1"></i>查看日志
                    </button>
                </div>
            </div>
        </div>

        <!-- 管理操作 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> 管理操作</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_panel.products', status='pending') }}" class="btn btn-warning">
                        <i class="fas fa-clock"></i> 审核商品
                        {% if pending_products > 0 %}
                        <span class="badge bg-light text-dark">{{ pending_products }}</span>
                        {% endif %}
                    </a>
                    <a href="{{ url_for('admin_panel.users') }}" class="btn btn-primary">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                    <a href="{{ url_for('admin_panel.feedbacks', status='pending') }}" class="btn btn-info">
                        <i class="fas fa-comment"></i> 处理反馈
                        {% if pending_feedbacks > 0 %}
                        <span class="badge bg-light text-dark">{{ pending_feedbacks }}</span>
                        {% endif %}
                    </a>
                    <a href="{{ url_for('admin_panel.add_category') }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> 添加分类
                    </a>
                    <a href="{{ url_for('admin_panel.system_config') }}" class="btn btn-secondary">
                        <i class="fas fa-cog"></i> 系统设置
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 最近订单 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> 最近订单</h5>
                <a href="{{ url_for('admin_panel.orders') }}" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                {% for order in recent_orders %}
                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                    <div>
                        <div class="fw-bold">{{ order.order_no }}</div>
                        <small class="text-muted">{{ order.product.title[:20] }}...</small>
                        <div class="text-danger fw-bold">¥{{ "%.2f"|format(order.total_amount) }}</div>
                    </div>
                    <div class="text-end">
                        <small>
                            {% if order.status.value == 'pending' %}
                            <span class="badge bg-warning">待付款</span>
                            {% elif order.status.value == 'paid' %}
                            <span class="badge bg-info">已付款</span>
                            {% elif order.status.value == 'completed' %}
                            <span class="badge bg-success">已完成</span>
                            {% endif %}
                        </small>
                        <div class="text-muted small">{{ moment(order.created_at).fromNow() }}</div>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无订单数据</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- 最近系统活动 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-history"></i> 最近活动</h5>
                <a href="{{ url_for('admin_panel.logs') }}" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                <div class="timeline">
                    {% for log in recent_logs %}
                    <div class="timeline-item">
                        <div class="timeline-marker
                            {% if log.level == 'ERROR' %}bg-danger
                            {% elif log.level == 'WARNING' %}bg-warning
                            {% else %}bg-info{% endif %}"></div>
                        <div class="timeline-content">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <small class="text-muted">{{ log.created_at.strftime('%H:%M:%S') }}</small>
                                    <div class="fw-bold">{{ log.message[:50] }}{% if log.message|length > 50 %}...{% endif %}</div>
                                    {% if log.module %}
                                    <small class="text-muted">模块: {{ log.module }}</small>
                                    {% endif %}
                                </div>
                                <span class="badge
                                    {% if log.level == 'ERROR' %}bg-danger
                                    {% elif log.level == 'WARNING' %}bg-warning
                                    {% else %}bg-info{% endif %}">{{ log.level }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-history fa-2x mb-2"></i><br>
                    暂无系统活动记录
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}

.timeline-marker {
    position: absolute;
    left: -26px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}
</style>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 自动刷新在线用户数和相关统计
    function updateOnlineUsers() {
        $.ajax({
            url: '/admin_panel/api/online_users',
            method: 'GET',
            timeout: 10000, // 10秒超时
            success: function(data) {
                console.log('Online users data:', data);

                // 更新在线用户数
                if (data.online_users !== undefined) {
                    $('#online-users').text(data.online_users);

                    // 添加动画效果
                    $('#online-users').parent().addClass('pulse-animation');
                    setTimeout(function() {
                        $('#online-users').parent().removeClass('pulse-animation');
                    }, 1000);
                }

                // 更新总用户数（如果页面有这个元素）
                if (data.total_users !== undefined && $('#total-users').length) {
                    $('#total-users').text(data.total_users);
                }

                // 更新今日新增用户数（如果页面有这个元素）
                if (data.new_users_today !== undefined && $('#new-users-today').length) {
                    $('#new-users-today').text(data.new_users_today);
                }

                // 更新时间戳
                if (data.timestamp && $('#last-update').length) {
                    $('#last-update').text('最后更新: ' + data.timestamp);
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to update online users:', error);
                // 错误时显示0
                $('#online-users').text('0');

                // 显示错误状态
                if ($('#connection-status').length) {
                    $('#connection-status').removeClass('text-success').addClass('text-danger').text('连接异常');
                }
            }
        });
    }

    // 立即更新一次在线用户数
    updateOnlineUsers();

    // 每5秒更新一次在线用户数（进一步提高更新频率）
    setInterval(updateOnlineUsers, 5000);

    // 页面获得焦点时立即更新
    $(window).focus(function() {
        updateOnlineUsers();
    });

    // 页面可见性变化时更新
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            updateOnlineUsers();
        }
    });

    // 工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();

    // 添加CSS动画样式
    if (!$('#pulse-animation-style').length) {
        $('<style id="pulse-animation-style">')
            .text(`
                .pulse-animation {
                    animation: pulse 0.5s ease-in-out;
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
            `)
            .appendTo('head');
    }
});

// 快速操作函数
function clearCache() {
    showConfirm('确定要清理缓存吗？', function() {
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        fetch('/admin_panel/api/clear_cache', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showInfo(data.message);
            } else {
                showInfo('清理失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showInfo('操作失败，请重试');
        });
    });
}

function backupDatabase() {
    showConfirm('确定要备份数据库吗？', function() {
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        fetch('/admin_panel/api/backup_database', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showInfo(data.message);
            } else {
                showInfo('备份失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showInfo('操作失败，请重试');
        });
    });
}

function testEmail() {
    showPrompt('请输入测试邮箱地址：', '', function(email) {
        if (email) {
            const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

            fetch('/admin_panel/api/test_email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showInfo(data.message);
                } else {
                    showInfo('测试失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showInfo('操作失败，请重试');
            });
        }
    });
}

function viewLogs() {
    window.open('/admin_panel/logs', '_blank');
}

// 自定义确认对话框
function showConfirm(message, onConfirm, onCancel) {
    // 创建模态对话框HTML
    const confirmHtml = `
        <div class="modal fade" id="customConfirmModal" tabindex="-1" aria-hidden="true" style="z-index: 1060;">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="border-radius: 12px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div class="modal-header" style="border-bottom: 1px solid #e9ecef; padding: 20px 24px 16px;">
                        <h5 class="modal-title" style="color: #333; font-weight: 500;">192.168.31.52:5000 显示</h5>
                    </div>
                    <div class="modal-body" style="padding: 20px 24px; color: #333; font-size: 16px;">
                        ${message}
                    </div>
                    <div class="modal-footer" style="border-top: none; padding: 16px 24px 20px; justify-content: flex-end; gap: 12px;">
                        <button type="button" class="btn btn-secondary" id="confirmCancel" style="padding: 8px 20px; border-radius: 6px; font-weight: 500;">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmOk" style="padding: 8px 20px; border-radius: 6px; font-weight: 500; background-color: #007bff;">确定</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除现有的确认对话框
    const existingModal = document.getElementById('customConfirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', confirmHtml);

    // 获取模态框元素
    const modal = document.getElementById('customConfirmModal');
    const confirmBtn = document.getElementById('confirmOk');
    const cancelBtn = document.getElementById('confirmCancel');

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 绑定事件
    confirmBtn.onclick = function() {
        bsModal.hide();
        if (onConfirm) onConfirm();
    };

    cancelBtn.onclick = function() {
        bsModal.hide();
        if (onCancel) onCancel();
    };

    // 模态框隐藏后清理DOM
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

// 自定义信息提示对话框（类似confirm样式，但只有确定按钮）
function showInfo(message, onOk) {
    // 创建模态对话框HTML
    const infoHtml = `
        <div class="modal fade" id="customInfoModal" tabindex="-1" aria-hidden="true" style="z-index: 1060;">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="border-radius: 12px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div class="modal-header" style="border-bottom: 1px solid #e9ecef; padding: 20px 24px 16px;">
                        <h5 class="modal-title" style="color: #333; font-weight: 500;">192.168.31.52:5000 显示</h5>
                    </div>
                    <div class="modal-body" style="padding: 20px 24px; color: #333; font-size: 16px;">
                        ${message}
                    </div>
                    <div class="modal-footer" style="border-top: none; padding: 16px 24px 20px; justify-content: flex-end;">
                        <button type="button" class="btn btn-primary" id="infoOk" style="padding: 8px 20px; border-radius: 6px; font-weight: 500; background-color: #007bff;">确定</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除现有的信息对话框
    const existingModal = document.getElementById('customInfoModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', infoHtml);

    // 获取模态框元素
    const modal = document.getElementById('customInfoModal');
    const okBtn = document.getElementById('infoOk');

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 绑定事件
    okBtn.onclick = function() {
        bsModal.hide();
        if (onOk) onOk();
    };

    // 模态框隐藏后清理DOM
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

// 自定义输入对话框（类似prompt样式）
function showPrompt(message, defaultValue, onOk, onCancel) {
    // 创建模态对话框HTML
    const promptHtml = `
        <div class="modal fade" id="customPromptModal" tabindex="-1" aria-hidden="true" style="z-index: 1060;">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="border-radius: 12px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div class="modal-header" style="border-bottom: 1px solid #e9ecef; padding: 20px 24px 16px;">
                        <h5 class="modal-title" style="color: #333; font-weight: 500;">192.168.31.52:5000 显示</h5>
                    </div>
                    <div class="modal-body" style="padding: 20px 24px; color: #333; font-size: 16px;">
                        <div style="margin-bottom: 15px;">${message}</div>
                        <input type="text" class="form-control" id="promptInput" value="${defaultValue || ''}" style="border-radius: 6px;">
                    </div>
                    <div class="modal-footer" style="border-top: none; padding: 16px 24px 20px; justify-content: flex-end; gap: 12px;">
                        <button type="button" class="btn btn-secondary" id="promptCancel" style="padding: 8px 20px; border-radius: 6px; font-weight: 500;">取消</button>
                        <button type="button" class="btn btn-primary" id="promptOk" style="padding: 8px 20px; border-radius: 6px; font-weight: 500; background-color: #007bff;">确定</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除现有的输入对话框
    const existingModal = document.getElementById('customPromptModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', promptHtml);

    // 获取模态框元素
    const modal = document.getElementById('customPromptModal');
    const input = document.getElementById('promptInput');
    const okBtn = document.getElementById('promptOk');
    const cancelBtn = document.getElementById('promptCancel');

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 聚焦到输入框
    modal.addEventListener('shown.bs.modal', function() {
        input.focus();
        input.select();
    });

    // 绑定事件
    okBtn.onclick = function() {
        const value = input.value.trim();
        bsModal.hide();
        if (onOk) onOk(value);
    };

    cancelBtn.onclick = function() {
        bsModal.hide();
        if (onCancel) onCancel();
    };

    // 回车键确认
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            okBtn.click();
        }
    });

    // 模态框隐藏后清理DOM
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

// 显示提示信息
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="margin-bottom: 20px; position: relative; z-index: 1050;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 优先在页面内容区域的最顶部显示提示
    const pageTransition = document.querySelector('.page-transition');
    const targetContainer = pageTransition || document.querySelector('.container-fluid') || document.querySelector('.container') || document.body;

    // 移除现有的提示（只移除我们创建的提示，不影响其他alert）
    const existingAlerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
    existingAlerts.forEach(alert => {
        if (alert.style.zIndex === '1050') {
            alert.remove();
        }
    });

    // 在目标容器最顶部插入新提示
    if (pageTransition) {
        // 如果有page-transition容器，在其第一个子元素之前插入
        const firstChild = pageTransition.firstElementChild;
        if (firstChild) {
            firstChild.insertAdjacentHTML('beforebegin', alertHtml);
        } else {
            pageTransition.insertAdjacentHTML('afterbegin', alertHtml);
        }
    } else {
        targetContainer.insertAdjacentHTML('afterbegin', alertHtml);
    }

    // 滚动到页面顶部以确保用户看到提示
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 3秒后自动消失
    setTimeout(() => {
        const alerts = targetContainer.querySelectorAll('.alert.alert-dismissible');
        alerts.forEach(alert => {
            if (alert.style.zIndex === '1050') {
                alert.remove();
            }
        });
    }, 3000);
}
</script>
{% endblock %}
