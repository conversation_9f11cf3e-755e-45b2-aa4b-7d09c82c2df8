#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端统计API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import User, Product, Order, Message, Notification, Category, ProductStatus, OrderStatus
from app import db
from app.utils.jwt_utils import jwt_required
from app.utils.api_response import success_response, error_response
from sqlalchemy import func, and_
from datetime import datetime, timedelta


@bp.route('/stats/dashboard', methods=['GET'])
@jwt_required
def user_dashboard():
    """用户仪表板统计"""
    try:
        user = request.current_user
        
        # 我的商品统计
        my_products = Product.query.filter_by(seller_id=user.id).count()
        active_products = Product.query.filter_by(
            seller_id=user.id,
            status=ProductStatus.ACTIVE
        ).count()
        sold_products = Product.query.filter_by(
            seller_id=user.id,
            status=ProductStatus.SOLD
        ).count()
        
        # 我的订单统计
        buy_orders = Order.query.filter_by(buyer_id=user.id).count()
        sell_orders = Order.query.filter_by(seller_id=user.id).count()
        completed_buy_orders = Order.query.filter_by(
            buyer_id=user.id,
            status=OrderStatus.COMPLETED
        ).count()
        completed_sell_orders = Order.query.filter_by(
            seller_id=user.id,
            status=OrderStatus.COMPLETED
        ).count()
        
        # 收入统计
        total_revenue = db.session.query(func.sum(Order.total_price)).filter(
            Order.seller_id == user.id,
            Order.status == OrderStatus.COMPLETED
        ).scalar() or 0
        
        # 本月收入
        month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_revenue = db.session.query(func.sum(Order.total_price)).filter(
            Order.seller_id == user.id,
            Order.status == OrderStatus.COMPLETED,
            Order.created_at >= month_start
        ).scalar() or 0
        
        # 未读消息和通知
        unread_messages = Message.query.filter_by(
            receiver_id=user.id,
            is_read=False
        ).count()
        
        unread_notifications = Notification.query.filter_by(
            user_id=user.id,
            is_read=False
        ).count()
        
        # 商品浏览量统计
        total_views = db.session.query(func.sum(Product.view_count)).filter(
            Product.seller_id == user.id
        ).scalar() or 0
        
        # 商品收藏量统计
        total_favorites = db.session.query(func.sum(Product.favorite_count)).filter(
            Product.seller_id == user.id
        ).scalar() or 0
        
        return success_response({
            'products': {
                'total': my_products,
                'active': active_products,
                'sold': sold_products,
                'totalViews': total_views,
                'totalFavorites': total_favorites
            },
            'orders': {
                'buyTotal': buy_orders,
                'sellTotal': sell_orders,
                'buyCompleted': completed_buy_orders,
                'sellCompleted': completed_sell_orders
            },
            'revenue': {
                'total': float(total_revenue),
                'thisMonth': float(month_revenue)
            },
            'notifications': {
                'unreadMessages': unread_messages,
                'unreadNotifications': unread_notifications,
                'totalUnread': unread_messages + unread_notifications
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户仪表板统计失败: {str(e)}")
        return error_response('SYS_001', '获取统计数据失败', http_code=500)


@bp.route('/stats/sales-trend', methods=['GET'])
@jwt_required
def sales_trend():
    """销售趋势统计"""
    try:
        user = request.current_user
        
        # 获取最近30天的销售数据
        days = 30
        start_date = datetime.now() - timedelta(days=days)
        
        # 按日期分组统计订单数量和收入
        daily_stats = db.session.query(
            func.date(Order.created_at).label('date'),
            func.count(Order.id).label('order_count'),
            func.sum(Order.total_price).label('revenue')
        ).filter(
            Order.seller_id == user.id,
            Order.status == OrderStatus.COMPLETED,
            Order.created_at >= start_date
        ).group_by(
            func.date(Order.created_at)
        ).order_by('date').all()
        
        # 格式化数据
        trend_data = []
        for stat in daily_stats:
            trend_data.append({
                'date': stat.date.isoformat(),
                'orderCount': stat.order_count,
                'revenue': float(stat.revenue or 0)
            })
        
        return success_response(trend_data)
        
    except Exception as e:
        current_app.logger.error(f"获取销售趋势失败: {str(e)}")
        return error_response('SYS_001', '获取销售趋势失败', http_code=500)


@bp.route('/stats/product-performance', methods=['GET'])
@jwt_required
def product_performance():
    """商品表现统计"""
    try:
        user = request.current_user
        
        # 获取用户商品的表现数据
        products = db.session.query(
            Product.id,
            Product.title,
            Product.price,
            Product.view_count,
            Product.favorite_count,
            Product.sold_quantity,
            Product.status,
            Product.created_at
        ).filter(
            Product.seller_id == user.id
        ).order_by(Product.view_count.desc()).limit(10).all()
        
        product_data = []
        for product in products:
            product_data.append({
                'id': product.id,
                'title': product.title,
                'price': float(product.price),
                'viewCount': product.view_count,
                'favoriteCount': product.favorite_count,
                'soldQuantity': product.sold_quantity,
                'status': product.status.value,
                'createdAt': product.created_at.isoformat() + 'Z'
            })
        
        return success_response(product_data)
        
    except Exception as e:
        current_app.logger.error(f"获取商品表现统计失败: {str(e)}")
        return error_response('SYS_001', '获取商品表现统计失败', http_code=500)


@bp.route('/stats/category-distribution', methods=['GET'])
@jwt_required
def category_distribution():
    """分类分布统计"""
    try:
        user = request.current_user
        
        # 统计用户商品的分类分布
        category_stats = db.session.query(
            Category.name,
            func.count(Product.id).label('count'),
            func.sum(Product.view_count).label('total_views'),
            func.sum(Product.favorite_count).label('total_favorites')
        ).join(
            Product, Product.category_id == Category.id
        ).filter(
            Product.seller_id == user.id
        ).group_by(
            Category.id, Category.name
        ).all()
        
        distribution_data = []
        for stat in category_stats:
            distribution_data.append({
                'category': stat.name,
                'productCount': stat.count,
                'totalViews': stat.total_views or 0,
                'totalFavorites': stat.total_favorites or 0
            })
        
        return success_response(distribution_data)
        
    except Exception as e:
        current_app.logger.error(f"获取分类分布统计失败: {str(e)}")
        return error_response('SYS_001', '获取分类分布统计失败', http_code=500)
