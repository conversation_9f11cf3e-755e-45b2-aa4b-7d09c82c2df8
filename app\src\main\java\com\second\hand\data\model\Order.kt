package com.second.hand.data.model

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import com.second.hand.data.model.enums.OrderStatus

/**
 * 订单数据模型
 * 对应Flask后端的Order模型
 */
@Entity(tableName = "orders")
data class Order(
    @PrimaryKey
    @SerializedName("id")
    var id: Int = 0,

    @SerializedName("order_no")
    var orderNo: String = "",

    // 外键
    @SerializedName("buyer_id")
    var buyerId: Int = 0,

    @SerializedName("seller_id")
    var sellerId: Int = 0,

    @SerializedName("product_id")
    var productId: Int = 0,

    // 订单信息
    @SerializedName("quantity")
    var quantity: Int = 1,

    @SerializedName("price")
    var price: Double = 0.0,

    @SerializedName("total_amount")
    var totalAmount: Double = 0.0,

    // 订单状态
    @SerializedName("status")
    var status: OrderStatus = OrderStatus.PENDING,

    // 收货信息
    @SerializedName("shipping_name")
    var shippingName: String? = null,

    @SerializedName("shipping_phone")
    var shippingPhone: String? = null,

    @SerializedName("shipping_address")
    var shippingAddress: String? = null,

    // 备注
    @SerializedName("buyer_note")
    var buyerNote: String? = null,

    @SerializedName("seller_note")
    var sellerNote: String? = null,

    // 时间戳
    @SerializedName("created_at")
    var createdAt: String = "",

    @SerializedName("updated_at")
    var updatedAt: String? = null,

    @SerializedName("paid_at")
    var paidAt: String? = null,

    @SerializedName("shipped_at")
    var shippedAt: String? = null,

    @SerializedName("delivered_at")
    var deliveredAt: String? = null,

    @SerializedName("completed_at")
    var completedAt: String? = null,
    
    // 关联数据（API返回时可能包含，不存储到数据库）
    @Ignore
    @SerializedName("buyer")
    val buyer: User? = null,

    @Ignore
    @SerializedName("seller")
    val seller: User? = null,

    @Ignore
    @SerializedName("product")
    val product: Product? = null,

    @Ignore
    @SerializedName("review")
    val review: Review? = null
) {
    /**
     * 获取状态显示文本
     */
    fun getStatusDisplay(): String {
        return when (status) {
            OrderStatus.PENDING -> "待付款"
            OrderStatus.PAID -> "已付款"
            OrderStatus.SHIPPED -> "已发货"
            OrderStatus.DELIVERED -> "已送达"
            OrderStatus.COMPLETED -> "已完成"
            OrderStatus.CANCELLED -> "已取消"
        }
    }
    
    /**
     * 获取状态颜色
     */
    fun getStatusColor(): String {
        return when (status) {
            OrderStatus.PENDING -> "#FF9800"      // 橙色
            OrderStatus.PAID -> "#2196F3"         // 蓝色
            OrderStatus.SHIPPED -> "#9C27B0"      // 紫色
            OrderStatus.DELIVERED -> "#4CAF50"    // 绿色
            OrderStatus.COMPLETED -> "#4CAF50"    // 绿色
            OrderStatus.CANCELLED -> "#F44336"    // 红色
        }
    }
    
    /**
     * 格式化总金额显示
     */
    fun getFormattedTotalAmount(): String {
        return "¥%.2f".format(totalAmount)
    }
    
    /**
     * 格式化单价显示
     */
    fun getFormattedPrice(): String {
        return "¥%.2f".format(price)
    }
    
    /**
     * 检查订单是否可以取消
     */
    fun canCancel(): Boolean {
        return status == OrderStatus.PENDING
    }
    
    /**
     * 检查订单是否可以确认收货
     */
    fun canConfirmDelivery(): Boolean {
        return status == OrderStatus.SHIPPED
    }
    
    /**
     * 检查订单是否可以评价
     */
    fun canReview(): Boolean {
        return status == OrderStatus.COMPLETED && review == null
    }
}
