package com.second.hand.ui.product

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.second.hand.data.model.Product
import com.second.hand.ui.components.HorizontalProductCard
import com.second.hand.ui.components.ProductCard


/**
 * 商品列表页面
 * 显示商品列表，支持搜索、筛选、分页加载、下拉刷新
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductListScreen(
    onProductClick: (Product) -> Unit,
    onSearchClick: () -> Unit = {},
    onFilterClick: () -> Unit = {},
    viewModel: ProductListViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val products by viewModel.products.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    
    var isGridView by remember { mutableStateOf(true) }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部搜索栏和工具栏
        TopAppBar(
            title = { 
                Text(
                    text = "商品列表",
                    fontWeight = FontWeight.Bold
                ) 
            },
            actions = {
                // 搜索按钮
                IconButton(onClick = onSearchClick) {
                    Icon(Icons.Default.Search, contentDescription = "搜索")
                }
                
                // 视图切换按钮
                IconButton(onClick = { isGridView = !isGridView }) {
                    Icon(
                        imageVector = if (isGridView) Icons.Default.List else Icons.Default.Menu,
                        contentDescription = if (isGridView) "列表视图" else "网格视图"
                    )
                }
                
                // 筛选按钮
                IconButton(onClick = onFilterClick) {
                    Icon(Icons.Default.Settings, contentDescription = "筛选")
                }
            }
        )
        
        // 搜索结果提示
        if (searchQuery.isNotBlank()) {
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primaryContainer
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "搜索结果: \"$searchQuery\"",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    
                    TextButton(onClick = { viewModel.clearSearch() }) {
                        Text("清除")
                    }
                }
            }
        }
        
        // 商品列表内容
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            when {
                isLoading && products.isEmpty() -> {
                    // 初始加载状态
                    LoadingContent()
                }
                error != null && products.isEmpty() -> {
                    // 错误状态
                    ErrorContent(
                        error = error ?: "未知错误",
                        onRetry = { viewModel.refreshProducts() }
                    )
                }
                uiState.isEmpty -> {
                    // 空状态
                    EmptyContent(
                        searchQuery = searchQuery,
                        onClearSearch = { viewModel.clearSearch() }
                    )
                }
                else -> {
                    // 商品列表
                    ProductListContent(
                        products = products,
                        isGridView = isGridView,
                        isLoadingMore = uiState.isLoadingMore,
                        hasMorePages = uiState.hasMorePages,
                        onProductClick = { product ->
                            viewModel.incrementViewCount(product.id)
                            onProductClick(product)
                        },
                        onFavoriteClick = { productId ->
                            viewModel.toggleFavorite(productId)
                        },
                        onLoadMore = { viewModel.loadMoreProducts() },
                        onRefresh = { viewModel.refreshProducts() }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ProductListContent(
    products: List<Product>,
    isGridView: Boolean,
    isLoadingMore: Boolean,
    hasMorePages: Boolean,
    onProductClick: (Product) -> Unit,
    onFavoriteClick: (Int) -> Unit,
    onLoadMore: () -> Unit,
    onRefresh: () -> Unit
) {
    var isRefreshing by remember { mutableStateOf(false) }

    // 监听刷新完成
    LaunchedEffect(products) {
        if (isRefreshing) {
            isRefreshing = false
        }
    }

    PullToRefreshBox(
        isRefreshing = isRefreshing,
        onRefresh = {
            isRefreshing = true
            onRefresh()
        }
    ) {
        if (isGridView) {
            // 网格视图
            val gridState = rememberLazyGridState()

            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                state = gridState,
                contentPadding = PaddingValues(16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(products) { product ->
                    ProductCard(
                        product = product,
                        onProductClick = onProductClick,
                        onFavoriteClick = onFavoriteClick
                    )
                }

                // 加载更多指示器
                if (isLoadingMore) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }
                }
            }

            // 检测滚动到底部
            LaunchedEffect(gridState) {
                snapshotFlow { gridState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }
                    .collect { lastVisibleIndex ->
                        if (lastVisibleIndex != null &&
                            lastVisibleIndex >= products.size - 3 &&
                            hasMorePages &&
                            !isLoadingMore) {
                            onLoadMore()
                        }
                    }
            }
        } else {
            // 列表视图
            val listState = rememberLazyListState()

            LazyColumn(
                state = listState,
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(products) { product ->
                    HorizontalProductCard(
                        product = product,
                        onProductClick = onProductClick,
                        onFavoriteClick = onFavoriteClick
                    )
                }

                // 加载更多指示器
                if (isLoadingMore) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }
                }
            }

            // 检测滚动到底部
            LaunchedEffect(listState) {
                snapshotFlow { listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }
                    .collect { lastVisibleIndex ->
                        if (lastVisibleIndex != null &&
                            lastVisibleIndex >= products.size - 3 &&
                            hasMorePages &&
                            !isLoadingMore) {
                            onLoadMore()
                        }
                    }
            }
        }
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CircularProgressIndicator()
            Text(
                text = "加载中...",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "加载失败",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = error,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}

@Composable
private fun EmptyContent(
    searchQuery: String,
    onClearSearch: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = if (searchQuery.isNotBlank()) "未找到相关商品" else "暂无商品",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = if (searchQuery.isNotBlank()) "尝试使用其他关键词搜索" else "还没有商品发布",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            if (searchQuery.isNotBlank()) {
                Button(onClick = onClearSearch) {
                    Text("清除搜索")
                }
            }
        }
    }
}
