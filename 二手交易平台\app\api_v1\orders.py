#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端订单API
"""

from flask import request, current_app
from app.api_v1 import bp
from app.models import Order, Product, User, Review, OrderStatus, ProductStatus, Notification
from app import db
from app.utils.jwt_utils import jwt_required
from app.utils.api_response import (
    success_response, error_response, validation_error_response,
    paginated_response, not_found_response
)
from app.utils.datetime_utils import local_now
from app.utils.notification_utils import create_notification
from sqlalchemy import or_, and_, desc
import uuid


def validate_order_data(data):
    """验证订单数据"""
    errors = {}
    
    if not data.get('productId'):
        errors['productId'] = '商品ID不能为空'
    
    try:
        quantity = int(data.get('quantity', 1))
        if quantity <= 0:
            errors['quantity'] = '购买数量必须大于0'
        elif quantity > 999:
            errors['quantity'] = '购买数量不能超过999'
    except (ValueError, TypeError):
        errors['quantity'] = '购买数量格式不正确'
    
    message = data.get('message', '').strip()
    if len(message) > 500:
        errors['message'] = '备注信息不能超过500个字符'
    
    return errors


def serialize_order(order, include_details=True):
    """序列化订单数据"""
    data = {
        'id': order.id,
        'orderNumber': order.order_number,
        'quantity': order.quantity,
        'totalPrice': float(order.total_price),
        'status': order.status.value,
        'message': order.message,
        'createdAt': order.created_at.strftime('%Y-%m-%dT%H:%M:%S') + 'Z',
        'updatedAt': order.updated_at.strftime('%Y-%m-%dT%H:%M:%S') + 'Z' if order.updated_at else None
    }
    
    if include_details:
        # 买家信息
        if order.buyer:
            data['buyer'] = {
                'id': order.buyer.id,
                'username': order.buyer.username,
                'nickname': order.buyer.nickname,
                'avatar': order.buyer.avatar_url()
            }
        
        # 卖家信息
        if order.seller:
            data['seller'] = {
                'id': order.seller.id,
                'username': order.seller.username,
                'nickname': order.seller.nickname,
                'avatar': order.seller.avatar_url()
            }
        
        # 商品信息
        if order.product:
            data['product'] = {
                'id': order.product.id,
                'title': order.product.title,
                'price': float(order.product.price),
                'condition': order.product.condition,
                'mainImage': order.product.main_image_url(),
                'status': order.product.status.value
            }
        
        # 评价信息
        if hasattr(order, 'review') and order.review:
            data['review'] = {
                'id': order.review.id,
                'rating': order.review.rating,
                'comment': order.review.comment,
                'createdAt': order.review.created_at.isoformat() + 'Z'
            }
    
    return data


@bp.route('/orders', methods=['POST'])
@jwt_required
def create_order():
    """创建订单"""
    try:
        user = request.current_user
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        # 验证数据
        errors = validate_order_data(data)
        if errors:
            return validation_error_response(errors)
        
        product_id = data['productId']
        quantity = int(data.get('quantity', 1))
        message = data.get('message', '').strip()
        
        # 检查商品
        product = Product.query.get(product_id)
        if not product:
            return not_found_response('商品不存在')
        
        if product.status != ProductStatus.ACTIVE:
            return error_response('BIZ_002', '商品已下架或不可购买')
        
        if product.seller_id == user.id:
            return error_response('BIZ_003', '不能购买自己的商品')
        
        # 检查库存
        available_quantity = product.quantity - product.sold_quantity
        if quantity > available_quantity:
            return error_response('BIZ_004', f'库存不足，仅剩{available_quantity}件')
        
        # 计算总价
        total_price = product.price * quantity
        
        # 生成订单号
        order_number = f"ORD{local_now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
        
        # 创建订单
        order = Order(
            order_number=order_number,
            buyer_id=user.id,
            seller_id=product.seller_id,
            product_id=product_id,
            quantity=quantity,
            total_price=total_price,
            status=OrderStatus.PENDING,
            message=message,
            created_at=local_now()
        )
        
        db.session.add(order)
        
        # 更新商品销量
        product.sold_quantity += quantity
        
        # 如果售完，更新商品状态
        if product.sold_quantity >= product.quantity:
            product.status = ProductStatus.SOLD
        
        db.session.flush()  # 获取order.id
        
        # 发送通知给卖家
        create_notification(
            user_id=product.seller_id,
            title='新订单通知',
            content=f'您的商品"{product.title}"有新订单，订单号：{order_number}',
            type='order',
            related_id=order.id
        )
        
        db.session.commit()
        
        return success_response(
            serialize_order(order),
            '订单创建成功',
            201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建订单失败: {str(e)}")
        return error_response('SYS_001', '创建订单失败', http_code=500)


@bp.route('/orders', methods=['GET'])
@jwt_required
def get_orders():
    """获取订单列表"""
    try:
        user = request.current_user
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        order_type = request.args.get('type', 'buy')  # buy, sell
        status = request.args.get('status', 'all')
        
        # 构建查询
        if order_type == 'sell':
            query = Order.query.filter(Order.seller_id == user.id)
        else:  # buy
            query = Order.query.filter(Order.buyer_id == user.id)
        
        # 状态筛选
        if status != 'all':
            try:
                status_enum = OrderStatus(status)
                query = query.filter(Order.status == status_enum)
            except ValueError:
                return validation_error_response({'status': '无效的订单状态'})
        
        # 按创建时间倒序排列
        query = query.order_by(desc(Order.created_at))
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )
        
        # 序列化数据
        orders = [serialize_order(order) for order in pagination.items]
        
        return paginated_response(orders, pagination)
        
    except Exception as e:
        current_app.logger.error(f"获取订单列表失败: {str(e)}")
        return error_response('SYS_001', '获取订单列表失败', http_code=500)


@bp.route('/orders/<int:order_id>', methods=['GET'])
@jwt_required
def get_order(order_id):
    """获取订单详情"""
    try:
        user = request.current_user
        order = Order.query.get(order_id)
        
        if not order:
            return not_found_response('订单不存在')
        
        # 权限检查
        if order.buyer_id != user.id and order.seller_id != user.id and not user.is_admin():
            return error_response('AUTH_004', '无权限查看此订单', http_code=403)
        
        return success_response(serialize_order(order))
        
    except Exception as e:
        current_app.logger.error(f"获取订单详情失败: {str(e)}")
        return error_response('SYS_001', '获取订单详情失败', http_code=500)


@bp.route('/orders/<int:order_id>/status', methods=['PUT'])
@jwt_required
def update_order_status(order_id):
    """更新订单状态"""
    try:
        user = request.current_user
        order = Order.query.get(order_id)
        
        if not order:
            return not_found_response('订单不存在')
        
        data = request.get_json()
        if not data:
            return validation_error_response({'request': '请求数据格式错误'})

        new_status = data.get('status')
        note = data.get('note', '').strip()

        if not new_status:
            return validation_error_response({'status': '订单状态不能为空'})
        
        try:
            new_status_enum = OrderStatus(new_status)
        except ValueError:
            return validation_error_response({'status': '无效的订单状态'})
        
        # 权限和状态转换检查
        current_status = order.status
        
        # 买家可以执行的操作
        if order.buyer_id == user.id:
            if current_status == OrderStatus.PENDING and new_status_enum == OrderStatus.CANCELLED:
                # 买家取消订单
                pass
            elif current_status == OrderStatus.SHIPPED and new_status_enum == OrderStatus.DELIVERED:
                # 买家确认收货
                pass
            elif current_status == OrderStatus.DELIVERED and new_status_enum == OrderStatus.COMPLETED:
                # 买家确认完成
                pass
            else:
                return error_response('BIZ_005', '无效的状态转换')
        
        # 卖家可以执行的操作
        elif order.seller_id == user.id:
            if current_status == OrderStatus.PENDING and new_status_enum == OrderStatus.CANCELLED:
                # 卖家取消订单
                pass
            elif current_status == OrderStatus.PENDING and new_status_enum == OrderStatus.PAID:
                # 卖家确认付款
                pass
            elif current_status == OrderStatus.PAID and new_status_enum == OrderStatus.SHIPPED:
                # 卖家发货
                pass
            else:
                return error_response('BIZ_005', '无效的状态转换')
        
        # 管理员可以执行任何操作
        elif not user.is_admin():
            return error_response('AUTH_004', '无权限更新此订单', http_code=403)
        
        # 更新订单状态
        old_status = order.status
        order.status = new_status_enum
        order.updated_at = local_now()
        
        # 如果订单被取消，恢复商品库存
        if new_status_enum == OrderStatus.CANCELLED and old_status != OrderStatus.CANCELLED:
            product = order.product
            product.sold_quantity = max(0, product.sold_quantity - order.quantity)
            if product.status == ProductStatus.SOLD:
                product.status = ProductStatus.ACTIVE
        
        # 发送状态更新通知
        if order.buyer_id == user.id:
            # 通知卖家
            notify_user_id = order.seller_id
            notify_content = f'订单{order.order_number}状态已更新为：{new_status_enum.value}'
        else:
            # 通知买家
            notify_user_id = order.buyer_id
            notify_content = f'您的订单{order.order_number}状态已更新为：{new_status_enum.value}'
        
        if note:
            notify_content += f'，备注：{note}'
        
        create_notification(
            user_id=notify_user_id,
            title='订单状态更新',
            content=notify_content,
            type='order',
            related_id=order.id
        )
        
        db.session.commit()
        
        return success_response(
            serialize_order(order),
            '订单状态更新成功'
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新订单状态失败: {str(e)}")
        return error_response('SYS_001', '更新订单状态失败', http_code=500)


@bp.route('/orders/<int:order_id>/cancel', methods=['POST'])
@jwt_required
def cancel_order(order_id):
    """取消订单"""
    try:
        user = request.current_user
        order = Order.query.get(order_id)

        if not order:
            return not_found_response('订单不存在')

        # 权限检查
        if order.buyer_id != user.id and order.seller_id != user.id and not user.is_admin():
            return error_response('AUTH_004', '无权限取消此订单', http_code=403)

        # 状态检查
        if order.status not in [OrderStatus.PENDING, OrderStatus.PAID]:
            return error_response('BIZ_005', '当前订单状态不允许取消')

        data = request.get_json() or {}
        reason = data.get('reason', '').strip()

        # 更新订单状态
        old_status = order.status
        order.status = OrderStatus.CANCELLED
        order.updated_at = local_now()

        # 恢复商品库存
        product = order.product
        product.sold_quantity = max(0, product.sold_quantity - order.quantity)
        if product.status == ProductStatus.SOLD:
            product.status = ProductStatus.ACTIVE

        # 发送通知
        if order.buyer_id == user.id:
            # 买家取消，通知卖家
            notify_user_id = order.seller_id
            notify_content = f'买家已取消订单{order.order_number}'
        else:
            # 卖家取消，通知买家
            notify_user_id = order.buyer_id
            notify_content = f'卖家已取消订单{order.order_number}'

        if reason:
            notify_content += f'，原因：{reason}'

        create_notification(
            user_id=notify_user_id,
            title='订单取消通知',
            content=notify_content,
            type='order',
            related_id=order.id
        )

        db.session.commit()

        return success_response(
            serialize_order(order),
            '订单取消成功'
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取消订单失败: {str(e)}")
        return error_response('SYS_001', '取消订单失败', http_code=500)


@bp.route('/orders/<int:order_id>/confirm', methods=['POST'])
@jwt_required
def confirm_order(order_id):
    """确认收货"""
    try:
        user = request.current_user
        order = Order.query.get(order_id)

        if not order:
            return not_found_response('订单不存在')

        # 权限检查（只有买家可以确认收货）
        if order.buyer_id != user.id:
            return error_response('AUTH_004', '只有买家可以确认收货', http_code=403)

        # 状态检查
        if order.status != OrderStatus.SHIPPED:
            return error_response('BIZ_005', '只有已发货的订单才能确认收货')

        # 更新订单状态
        order.status = OrderStatus.DELIVERED
        order.updated_at = local_now()

        # 发送通知给卖家
        create_notification(
            user_id=order.seller_id,
            title='确认收货通知',
            content=f'买家已确认收货，订单{order.order_number}',
            type='order',
            related_id=order.id
        )

        db.session.commit()

        return success_response(
            serialize_order(order),
            '确认收货成功'
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"确认收货失败: {str(e)}")
        return error_response('SYS_001', '确认收货失败', http_code=500)


@bp.route('/orders/<int:order_id>/review', methods=['POST'])
@jwt_required
def create_review(order_id):
    """创建订单评价"""
    try:
        user = request.current_user
        order = Order.query.get(order_id)

        if not order:
            return not_found_response('订单不存在')

        # 权限检查（只有买家可以评价）
        if order.buyer_id != user.id:
            return error_response('AUTH_004', '只有买家可以评价', http_code=403)

        # 状态检查
        if order.status not in [OrderStatus.DELIVERED, OrderStatus.COMPLETED]:
            return error_response('BIZ_005', '只有已收货的订单才能评价')

        # 检查是否已评价
        existing_review = Review.query.filter_by(order_id=order_id).first()
        if existing_review:
            return error_response('BIZ_006', '该订单已评价')

        data = request.get_json()

        # 验证数据
        if not data.get('rating'):
            return validation_error_response({'rating': '评分不能为空'})

        try:
            rating = int(data['rating'])
            if rating < 1 or rating > 5:
                return validation_error_response({'rating': '评分必须在1-5之间'})
        except (ValueError, TypeError):
            return validation_error_response({'rating': '评分格式不正确'})

        comment = data.get('comment', '').strip()
        if len(comment) > 500:
            return validation_error_response({'comment': '评价内容不能超过500个字符'})

        # 创建评价
        review = Review(
            order_id=order_id,
            reviewer_id=user.id,
            reviewee_id=order.seller_id,
            product_id=order.product_id,
            rating=rating,
            comment=comment,
            created_at=local_now()
        )

        db.session.add(review)

        # 更新订单状态为已完成
        order.status = OrderStatus.COMPLETED
        order.updated_at = local_now()

        # 发送通知给卖家
        create_notification(
            user_id=order.seller_id,
            title='收到新评价',
            content=f'您收到了来自订单{order.order_number}的{rating}星评价',
            type='order',
            related_id=order.id
        )

        db.session.commit()

        return success_response({
            'review': {
                'id': review.id,
                'rating': review.rating,
                'comment': review.comment,
                'createdAt': review.created_at.isoformat() + 'Z'
            },
            'order': serialize_order(order)
        }, '评价成功', 201)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建评价失败: {str(e)}")
        return error_response('SYS_001', '创建评价失败', http_code=500)


@bp.route('/orders/<int:order_id>/review', methods=['GET'])
@jwt_required
def get_review(order_id):
    """获取订单评价"""
    try:
        user = request.current_user
        order = Order.query.get(order_id)

        if not order:
            return not_found_response('订单不存在')

        # 权限检查
        if order.buyer_id != user.id and order.seller_id != user.id and not user.is_admin():
            return error_response('AUTH_004', '无权限查看此订单评价', http_code=403)

        review = Review.query.filter_by(order_id=order_id).first()
        if not review:
            return not_found_response('该订单暂无评价')

        review_data = {
            'id': review.id,
            'rating': review.rating,
            'comment': review.comment,
            'createdAt': review.created_at.isoformat() + 'Z',
            'reviewer': {
                'id': review.reviewer.id,
                'username': review.reviewer.username,
                'nickname': review.reviewer.nickname,
                'avatar': review.reviewer.avatar_url()
            } if review.reviewer else None
        }

        return success_response(review_data)

    except Exception as e:
        current_app.logger.error(f"获取订单评价失败: {str(e)}")
        return error_response('SYS_001', '获取订单评价失败', http_code=500)
