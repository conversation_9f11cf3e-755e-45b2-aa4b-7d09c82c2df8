package com.second.hand.ui.main

import com.second.hand.data.api.NetworkResult
import com.second.hand.data.preferences.PreferencesManager
import com.second.hand.data.repository.TestRepository
import com.second.hand.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * 主页面ViewModel
 * 演示MVVM架构和Hilt依赖注入的使用
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val preferencesManager: PreferencesManager,
    private val testRepository: TestRepository
) : BaseViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    init {
        initializeApp()
    }
    
    /**
     * 初始化应用状态
     */
    private fun initializeApp() {
        launchSafely {
            val isLoggedIn = preferencesManager.isLoggedIn()
            _uiState.value = _uiState.value.copy(
                isLoggedIn = isLoggedIn,
                welcomeMessage = if (isLoggedIn) "欢迎回来！" else "欢迎使用二手交易平台"
            )
        }
    }
    
    /**
     * 测试网络连接
     */
    fun testConnection() {
        launchSafely {
            when (val result = testRepository.testConnection()) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        connectionStatus = "网络连接正常 ✓"
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        connectionStatus = "网络连接失败: ${result.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(
                        connectionStatus = "正在测试连接..."
                    )
                }
            }
        }
    }

    /**
     * 测试获取分类列表
     */
    fun testGetCategories() {
        launchSafely {
            when (val result = testRepository.getCategories()) {
                is NetworkResult.Success -> {
                    val categories = result.data
                    _uiState.value = _uiState.value.copy(
                        connectionStatus = "获取到 ${categories.size} 个分类"
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        connectionStatus = "获取分类失败: ${result.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(
                        connectionStatus = "正在获取分类..."
                    )
                }
            }
        }
    }
}

/**
 * 主页面UI状态
 */
data class MainUiState(
    val isLoggedIn: Boolean = false,
    val welcomeMessage: String = "加载中...",
    val connectionStatus: String = "未测试"
)
