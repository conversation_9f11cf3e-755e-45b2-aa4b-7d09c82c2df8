#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单管理表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, IntegerField, SubmitField, SelectField
from wtforms.validators import DataRequired, Length, NumberRange

class CreateOrderForm(FlaskForm):
    """创建订单表单"""
    quantity = IntegerField('数量', validators=[
        DataRequired(message='请输入数量'),
        NumberRange(min=1, max=99, message='数量应在1-99之间')
    ], default=1)
    
    shipping_name = StringField('收货人姓名', validators=[
        DataRequired(message='请输入收货人姓名'),
        Length(min=2, max=100, message='姓名长度应在2-100个字符之间')
    ])
    
    shipping_phone = StringField('收货人电话', validators=[
        DataRequired(message='请输入收货人电话'),
        Length(min=11, max=20, message='电话长度应在11-20个字符之间')
    ])
    
    shipping_address = TextAreaField('收货地址', validators=[
        DataRequired(message='请输入收货地址'),
        Length(min=10, max=500, message='地址长度应在10-500个字符之间')
    ])
    
    buyer_note = TextAreaField('买家留言', validators=[
        Length(max=500, message='留言长度不能超过500个字符')
    ])
    
    submit = SubmitField('提交订单')

class UpdateOrderForm(FlaskForm):
    """更新订单表单"""
    status = SelectField('订单状态', choices=[
        ('pending', '待付款'),
        ('paid', '已付款'),
        ('shipped', '已发货'),
        ('delivered', '已送达'),
        ('completed', '已完成'),
        ('cancelled', '已取消')
    ], validators=[DataRequired(message='请选择订单状态')])
    
    seller_note = TextAreaField('卖家备注', validators=[
        Length(max=500, message='备注长度不能超过500个字符')
    ])
    
    submit = SubmitField('更新订单')

class ReviewForm(FlaskForm):
    """评价表单"""
    rating = SelectField('评分', choices=[
        (5, '5星 - 非常满意'),
        (4, '4星 - 满意'),
        (3, '3星 - 一般'),
        (2, '2星 - 不满意'),
        (1, '1星 - 非常不满意')
    ], coerce=int, validators=[DataRequired(message='请选择评分')])
    
    content = TextAreaField('评价内容', validators=[
        Length(max=1000, message='评价内容长度不能超过1000个字符')
    ])
    
    submit = SubmitField('提交评价')

class MessageForm(FlaskForm):
    """消息表单"""
    content = TextAreaField('消息内容', validators=[
        DataRequired(message='请输入消息内容'),
        Length(min=1, max=1000, message='消息长度应在1-1000个字符之间')
    ])
    
    submit = SubmitField('发送消息')
