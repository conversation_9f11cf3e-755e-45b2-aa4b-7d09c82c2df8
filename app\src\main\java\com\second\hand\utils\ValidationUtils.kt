package com.second.hand.utils

import android.util.Patterns
import java.util.regex.Pattern

/**
 * 表单验证工具类
 * 提供各种输入验证功能
 */
object ValidationUtils {
    
    /**
     * 验证用户名
     */
    fun validateUsername(username: String): ValidationResult {
        return when {
            username.isBlank() -> ValidationResult.Error("用户名不能为空")
            username.length < 3 -> ValidationResult.Error("用户名至少需要3个字符")
            username.length > 20 -> ValidationResult.Error("用户名不能超过20个字符")
            !Pattern.matches("^[a-zA-Z0-9_]+$", username) -> 
                ValidationResult.Error("用户名只能包含字母、数字和下划线")
            else -> ValidationResult.Success
        }
    }
    
    /**
     * 验证邮箱
     */
    fun validateEmail(email: String): ValidationResult {
        return when {
            email.isBlank() -> ValidationResult.Error("邮箱不能为空")
            !Patterns.EMAIL_ADDRESS.matcher(email).matches() -> 
                ValidationResult.Error("邮箱格式不正确")
            else -> ValidationResult.Success
        }
    }
    
    /**
     * 验证密码
     */
    fun validatePassword(password: String): ValidationResult {
        return when {
            password.isBlank() -> ValidationResult.Error("密码不能为空")
            password.length < 6 -> ValidationResult.Error("密码至少需要6个字符")
            password.length > 50 -> ValidationResult.Error("密码不能超过50个字符")
            !containsLetterAndNumber(password) -> 
                ValidationResult.Error("密码必须包含字母和数字")
            else -> ValidationResult.Success
        }
    }
    
    /**
     * 验证确认密码
     */
    fun validateConfirmPassword(password: String, confirmPassword: String): ValidationResult {
        return when {
            confirmPassword.isBlank() -> ValidationResult.Error("确认密码不能为空")
            password != confirmPassword -> ValidationResult.Error("两次输入的密码不一致")
            else -> ValidationResult.Success
        }
    }
    
    /**
     * 验证邮箱验证码
     */
    fun validateEmailCode(emailCode: String): ValidationResult {
        return when {
            emailCode.isBlank() -> ValidationResult.Error("邮箱验证码不能为空")
            emailCode.length != 6 -> ValidationResult.Error("邮箱验证码应为6位数字")
            !Pattern.matches("^\\d{6}$", emailCode) -> 
                ValidationResult.Error("邮箱验证码只能包含数字")
            else -> ValidationResult.Success
        }
    }
    
    /**
     * 验证昵称
     */
    fun validateNickname(nickname: String): ValidationResult {
        return when {
            nickname.isBlank() -> ValidationResult.Error("昵称不能为空")
            nickname.length < 2 -> ValidationResult.Error("昵称至少需要2个字符")
            nickname.length > 20 -> ValidationResult.Error("昵称不能超过20个字符")
            else -> ValidationResult.Success
        }
    }
    
    /**
     * 检查密码是否包含字母和数字
     */
    private fun containsLetterAndNumber(password: String): Boolean {
        val hasLetter = password.any { it.isLetter() }
        val hasDigit = password.any { it.isDigit() }
        return hasLetter && hasDigit
    }
    
    /**
     * 验证登录表单
     */
    fun validateLoginForm(username: String, password: String): List<ValidationError> {
        val errors = mutableListOf<ValidationError>()
        
        if (username.isBlank()) {
            errors.add(ValidationError("username", "用户名不能为空"))
        }
        
        if (password.isBlank()) {
            errors.add(ValidationError("password", "密码不能为空"))
        }
        
        return errors
    }
    
    /**
     * 验证注册表单
     */
    fun validateRegisterForm(
        username: String,
        email: String,
        password: String,
        confirmPassword: String,
        emailCode: String,
        nickname: String = username
    ): List<ValidationError> {
        val errors = mutableListOf<ValidationError>()
        
        // 验证用户名
        when (val result = validateUsername(username)) {
            is ValidationResult.Error -> errors.add(ValidationError("username", result.message))
            is ValidationResult.Success -> {}
        }
        
        // 验证邮箱
        when (val result = validateEmail(email)) {
            is ValidationResult.Error -> errors.add(ValidationError("email", result.message))
            is ValidationResult.Success -> {}
        }
        
        // 验证密码
        when (val result = validatePassword(password)) {
            is ValidationResult.Error -> errors.add(ValidationError("password", result.message))
            is ValidationResult.Success -> {}
        }
        
        // 验证确认密码
        when (val result = validateConfirmPassword(password, confirmPassword)) {
            is ValidationResult.Error -> errors.add(ValidationError("confirmPassword", result.message))
            is ValidationResult.Success -> {}
        }
        
        // 验证邮箱验证码
        when (val result = validateEmailCode(emailCode)) {
            is ValidationResult.Error -> errors.add(ValidationError("emailCode", result.message))
            is ValidationResult.Success -> {}
        }
        
        // 验证昵称
        when (val result = validateNickname(nickname)) {
            is ValidationResult.Error -> errors.add(ValidationError("nickname", result.message))
            is ValidationResult.Success -> {}
        }
        
        return errors
    }
}

/**
 * 验证结果密封类
 */
sealed class ValidationResult {
    object Success : ValidationResult()
    data class Error(val message: String) : ValidationResult()
}

/**
 * 验证错误数据类
 */
data class ValidationError(
    val field: String,
    val message: String
)
