package com.second.hand.utils

/**
 * 应用常量定义
 * 统一管理应用中使用的常量
 */
object Constants {
    
    // 网络相关
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
    
    // 分页相关
    const val DEFAULT_PAGE_SIZE = 10
    const val INITIAL_PAGE = 1
    
    // 图片相关
    const val MAX_IMAGE_SIZE = 5 * 1024 * 1024 // 5MB
    const val IMAGE_QUALITY = 80
    
    // 缓存相关
    const val CACHE_SIZE = 10 * 1024 * 1024L // 10MB
    
    // 时间相关
    const val TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000L // 5分钟
    
    // 请求头
    const val HEADER_AUTHORIZATION = "Authorization"
    const val HEADER_CONTENT_TYPE = "Content-Type"
    const val HEADER_ACCEPT = "Accept"
    
    // 内容类型
    const val CONTENT_TYPE_JSON = "application/json"
    const val CONTENT_TYPE_FORM = "application/x-www-form-urlencoded"
}
