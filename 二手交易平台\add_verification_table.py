#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在现有数据库中添加邮箱验证码表
"""

import sqlite3
import os

def add_verification_table():
    """在现有数据库中添加邮箱验证码表"""
    db_path = os.path.join('app', 'app.db')
    print(f"更新数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='email_verification_codes';")
        result = cursor.fetchone()
        
        if result:
            print("✓ email_verification_codes 表已存在")
        else:
            print("创建 email_verification_codes 表...")
            
            # 创建邮箱验证码表
            cursor.execute('''
            CREATE TABLE email_verification_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email VARCHAR(120) NOT NULL,
                code VARCHAR(6) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                is_used BOOLEAN DEFAULT 0
            )
            ''')
            
            # 创建索引以提高查询性能
            cursor.execute('''
            CREATE INDEX idx_email_verification_codes_email ON email_verification_codes(email);
            ''')
            
            cursor.execute('''
            CREATE INDEX idx_email_verification_codes_expires_at ON email_verification_codes(expires_at);
            ''')
            
            conn.commit()
            print("✓ email_verification_codes 表创建成功")
        
        # 验证表结构
        cursor.execute("PRAGMA table_info(email_verification_codes);")
        columns = cursor.fetchall()
        print("\n表结构:")
        for col in columns:
            print(f"  {col[1]} {col[2]}")
        
        conn.close()
        print("\n✓ 数据库更新完成")
        
    except Exception as e:
        print(f"✗ 更新数据库时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    add_verification_table()
