#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据一致性验证工具
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
from flask import current_app


class DataConsistencyChecker:
    """数据一致性检查器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.issues = []
    
    def check_all(self) -> Dict[str, Any]:
        """执行所有一致性检查"""
        self.logger.info("开始执行数据一致性检查")
        self.issues = []
        
        results = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'checks': {},
            'summary': {
                'total_checks': 0,
                'passed': 0,
                'failed': 0,
                'warnings': 0
            },
            'issues': []
        }
        
        # 执行各项检查
        checks = [
            ('online_users_consistency', self.check_online_users_consistency),
            ('system_status_consistency', self.check_system_status_consistency),
            ('product_view_counts', self.check_product_view_counts),
            ('database_integrity', self.check_database_integrity)
        ]
        
        for check_name, check_func in checks:
            try:
                self.logger.debug(f"执行检查: {check_name}")
                check_result = check_func()
                results['checks'][check_name] = check_result
                results['summary']['total_checks'] += 1
                
                if check_result['status'] == 'passed':
                    results['summary']['passed'] += 1
                elif check_result['status'] == 'failed':
                    results['summary']['failed'] += 1
                elif check_result['status'] == 'warning':
                    results['summary']['warnings'] += 1
                    
            except Exception as e:
                self.logger.error(f"检查 {check_name} 时发生错误: {str(e)}")
                results['checks'][check_name] = {
                    'status': 'error',
                    'message': f'检查失败: {str(e)}',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                results['summary']['failed'] += 1
        
        results['issues'] = self.issues
        self.logger.info(f"数据一致性检查完成: {results['summary']}")
        return results
    
    def check_online_users_consistency(self) -> Dict[str, Any]:
        """检查在线用户数的一致性"""
        try:
            from app.utils.datetime_utils import get_online_users_count
            
            # 多次获取在线用户数，检查是否一致
            counts = []
            for i in range(3):
                count = get_online_users_count()
                counts.append(count)
            
            # 检查数值是否在合理范围内变化（允许±1的差异）
            max_count = max(counts)
            min_count = min(counts)
            
            if max_count - min_count <= 1:
                return {
                    'status': 'passed',
                    'message': f'在线用户数一致性检查通过，数值范围: {min_count}-{max_count}',
                    'data': {'counts': counts},
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                issue = f"在线用户数变化过大: {counts}"
                self.issues.append(issue)
                return {
                    'status': 'warning',
                    'message': issue,
                    'data': {'counts': counts},
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'在线用户数一致性检查失败: {str(e)}',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def check_system_status_consistency(self) -> Dict[str, Any]:
        """检查系统状态数据的一致性"""
        try:
            from app.utils.system_monitor import get_simplified_system_status
            
            # 获取两次系统状态，检查关键指标
            status1 = get_simplified_system_status()
            status2 = get_simplified_system_status()
            
            # 检查关键字段是否存在
            required_fields = ['cpu_usage', 'memory_usage', 'disk_usage', 'timestamp']
            missing_fields = []
            
            for field in required_fields:
                if field not in status1 or field not in status2:
                    missing_fields.append(field)
            
            if missing_fields:
                issue = f"系统状态缺少必要字段: {missing_fields}"
                self.issues.append(issue)
                return {
                    'status': 'failed',
                    'message': issue,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            
            return {
                'status': 'passed',
                'message': '系统状态数据结构一致性检查通过',
                'data': {
                    'fields_checked': required_fields,
                    'sample_data': {k: status1.get(k) for k in required_fields}
                },
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'系统状态一致性检查失败: {str(e)}',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def check_product_view_counts(self) -> Dict[str, Any]:
        """检查商品浏览量数据的合理性"""
        try:
            from app.models import Product
            
            # 检查是否有异常的浏览量数据
            total_products = Product.query.count()
            if total_products == 0:
                return {
                    'status': 'warning',
                    'message': '系统中没有商品数据',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            
            # 检查浏览量统计
            max_views = Product.query.order_by(Product.view_count.desc()).first()
            avg_views = Product.query.with_entities(
                Product.view_count
            ).all()
            
            if avg_views:
                avg_count = sum(p.view_count for p in avg_views) / len(avg_views)
                
                return {
                    'status': 'passed',
                    'message': '商品浏览量数据检查通过',
                    'data': {
                        'total_products': total_products,
                        'max_views': max_views.view_count if max_views else 0,
                        'avg_views': round(avg_count, 2)
                    },
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                return {
                    'status': 'warning',
                    'message': '无法获取浏览量统计数据',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'商品浏览量检查失败: {str(e)}',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def check_database_integrity(self) -> Dict[str, Any]:
        """检查数据库完整性"""
        try:
            from app.models import User, Product, Order
            from app import db
            
            # 基本连接测试
            db.session.execute("SELECT 1")
            
            # 检查主要表的记录数
            user_count = User.query.count()
            product_count = Product.query.count()
            order_count = Order.query.count()
            
            return {
                'status': 'passed',
                'message': '数据库完整性检查通过',
                'data': {
                    'users': user_count,
                    'products': product_count,
                    'orders': order_count
                },
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'数据库完整性检查失败: {str(e)}',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }


def run_consistency_check() -> Dict[str, Any]:
    """运行数据一致性检查的便捷函数"""
    checker = DataConsistencyChecker()
    return checker.check_all()


def schedule_consistency_checks():
    """安排定期的一致性检查（可以与任务调度器集成）"""
    import threading
    import time
    
    def check_loop():
        while True:
            try:
                result = run_consistency_check()
                if result['summary']['failed'] > 0:
                    logging.getLogger(__name__).warning(
                        f"数据一致性检查发现问题: {result['summary']}"
                    )
                time.sleep(300)  # 每5分钟检查一次
            except Exception as e:
                logging.getLogger(__name__).error(f"定期一致性检查失败: {str(e)}")
                time.sleep(60)  # 出错后1分钟后重试
    
    # 在后台线程中运行
    thread = threading.Thread(target=check_loop, daemon=True)
    thread.start()
    return thread
