#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主页面路由
"""

from flask import render_template, request, current_app, redirect, session
from flask_login import current_user
from app.main import bp
from app.models import Product, Category, ProductStatus, User, Order, Review, OrderStatus, db
from sqlalchemy import desc, func

@bp.route('/')
@bp.route('/index')
def index():
    """首页"""
    # 清除登录成功后的刷新标记
    session.pop('refresh_notifications', None)
    session.pop('refresh_messages', None)

    # 获取最新商品
    page = request.args.get('page', 1, type=int)
    products = Product.query.filter_by(status=ProductStatus.ACTIVE)\
        .order_by(desc(Product.created_at))\
        .paginate(
            page=page,
            per_page=current_app.config['PRODUCTS_PER_PAGE'],
            error_out=False
        )

    # 获取商品分类
    categories = Category.query.filter_by(is_active=True).all()

    # 获取推荐商品（浏览量最高的前8个）
    featured_products = Product.query.filter_by(status=ProductStatus.ACTIVE)\
        .order_by(desc(Product.view_count))\
        .limit(8).all()

    # 获取统计数据
    stats = {
        'active_users': User.query.count(),  # 活跃用户数（总用户数）
        'active_products': Product.query.filter_by(status=ProductStatus.ACTIVE).count(),  # 在售商品数
        'completed_orders': Order.query.filter_by(status=OrderStatus.COMPLETED).count(),  # 成功交易数
        'total_reviews': Review.query.count()  # 用户评价数
    }

    return render_template('main/index.html',
                         title='首页',
                         products=products,
                         categories=categories,
                         featured_products=featured_products,
                         stats=stats)

@bp.route('/search')
def search():
    """搜索页面"""
    query = request.args.get('q', '')
    category_id = request.args.get('category', type=int)
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)
    sort_by = request.args.get('sort', 'newest')
    page = request.args.get('page', 1, type=int)
    
    # 构建查询
    products_query = Product.query.filter_by(status=ProductStatus.ACTIVE)
    
    # 关键词搜索 - 只搜索商品标题
    if query:
        products_query = products_query.filter(
            Product.title.contains(query)
        )
    
    # 分类筛选
    if category_id:
        products_query = products_query.filter_by(category_id=category_id)
    
    # 价格筛选
    if min_price is not None:
        products_query = products_query.filter(Product.price >= min_price)
    if max_price is not None:
        products_query = products_query.filter(Product.price <= max_price)
    
    # 排序
    if sort_by == 'price_asc':
        products_query = products_query.order_by(Product.price.asc())
    elif sort_by == 'price_desc':
        products_query = products_query.order_by(Product.price.desc())
    elif sort_by == 'popular':
        products_query = products_query.order_by(desc(Product.view_count))
    else:  # newest
        products_query = products_query.order_by(desc(Product.created_at))
    
    # 分页
    products = products_query.paginate(
        page=page,
        per_page=current_app.config['PRODUCTS_PER_PAGE'],
        error_out=False
    )
    
    # 获取分类列表
    categories = Category.query.filter_by(is_active=True).all()
    
    return render_template('main/search.html',
                         title='搜索结果',
                         products=products,
                         categories=categories,
                         query=query,
                         category_id=category_id,
                         min_price=min_price,
                         max_price=max_price,
                         sort_by=sort_by)

@bp.route('/about')
def about():
    """关于我们"""
    return render_template('main/about.html', title='关于我们')

@bp.route('/contact', methods=['GET', 'POST'])
def contact():
    """联系我们"""
    from app.notifications.forms import ContactForm
    from app.models import Feedback, db
    from flask_login import current_user
    from flask import flash, redirect, url_for

    form = ContactForm()

    if form.validate_on_submit():
        # 创建反馈记录
        subject_map = {
            'general': '一般咨询',
            'support': '技术支持',
            'business': '商务合作',
            'complaint': '投诉建议',
            'other': '其他'
        }

        feedback = Feedback(
            user_id=current_user.id if current_user.is_authenticated else None,
            title=f"[联系我们] {subject_map.get(form.subject.data, form.subject.data)} - {form.name.data}",
            content=f"联系人：{form.name.data}\n联系邮箱：{form.email.data}\n主题：{subject_map.get(form.subject.data, form.subject.data)}\n\n留言内容：\n{form.message.data}",
            type='contact'  # 新增联系我们类型
        )

        db.session.add(feedback)
        db.session.commit()

        flash('您的消息已发送成功，我们会尽快回复您！', 'success')
        return redirect(url_for('main.contact'))

    return render_template('main/contact.html', title='联系我们', form=form)

@bp.route('/avatar/<int:user_id>')
def generate_avatar(user_id):
    """动态生成用户头像"""
    from app.models import User
    from flask import Response
    import io

    try:
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        # 如果没有PIL，返回SVG头像
        return generate_svg_avatar(user)

    user = User.query.get_or_404(user_id)

    # 创建头像图片
    size = 200
    img = Image.new('RGB', (size, size), user.get_avatar_color())
    draw = ImageDraw.Draw(img)

    # 获取显示字符
    char = user.get_avatar_char()

    # 尝试使用支持中文的系统字体
    font_size = size // 2
    font = None

    # 支持中文的字体列表
    chinese_fonts = [
        # Windows中文字体
        "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
        "C:/Windows/Fonts/simsun.ttc",  # 宋体
        "C:/Windows/Fonts/simhei.ttf",  # 黑体
        # Linux中文字体
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/usr/share/fonts/truetype/arphic/uming.ttc",
        "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
        # macOS中文字体
        "/System/Library/Fonts/PingFang.ttc",
        "/System/Library/Fonts/STHeiti Light.ttc",
        # 英文字体备用
        "arial.ttf",
        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
        "/System/Library/Fonts/Arial.ttf"
    ]

    for font_path in chinese_fonts:
        try:
            font = ImageFont.truetype(font_path, font_size)
            break
        except:
            continue

    # 如果所有字体都失败，使用默认字体
    if font is None:
        try:
            font = ImageFont.load_default()
        except:
            # 最后的备用方案
            font = None

    # 计算文字位置（完美居中）
    if font:
        # 检查是否为中文字符，中文字符需要特殊的偏移处理
        import re
        is_chinese = re.match(r'[\u4e00-\u9fff]', char)
        y_offset = 8 if is_chinese else 0  # 中文字符向上偏移8像素

        try:
            # 使用anchor='mm'实现完美居中（middle-middle）
            draw.text((size//2, size//2 - y_offset), char, fill='white', font=font, anchor='mm')
        except:
            # 如果anchor不支持，使用传统方法
            try:
                # 获取文字边界
                bbox = draw.textbbox((0, 0), char, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                # 计算居中位置，考虑字体的基线偏移和中文偏移
                x = (size - text_width) // 2 - bbox[0]
                y = (size - text_height) // 2 - bbox[1] - y_offset

                # 绘制文字
                draw.text((x, y), char, fill='white', font=font)
            except:
                # 最简单的居中方法
                text_width, text_height = draw.textsize(char, font=font) if hasattr(draw, 'textsize') else (font_size//2, font_size//2)
                x = (size - text_width) // 2
                y = (size - text_height) // 2 - y_offset
                draw.text((x, y), char, fill='white', font=font)
    else:
        # 没有字体时，绘制一个简单的用户图标
        # 绘制头部（圆形）
        head_radius = size // 8
        head_center = (size // 2, size // 3)
        draw.ellipse([
            head_center[0] - head_radius, head_center[1] - head_radius,
            head_center[0] + head_radius, head_center[1] + head_radius
        ], fill='white')

        # 绘制身体（椭圆）
        body_width = size // 4
        body_height = size // 3
        body_center = (size // 2, size * 2 // 3)
        draw.ellipse([
            body_center[0] - body_width, body_center[1] - body_height,
            body_center[0] + body_width, body_center[1] + body_height
        ], fill='white')

    # 保存到内存
    img_io = io.BytesIO()
    img.save(img_io, 'PNG')
    img_io.seek(0)

    return Response(img_io.getvalue(), mimetype='image/png')

def generate_svg_avatar(user):
    """生成SVG格式的头像"""
    from flask import Response
    import re

    char = user.get_avatar_char()
    color = user.get_avatar_color()

    # 检查是否为中文字符，调整Y位置
    is_chinese = re.match(r'[\u4e00-\u9fff]', char)
    y_pos = 105 if is_chinese else 110  # 中文字符稍微向上

    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
    <circle cx="100" cy="100" r="100" fill="{color}"/>
    <text x="100" y="{y_pos}" text-anchor="middle" dominant-baseline="middle"
          font-family="Microsoft YaHei, SimHei, Arial, sans-serif"
          font-size="80" font-weight="bold" fill="white">
        {char}
    </text>
</svg>'''

    return Response(svg_content, mimetype='image/svg+xml')

@bp.route('/test_avatars')
def test_avatars():
    """测试头像显示页面"""
    from app.models import User
    users = User.query.limit(10).all()
    return render_template('main/test_avatars.html', users=users, title='头像测试')

@bp.route('/test_chinese_avatar/<char>')
def test_chinese_avatar(char):
    """测试中文字符头像生成"""
    from flask import Response
    import io

    try:
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        # 返回SVG头像
        return test_chinese_svg_avatar(char)

    # 创建头像图片
    size = 200
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    color = colors[ord(char) % len(colors)]

    img = Image.new('RGB', (size, size), color)
    draw = ImageDraw.Draw(img)

    # 尝试使用支持中文的字体
    font_size = size // 2
    font = None

    chinese_fonts = [
        "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
        "C:/Windows/Fonts/simsun.ttc",  # 宋体
        "C:/Windows/Fonts/simhei.ttf",  # 黑体
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/System/Library/Fonts/PingFang.ttc",
    ]

    for font_path in chinese_fonts:
        try:
            font = ImageFont.truetype(font_path, font_size)
            break
        except:
            continue

    if font is None:
        font = ImageFont.load_default()

    # 绘制文字（完美居中）
    if font:
        try:
            # 使用anchor='mm'实现完美居中（middle-middle）
            draw.text((size//2, size//2), char, fill='white', font=font, anchor='mm')
        except:
            # 如果anchor不支持，使用传统方法
            try:
                bbox = draw.textbbox((0, 0), char, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                # 计算居中位置，考虑字体的基线偏移
                x = (size - text_width) // 2 - bbox[0]
                y = (size - text_height) // 2 - bbox[1]

                draw.text((x, y), char, fill='white', font=font)
            except:
                # 最简单的居中方法
                text_width, text_height = draw.textsize(char, font=font) if hasattr(draw, 'textsize') else (font_size//2, font_size//2)
                x = (size - text_width) // 2
                y = (size - text_height) // 2
                draw.text((x, y), char, fill='white', font=font)

    # 保存到内存
    img_io = io.BytesIO()
    img.save(img_io, 'PNG')
    img_io.seek(0)

    return Response(img_io.getvalue(), mimetype='image/png')

def test_chinese_svg_avatar(char):
    """生成中文字符的SVG头像"""
    from flask import Response

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    color = colors[ord(char) % len(colors)]

    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
    <circle cx="100" cy="100" r="100" fill="{color}"/>
    <text x="100" y="110" text-anchor="middle" dominant-baseline="middle"
          font-family="Microsoft YaHei, SimHei, Arial, sans-serif"
          font-size="80" font-weight="bold" fill="white">
        {char}
    </text>
</svg>'''

    return Response(svg_content, mimetype='image/svg+xml')

@bp.route('/test_center_avatar/<char>/<int:y_offset>')
def test_center_avatar(char, y_offset=0):
    """测试不同Y偏移的头像居中效果"""
    from flask import Response
    import io

    try:
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        return test_center_svg_avatar(char, y_offset)

    # 创建头像图片
    size = 200
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    color = colors[ord(char) % len(colors)]

    img = Image.new('RGB', (size, size), color)
    draw = ImageDraw.Draw(img)

    # 绘制网格线帮助观察居中效果
    draw.line([(size//2, 0), (size//2, size)], fill='rgba(255,255,255,0.3)', width=1)
    draw.line([(0, size//2), (size, size//2)], fill='rgba(255,255,255,0.3)', width=1)

    # 尝试使用支持中文的字体
    font_size = size // 2
    font = None

    chinese_fonts = [
        "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
        "C:/Windows/Fonts/simsun.ttc",  # 宋体
        "C:/Windows/Fonts/simhei.ttf",  # 黑体
    ]

    for font_path in chinese_fonts:
        try:
            font = ImageFont.truetype(font_path, font_size)
            break
        except:
            continue

    if font is None:
        font = ImageFont.load_default()

    # 绘制文字（带Y偏移调整）
    if font:
        try:
            # 使用anchor='mm'并加上偏移
            draw.text((size//2, size//2 + y_offset), char, fill='white', font=font, anchor='mm')
        except:
            # 传统方法
            bbox = draw.textbbox((0, 0), char, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (size - text_width) // 2 - bbox[0]
            y = (size - text_height) // 2 - bbox[1] + y_offset
            draw.text((x, y), char, fill='white', font=font)

    # 保存到内存
    img_io = io.BytesIO()
    img.save(img_io, 'PNG')
    img_io.seek(0)

    return Response(img_io.getvalue(), mimetype='image/png')

def test_center_svg_avatar(char, y_offset=0):
    """生成带Y偏移的SVG头像"""
    from flask import Response

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    color = colors[ord(char) % len(colors)]

    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
    <circle cx="100" cy="100" r="100" fill="{color}"/>
    <!-- 网格线 -->
    <line x1="100" y1="0" x2="100" y2="200" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
    <line x1="0" y1="100" x2="200" y2="100" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
    <!-- 文字 -->
    <text x="100" y="{100 + y_offset}" text-anchor="middle" dominant-baseline="middle"
          font-family="Microsoft YaHei, SimHei, Arial, sans-serif"
          font-size="80" font-weight="bold" fill="white">
        {char}
    </text>
</svg>'''

    return Response(svg_content, mimetype='image/svg+xml')

@bp.route('/test_center')
def test_center():
    """头像居中测试页面"""
    return render_template('main/test_center.html', title='头像居中测试')

@bp.route('/product_default_image/<int:category_id>')
def generate_product_default_image(category_id):
    """动态生成商品分类默认图片"""
    from app.models import Category
    from flask import Response
    import io

    try:
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        # 如果没有PIL，返回SVG图片
        return generate_product_default_svg(category_id)

    category = Category.query.get_or_404(category_id)

    # 图片尺寸 (4:3比例，适合商品展示)
    width, height = 400, 300

    # 创建图片
    img = Image.new('RGB', (width, height), category.get_color())
    draw = ImageDraw.Draw(img)

    # 添加渐变效果 (简单的从上到下渐变)
    base_color = category.get_color()
    # 将颜色转换为RGB
    base_rgb = tuple(int(base_color[i:i+2], 16) for i in (1, 3, 5))

    for y in range(height):
        # 创建渐变效果
        alpha = y / height
        darker_rgb = tuple(int(c * (0.7 + 0.3 * alpha)) for c in base_rgb)
        color_hex = '#{:02x}{:02x}{:02x}'.format(*darker_rgb)
        draw.line([(0, y), (width, y)], fill=color_hex)

    # 绘制图标区域 (圆形背景)
    icon_size = 80
    icon_center_x = width // 2
    icon_center_y = height // 2 - 30

    # 绘制图标背景圆圈
    circle_radius = icon_size // 2
    # 创建半透明白色 (PIL不支持rgba，使用较浅的颜色代替)
    light_color = tuple(int(c * 0.8 + 255 * 0.2) for c in base_rgb)
    light_color_hex = '#{:02x}{:02x}{:02x}'.format(*light_color)

    draw.ellipse([
        icon_center_x - circle_radius, icon_center_y - circle_radius,
        icon_center_x + circle_radius, icon_center_y + circle_radius
    ], fill=light_color_hex, outline='white', width=2)

    # 绘制分类名称
    category_name = category.name

    # 尝试使用中文字体
    font_size = 24
    font = None

    chinese_fonts = [
        "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
        "C:/Windows/Fonts/simsun.ttc",  # 宋体
        "C:/Windows/Fonts/simhei.ttf",  # 黑体
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/System/Library/Fonts/PingFang.ttc",
    ]

    for font_path in chinese_fonts:
        try:
            font = ImageFont.truetype(font_path, font_size)
            break
        except:
            continue

    if font is None:
        font = ImageFont.load_default()

    # 绘制分类名称 (底部居中)
    if font:
        try:
            # 使用anchor='mm'实现居中
            text_y = height - 40
            draw.text((width//2, text_y), category_name, fill='white', font=font, anchor='mm')
        except:
            # 备用方法
            bbox = draw.textbbox((0, 0), category_name, font=font)
            text_width = bbox[2] - bbox[0]
            text_x = (width - text_width) // 2
            text_y = height - 50
            draw.text((text_x, text_y), category_name, fill='white', font=font)

    # 在图标位置绘制一个简单的图标替代 (因为PIL不能直接绘制FontAwesome图标)
    # 根据分类绘制不同的简单图标
    icon_name = category.get_icon()

    if 'laptop' in icon_name or 'desktop' in icon_name:
        # 绘制电脑图标
        screen_w, screen_h = 24, 16
        screen_x = icon_center_x - screen_w // 2
        screen_y = icon_center_y - screen_h // 2 - 5
        draw.rectangle([screen_x, screen_y, screen_x + screen_w, screen_y + screen_h],
                       outline='white', width=2)
        # 底座
        draw.rectangle([screen_x + 8, screen_y + screen_h, screen_x + 16, screen_y + screen_h + 4],
                       fill='white')
    elif 'tshirt' in icon_name or 'male' in icon_name or 'female' in icon_name:
        # 绘制衣服图标
        # T恤形状
        draw.polygon([
            (icon_center_x - 12, icon_center_y - 8),
            (icon_center_x - 8, icon_center_y - 12),
            (icon_center_x + 8, icon_center_y - 12),
            (icon_center_x + 12, icon_center_y - 8),
            (icon_center_x + 8, icon_center_y + 12),
            (icon_center_x - 8, icon_center_y + 12)
        ], outline='white')
    elif 'home' in icon_name or 'couch' in icon_name:
        # 绘制房子图标
        # 屋顶
        draw.polygon([
            (icon_center_x - 15, icon_center_y),
            (icon_center_x, icon_center_y - 12),
            (icon_center_x + 15, icon_center_y)
        ], outline='white')
        # 房子主体
        draw.rectangle([icon_center_x - 12, icon_center_y, icon_center_x + 12, icon_center_y + 12],
                       outline='white', width=2)
        # 门
        draw.rectangle([icon_center_x - 3, icon_center_y + 4, icon_center_x + 3, icon_center_y + 12],
                       outline='white', width=1)
    elif 'book' in icon_name or 'pen' in icon_name:
        # 绘制书本图标
        draw.rectangle([icon_center_x - 10, icon_center_y - 8, icon_center_x + 10, icon_center_y + 8],
                       outline='white', width=2)
        # 书页线条
        for i in range(3):
            y = icon_center_y - 4 + i * 3
            draw.line([icon_center_x - 6, y, icon_center_x + 6, y], fill='white', width=1)
    elif 'running' in icon_name or 'dumbbell' in icon_name:
        # 绘制运动图标 (简单的人形)
        # 头
        draw.ellipse([icon_center_x - 4, icon_center_y - 12, icon_center_x + 4, icon_center_y - 4],
                     outline='white', width=2)
        # 身体
        draw.line([icon_center_x, icon_center_y - 4, icon_center_x, icon_center_y + 8],
                  fill='white', width=3)
        # 手臂
        draw.line([icon_center_x - 8, icon_center_y - 2, icon_center_x + 8, icon_center_y + 2],
                  fill='white', width=2)
        # 腿
        draw.line([icon_center_x, icon_center_y + 8, icon_center_x - 6, icon_center_y + 16],
                  fill='white', width=2)
        draw.line([icon_center_x, icon_center_y + 8, icon_center_x + 6, icon_center_y + 16],
                  fill='white', width=2)
    else:
        # 默认盒子图标
        box_size = 20
        box_x = icon_center_x - box_size // 2
        box_y = icon_center_y - box_size // 2

        # 绘制盒子
        draw.rectangle([box_x, box_y, box_x + box_size, box_y + box_size],
                       outline='white', width=2)
        # 盒子顶部
        draw.polygon([
            (box_x, box_y),
            (box_x + 4, box_y - 4),
            (box_x + box_size + 4, box_y - 4),
            (box_x + box_size, box_y)
        ], outline='white')
        # 盒子侧面
        draw.polygon([
            (box_x + box_size, box_y),
            (box_x + box_size + 4, box_y - 4),
            (box_x + box_size + 4, box_y + box_size - 4),
            (box_x + box_size, box_y + box_size)
        ], outline='white')

    # 保存到内存
    img_io = io.BytesIO()
    img.save(img_io, 'PNG')
    img_io.seek(0)

    return Response(img_io.getvalue(), mimetype='image/png')

def generate_product_default_svg(category_id):
    """生成SVG格式的商品默认图片"""
    from app.models import Category
    from flask import Response

    category = Category.query.get_or_404(category_id)

    icon = category.get_icon()
    color = category.get_color()
    category_name = category.name

    # 创建渐变色
    base_rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
    darker_rgb = tuple(int(c * 0.7) for c in base_rgb)
    darker_color = '#{:02x}{:02x}{:02x}'.format(*darker_rgb)

    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:{color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:{darker_color};stop-opacity:1" />
        </linearGradient>
    </defs>

    <!-- 背景 -->
    <rect width="400" height="300" fill="url(#bgGradient)"/>

    <!-- 图标背景圆圈 -->
    <circle cx="200" cy="120" r="40" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>

    <!-- 简化图标 (根据分类类型) -->
    <g transform="translate(200,120)">
        <!-- 默认盒子图标 -->
        <rect x="-15" y="-15" width="30" height="30" fill="none" stroke="white" stroke-width="2"/>
        <polygon points="-15,-15 -11,-19 19,-19 15,-15" fill="none" stroke="white" stroke-width="1"/>
        <polygon points="15,-15 19,-19 19,11 15,15" fill="none" stroke="white" stroke-width="1"/>
    </g>

    <!-- 分类名称 -->
    <text x="200" y="260" text-anchor="middle" dominant-baseline="middle"
          font-family="Microsoft YaHei, SimHei, Arial, sans-serif"
          font-size="24" font-weight="bold" fill="white">
        {category_name}
    </text>
</svg>'''

    return Response(svg_content, mimetype='image/svg+xml')

@bp.route('/test_product_images')
def test_product_images():
    """测试商品默认图片页面"""
    from app.models import Category
    categories = Category.query.all()
    return render_template('main/test_product_images.html', categories=categories, title='商品默认图片测试')

@bp.route('/help')
def help():
    """帮助中心"""
    return render_template('main/help.html', title='帮助中心')
