package com.second.hand.data.user

import com.second.hand.data.auth.TokenManager
import com.second.hand.data.model.User
import com.second.hand.data.preferences.PreferencesManager
import com.second.hand.data.repository.AuthRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 全局用户状态管理器
 * 统一管理用户登录状态、用户信息和状态变更
 */
@Singleton
class UserStateManager @Inject constructor(
    private val tokenManager: TokenManager,
    private val preferencesManager: PreferencesManager,
    private val authRepository: AuthRepository
) {
    
    // 用户状态流
    private val _userState = MutableStateFlow(UserState())
    val userState: StateFlow<UserState> = _userState.asStateFlow()
    
    // 用户信息流
    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser.asStateFlow()
    
    // 登录状态流
    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()
    
    // 用户状态变更事件流
    private val _userStateEvents = MutableSharedFlow<UserStateEvent>()
    val userStateEvents: SharedFlow<UserStateEvent> = _userStateEvents.asSharedFlow()
    
    private val stateScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    init {
        // 初始化用户状态
        initializeUserState()
        
        // 监听Token状态变化
        observeTokenState()
    }
    
    /**
     * 初始化用户状态
     */
    private fun initializeUserState() {
        stateScope.launch {
            try {
                val isLoggedIn = tokenManager.isLoggedIn()
                val userInfo = tokenManager.getUserInfo()

                _isLoggedIn.value = isLoggedIn

                if (isLoggedIn && userInfo != null) {
                    // 从本地存储恢复用户信息
                    val user = mapUserInfoToUser(userInfo)
                    _currentUser.value = user
                }

                // 立即更新用户状态，不等待网络请求
                updateUserState()

                println("👤 用户状态初始化完成 - 登录状态: $isLoggedIn")

                // 异步同步用户信息（不阻塞初始化）
                if (isLoggedIn) {
                    launch {
                        try {
                            syncUserInfoFromServer()
                        } catch (e: Exception) {
                            println("👤 后台同步用户信息失败: ${e.message}")
                            // 不影响主要功能，只记录错误
                        }
                    }
                }

            } catch (e: Exception) {
                println("👤 用户状态初始化失败: ${e.message}")
                handleStateError(e)
            }
        }
    }
    
    /**
     * 监听Token状态变化
     */
    private fun observeTokenState() {
        stateScope.launch {
            tokenManager.tokenState.collect { tokenState ->
                val wasLoggedIn = _isLoggedIn.value
                val isNowLoggedIn = tokenState.isValid && tokenManager.isLoggedIn()
                
                if (wasLoggedIn != isNowLoggedIn) {
                    _isLoggedIn.value = isNowLoggedIn
                    
                    if (!isNowLoggedIn) {
                        // Token失效，清除用户状态
                        clearUserState()
                        _userStateEvents.emit(UserStateEvent.LoggedOut("Token已失效"))
                    }
                    
                    updateUserState()
                }
            }
        }
    }
    
    /**
     * 用户登录成功
     */
    suspend fun onUserLoggedIn(user: User) {
        try {
            _currentUser.value = user
            _isLoggedIn.value = true
            
            // 保存用户信息到本地
            saveUserInfoToLocal(user)
            
            updateUserState()
            
            _userStateEvents.emit(UserStateEvent.LoggedIn(user))
            
            println("👤 用户登录成功: ${user.username}")
            
        } catch (e: Exception) {
            println("👤 处理用户登录失败: ${e.message}")
            handleStateError(e)
        }
    }
    
    /**
     * 用户登出
     */
    suspend fun onUserLoggedOut(reason: String = "用户主动登出") {
        try {
            val currentUser = _currentUser.value
            
            clearUserState()
            
            _userStateEvents.emit(UserStateEvent.LoggedOut(reason))
            
            println("👤 用户登出: $reason")
            
        } catch (e: Exception) {
            println("👤 处理用户登出失败: ${e.message}")
            handleStateError(e)
        }
    }
    
    /**
     * 更新用户信息
     */
    suspend fun updateUserInfo(user: User) {
        try {
            _currentUser.value = user
            
            // 保存到本地存储
            saveUserInfoToLocal(user)
            
            updateUserState()
            
            _userStateEvents.emit(UserStateEvent.UserInfoUpdated(user))
            
            println("👤 用户信息已更新: ${user.username}")
            
        } catch (e: Exception) {
            println("👤 更新用户信息失败: ${e.message}")
            handleStateError(e)
        }
    }
    
    /**
     * 从服务器同步用户信息
     */
    suspend fun syncUserInfoFromServer() {
        try {
            // 添加超时处理，防止网络请求卡住
            withTimeout(10_000) { // 10秒超时
                when (val result = authRepository.getProfile()) {
                    is com.second.hand.data.api.NetworkResult.Success -> {
                        val user = result.data
                        _currentUser.value = user
                        saveUserInfoToLocal(user)
                        updateUserState()

                        _userStateEvents.emit(UserStateEvent.UserInfoSynced(user))

                        println("👤 用户信息同步成功")
                    }
                    is com.second.hand.data.api.NetworkResult.Error -> {
                        println("👤 用户信息同步失败: ${result.message}")
                        _userStateEvents.emit(UserStateEvent.SyncFailed(result.message))
                    }
                    else -> {
                        // Loading状态，不处理
                    }
                }
            }
        } catch (e: kotlinx.coroutines.TimeoutCancellationException) {
            println("👤 用户信息同步超时")
            _userStateEvents.emit(UserStateEvent.SyncFailed("网络请求超时"))
        } catch (e: Exception) {
            println("👤 用户信息同步异常: ${e.message}")
            _userStateEvents.emit(UserStateEvent.SyncFailed(e.message ?: "同步异常"))
        }
    }
    
    /**
     * 清除用户状态
     */
    private fun clearUserState() {
        _currentUser.value = null
        _isLoggedIn.value = false
        updateUserState()
    }
    
    /**
     * 更新用户状态
     */
    private fun updateUserState() {
        val user = _currentUser.value
        val isLoggedIn = _isLoggedIn.value
        
        _userState.value = UserState(
            isLoggedIn = isLoggedIn,
            user = user,
            isGuest = !isLoggedIn,
            hasProfile = user != null,
            lastSyncTime = System.currentTimeMillis()
        )
    }
    
    /**
     * 保存用户信息到本地
     */
    private fun saveUserInfoToLocal(user: User) {
        val userInfo = mapOf(
            "id" to user.id,
            "username" to user.username,
            "email" to user.email,
            "nickname" to (user.nickname ?: ""),
            "role" to user.role.name,
            "avatar" to (user.avatar ?: ""),
            "phone" to (user.phone ?: ""),
            "bio" to (user.bio ?: "")
        )
        
        // 更新TokenManager中的用户信息
        val accessToken = tokenManager.getAccessToken()
        val refreshToken = tokenManager.getRefreshToken()
        if (!accessToken.isNullOrEmpty() && !refreshToken.isNullOrEmpty()) {
            tokenManager.saveTokens(accessToken, refreshToken, userInfo)
        }
        
        // 保存用户ID到PreferencesManager
        preferencesManager.saveUserId(user.id)
    }
    
    /**
     * 将用户信息Map转换为User对象
     */
    private fun mapUserInfoToUser(userInfo: Map<String, Any>): User {
        return User(
            id = (userInfo["id"] as? Number)?.toInt() ?: 0,
            username = userInfo["username"] as? String ?: "",
            email = userInfo["email"] as? String ?: "",
            nickname = userInfo["nickname"] as? String,
            role = com.second.hand.data.model.enums.UserRole.valueOf(
                userInfo["role"] as? String ?: "USER"
            ),
            avatar = userInfo["avatar"] as? String,
            phone = userInfo["phone"] as? String,
            bio = userInfo["bio"] as? String,
            createdAt = "",
            updatedAt = null
        )
    }
    
    /**
     * 处理状态错误
     */
    private suspend fun handleStateError(error: Throwable) {
        _userStateEvents.emit(UserStateEvent.Error(error.message ?: "未知错误"))
    }
    
    /**
     * 获取当前用户ID
     */
    fun getCurrentUserId(): Int? {
        return _currentUser.value?.id
    }
    
    /**
     * 检查是否为管理员
     */
    fun isAdmin(): Boolean {
        return _currentUser.value?.role == com.second.hand.data.model.enums.UserRole.ADMIN
    }
    
    /**
     * 检查是否有特定权限
     */
    fun hasPermission(permission: String): Boolean {
        val user = _currentUser.value ?: return false
        return when (permission) {
            "admin" -> user.role == com.second.hand.data.model.enums.UserRole.ADMIN
            "user" -> user.role == com.second.hand.data.model.enums.UserRole.USER
            else -> false
        }
    }
}

/**
 * 用户状态数据类
 */
data class UserState(
    val isLoggedIn: Boolean = false,
    val user: User? = null,
    val isGuest: Boolean = true,
    val hasProfile: Boolean = false,
    val lastSyncTime: Long = 0L
)

/**
 * 用户状态事件密封类
 */
sealed class UserStateEvent {
    data class LoggedIn(val user: User) : UserStateEvent()
    data class LoggedOut(val reason: String) : UserStateEvent()
    data class UserInfoUpdated(val user: User) : UserStateEvent()
    data class UserInfoSynced(val user: User) : UserStateEvent()
    data class SyncFailed(val error: String) : UserStateEvent()
    data class Error(val message: String) : UserStateEvent()
}
